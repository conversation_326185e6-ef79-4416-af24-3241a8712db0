"""
Complete Cryptocurrency Prediction System Runner
This script provides a unified interface to train models and generate predictions
"""

import argparse
import logging
import sys
from pathlib import Path
import json
from datetime import datetime

# Import our custom modules
from multi_currency_trainer import MultiCurrencyTrainer
from crypto_predictor import CryptoPricePredictor

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('crypto_system.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def train_models(model_types=None, max_workers=2):
    """Train models for all currencies"""
    print("=" * 60)
    print("CRYPTOCURRENCY PREDICTION SYSTEM - TRAINING MODE")
    print("=" * 60)
    
    if model_types is None:
        model_types = ['random_forest', 'xgboost']
    
    # Initialize trainer
    trainer = MultiCurrencyTrainer()
    
    print(f"Configuration:")
    print(f"  - Symbols to train: {len(trainer.symbols)}")
    print(f"  - Timeframes: {trainer.timeframes}")
    print(f"  - Models: {model_types}")
    print(f"  - Total combinations: {len(trainer.symbols) * len(trainer.timeframes) * len(model_types)}")
    print(f"  - Max workers: {max_workers}")
    
    # Confirm before starting
    response = input("\nDo you want to start training? This may take several hours. (y/N): ")
    if response.lower() != 'y':
        print("Training cancelled.")
        return
    
    print("\nStarting training process...")
    start_time = datetime.now()
    
    # Start training
    results = trainer.train_all_currencies(model_types=model_types, max_workers=max_workers)
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    print(f"\nTraining completed in {duration}")
    print(f"Results saved in: {trainer.results_dir}")
    
    return results

def generate_predictions(timeframes=None, top_n=20, save_results=True):
    """Generate predictions for all currencies"""
    print("=" * 60)
    print("CRYPTOCURRENCY PREDICTION SYSTEM - PREDICTION MODE")
    print("=" * 60)
    
    if timeframes is None:
        timeframes = ['8h', '24h', '48h']
    
    # Initialize predictor
    try:
        predictor = CryptoPricePredictor()
    except Exception as e:
        print(f"Error initializing predictor: {e}")
        print("Make sure you have trained models first using the --train option")
        return
    
    print(f"Loaded models for prediction")
    print(f"Timeframes: {timeframes}")
    
    all_predictions = {}
    
    for timeframe in timeframes:
        print(f"\n{'='*40}")
        print(f"Generating predictions for {timeframe} timeframe...")
        print(f"{'='*40}")
        
        try:
            # Generate predictions
            predictions = predictor.predict_all_currencies(timeframe, min_rise_probability=0.5)
            all_predictions[timeframe] = predictions
            
            # Generate and display report
            report = predictor.generate_prediction_report(timeframe, top_n=top_n)
            print(report)
            
            # Save predictions if requested
            if save_results:
                filename = f"predictions_{timeframe}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                predictor.save_predictions(timeframe, filename)
                print(f"\nPredictions saved to: {filename}")
            
        except Exception as e:
            print(f"Error generating predictions for {timeframe}: {e}")
    
    return all_predictions

def quick_prediction(symbol, timeframe='24h'):
    """Generate quick prediction for a specific symbol"""
    print(f"Quick prediction for {symbol} ({timeframe})")
    
    try:
        predictor = CryptoPricePredictor()
        result = predictor.predict_single_currency(symbol, timeframe)
        
        if 'error' in result:
            print(f"Error: {result['error']}")
            return
        
        ensemble = result['ensemble_prediction']
        print(f"\nPrediction Results for {symbol}:")
        print(f"  Current Price: ${result['current_price']:.4f}")
        print(f"  Rise Probability: {ensemble['rise_probability']:.1%}")
        print(f"  Estimated Rise %: {ensemble['estimated_rise_percentage']:.2f}%")
        print(f"  Will Rise: {'YES' if ensemble['will_rise'] else 'NO'}")
        print(f"  Confidence: {ensemble['confidence']:.1%}")
        
        return result
        
    except Exception as e:
        print(f"Error: {e}")

def show_system_status():
    """Show current system status"""
    print("=" * 60)
    print("CRYPTOCURRENCY PREDICTION SYSTEM - STATUS")
    print("=" * 60)
    
    # Check if symbols file exists
    symbols_file = Path("symbols.txt")
    if symbols_file.exists():
        with open(symbols_file, 'r') as f:
            symbols_count = len([line.strip() for line in f.readlines() if line.strip()])
        print(f"✓ Symbols file found: {symbols_count} symbols")
    else:
        print("✗ Symbols file not found")
    
    # Check for trained models
    models_dir = Path("multi_currency_results")
    if models_dir.exists():
        total_models = 0
        for timeframe in ['8h', '24h', '48h']:
            timeframe_dir = models_dir / timeframe
            if timeframe_dir.exists():
                model_count = len(list(timeframe_dir.glob("*.pkl")))
                total_models += model_count
                print(f"✓ {timeframe} models: {model_count}")
        print(f"Total trained models: {total_models}")
    else:
        print("✗ No trained models found")
    
    # Check for recent predictions
    prediction_files = list(Path(".").glob("predictions_*.json"))
    if prediction_files:
        latest_prediction = max(prediction_files, key=lambda x: x.stat().st_mtime)
        print(f"✓ Latest predictions: {latest_prediction.name}")
    else:
        print("✗ No prediction files found")

def main():
    """Main function with command line interface"""
    setup_logging()
    
    parser = argparse.ArgumentParser(description="Cryptocurrency Prediction System")
    parser.add_argument('--train', action='store_true', help='Train models for all currencies')
    parser.add_argument('--predict', action='store_true', help='Generate predictions')
    parser.add_argument('--quick', type=str, help='Quick prediction for specific symbol (e.g., BTC/USDT)')
    parser.add_argument('--timeframe', type=str, default='24h', choices=['8h', '24h', '48h'], 
                       help='Timeframe for quick prediction')
    parser.add_argument('--models', nargs='+', default=['random_forest', 'xgboost'],
                       help='Model types to train')
    parser.add_argument('--workers', type=int, default=2, help='Number of parallel workers for training')
    parser.add_argument('--top', type=int, default=20, help='Number of top predictions to show')
    parser.add_argument('--status', action='store_true', help='Show system status')
    
    args = parser.parse_args()
    
    # If no arguments provided, show help and status
    if len(sys.argv) == 1:
        show_system_status()
        print("\nUsage examples:")
        print("  python run_crypto_system.py --train                    # Train models")
        print("  python run_crypto_system.py --predict                  # Generate predictions")
        print("  python run_crypto_system.py --quick BTC/USDT          # Quick prediction for BTC")
        print("  python run_crypto_system.py --status                   # Show system status")
        return
    
    if args.status:
        show_system_status()
    
    elif args.train:
        train_models(model_types=args.models, max_workers=args.workers)
    
    elif args.predict:
        generate_predictions(top_n=args.top)
    
    elif args.quick:
        quick_prediction(args.quick, args.timeframe)
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
