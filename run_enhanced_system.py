"""
Enhanced Cryptocurrency Prediction System Runner
Complete system with 4+ years historical data and optimal timeframes
"""

import argparse
import logging
import sys
import os
from pathlib import Path
import json
from datetime import datetime
import time

# Import our enhanced modules
from advanced_data_fetcher import AdvancedDataFetcher
from enhanced_trainer import EnhancedMultiCurrencyTrainer
from crypto_predictor import CryptoPricePredictor

def setup_logging():
    """Setup comprehensive logging"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / 'enhanced_crypto_system.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_system_requirements():
    """Check if all system requirements are met"""
    logger = logging.getLogger(__name__)
    
    print("Checking system requirements...")
    
    # Check Python version
    if sys.version_info < (3, 7):
        logger.error("Python 3.7 or higher is required")
        return False
    
    # Check required files
    required_files = ["symbols.txt"]
    for file in required_files:
        if not Path(file).exists():
            logger.error(f"Required file missing: {file}")
            return False
    
    # Check required modules
    required_modules = [
        "pandas", "numpy", "sklearn", "requests", "tqdm", "xgboost"
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        logger.error(f"Missing required modules: {missing_modules}")
        logger.info("Please install requirements: pip install -r requirements.txt")
        return False
    
    logger.info("[OK] All system requirements met")
    return True

def fetch_historical_data(max_workers=3):
    """Fetch 4+ years of historical data for all currencies"""
    print("=" * 60)
    print("FETCHING HISTORICAL DATA (4+ YEARS)")
    print("=" * 60)
    
    fetcher = AdvancedDataFetcher()
    
    # Load symbols count
    symbols = fetcher._load_symbols()
    print(f"Symbols to fetch: {len(symbols)}")
    print(f"Timeframes: 1h, 4h, 1d")
    print(f"Expected data range: 4+ years per symbol")
    print(f"Max workers: {max_workers}")
    
    # Estimate time
    estimated_time = len(symbols) * 3 * 2 / max_workers  # rough estimate in minutes
    print(f"Estimated time: {estimated_time:.0f} minutes")
    
    response = input(f"\nStart data fetching? This will download large amounts of data. (y/N): ")
    if response.lower() != 'y':
        print("Data fetching cancelled.")
        return False
    
    print("\nStarting data fetch...")
    start_time = datetime.now()
    
    try:
        results = fetcher.fetch_all_symbols_data(max_workers=max_workers)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\nData fetching completed in {duration}")
        print(f"Results saved in: {fetcher.data_dir}")
        
        # Show summary
        successful = len([r for r in results.values() if not any('error' in tf_data for tf_data in r.values() if isinstance(tf_data, dict))])
        print(f"Successfully fetched data for {successful}/{len(symbols)} symbols")
        
        return True
        
    except Exception as e:
        print(f"Data fetching failed: {e}")
        return False

def train_enhanced_models(max_workers=2, selected_models=None):
    """Train enhanced models with 4+ years of data"""
    print("=" * 60)
    print("ENHANCED MODEL TRAINING")
    print("=" * 60)
    
    # Check if historical data exists
    data_dir = Path("historical_data")
    if not data_dir.exists():
        print("Historical data not found. Please fetch data first using --fetch-data")
        return False
    
    # Count available data files
    total_files = 0
    for timeframe in ['1h', '4h', '1d']:
        tf_dir = data_dir / timeframe
        if tf_dir.exists():
            total_files += len(list(tf_dir.glob("*.csv")))
    
    print(f"Historical data files found: {total_files}")
    
    if total_files < 100:  # Minimum threshold
        print("Insufficient historical data. Please fetch data first.")
        return False
    
    # Initialize trainer
    trainer = EnhancedMultiCurrencyTrainer()
    
    if selected_models is None:
        selected_models = ['random_forest_classifier', 'xgboost_classifier', 'random_forest_regressor']
    
    print(f"Selected models: {selected_models}")
    print(f"Prediction timeframes: {trainer.prediction_timeframes}")
    print(f"Data timeframes: {trainer.data_timeframes}")
    
    # Estimate training time
    symbols_count = len(trainer._load_symbols())
    total_tasks = symbols_count * len(trainer.data_timeframes) * len(trainer.prediction_timeframes) * len(selected_models)
    estimated_hours = total_tasks * 0.5 / max_workers / 60  # rough estimate
    
    print(f"Total training tasks: {total_tasks}")
    print(f"Estimated time: {estimated_hours:.1f} hours")
    
    response = input(f"\nStart enhanced training? This will take several hours. (y/N): ")
    if response.lower() != 'y':
        print("Training cancelled.")
        return False
    
    print("\nStarting enhanced training...")
    start_time = datetime.now()
    
    try:
        results = trainer.train_all_models(max_workers=max_workers, selected_models=selected_models)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\nEnhanced training completed in {duration}")
        print(f"Results saved in: {trainer.results_dir}")
        
        return True
        
    except Exception as e:
        print(f"Training failed: {e}")
        return False

def generate_enhanced_predictions(timeframes=None, top_n=25):
    """Generate predictions using enhanced models"""
    print("=" * 60)
    print("ENHANCED PREDICTIONS")
    print("=" * 60)
    
    if timeframes is None:
        timeframes = ['8h', '24h', '48h']
    
    # Check if enhanced models exist
    models_dir = Path("enhanced_results")
    if not models_dir.exists():
        print("Enhanced models not found. Please train models first using --train")
        return False
    
    # Count available models
    total_models = 0
    for timeframe in timeframes:
        tf_dir = models_dir / timeframe
        if tf_dir.exists():
            total_models += len(list(tf_dir.glob("*.pkl")))
    
    print(f"Enhanced models found: {total_models}")
    
    if total_models < 10:
        print("Insufficient trained models. Please train models first.")
        return False
    
    # Initialize predictor (adapt to use enhanced models)
    try:
        predictor = CryptoPricePredictor(models_dir="enhanced_results")
    except Exception as e:
        print(f"Failed to initialize predictor: {e}")
        return False
    
    print(f"Generating predictions for timeframes: {timeframes}")
    
    all_predictions = {}
    
    for timeframe in timeframes:
        print(f"\n{'='*40}")
        print(f"Generating predictions for {timeframe} timeframe...")
        print(f"{'='*40}")
        
        try:
            # Generate predictions
            predictions = predictor.predict_all_currencies(timeframe, min_rise_probability=0.6)
            all_predictions[timeframe] = predictions
            
            # Generate and display report
            report = predictor.generate_prediction_report(timeframe, top_n=top_n)
            print(report)
            
            # Save predictions
            filename = f"enhanced_predictions_{timeframe}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            predictor.save_predictions(timeframe, filename)
            print(f"\nPredictions saved to: {filename}")
            
        except Exception as e:
            print(f"Error generating predictions for {timeframe}: {e}")
    
    return all_predictions

def show_enhanced_status():
    """Show enhanced system status"""
    print("=" * 60)
    print("ENHANCED CRYPTOCURRENCY PREDICTION SYSTEM - STATUS")
    print("=" * 60)
    
    # Check symbols
    symbols_file = Path("symbols.txt")
    if symbols_file.exists():
        with open(symbols_file, 'r') as f:
            symbols_count = len([line.strip() for line in f.readlines() if line.strip()])
        print(f"[OK] Symbols file: {symbols_count} symbols")
    else:
        print("[ERROR] Symbols file not found")
    
    # Check historical data
    data_dir = Path("historical_data")
    if data_dir.exists():
        total_data_files = 0
        for timeframe in ['1h', '4h', '1d']:
            tf_dir = data_dir / timeframe
            if tf_dir.exists():
                count = len(list(tf_dir.glob("*.csv")))
                total_data_files += count
                print(f"[OK] {timeframe} historical data: {count} files")
        print(f"Total historical data files: {total_data_files}")
        
        # Check data summary
        summary_file = data_dir / "data_summary.json"
        if summary_file.exists():
            with open(summary_file, 'r') as f:
                summary = json.load(f)
            print(f"[OK] Data quality: {summary.get('quality_distribution', {}).get('high', 0)} high-quality datasets")
    else:
        print("[ERROR] No historical data found")
    
    # Check enhanced models
    models_dir = Path("enhanced_results")
    if models_dir.exists():
        total_models = 0
        for timeframe in ['8h', '24h', '48h']:
            tf_dir = models_dir / timeframe
            if tf_dir.exists():
                count = len(list(tf_dir.glob("*.pkl")))
                total_models += count
                print(f"[OK] {timeframe} enhanced models: {count}")
        print(f"Total enhanced models: {total_models}")
        
        # Check training summary
        summary_file = models_dir / "enhanced_training_summary.json"
        if summary_file.exists():
            with open(summary_file, 'r') as f:
                summary = json.load(f)
            success_rate = summary.get('successful_tasks', 0) / summary.get('total_tasks', 1) * 100
            print(f"[OK] Training success rate: {success_rate:.1f}%")
    else:
        print("[ERROR] No enhanced models found")
    
    # Check recent predictions
    prediction_files = list(Path(".").glob("enhanced_predictions_*.json"))
    if prediction_files:
        latest_prediction = max(prediction_files, key=lambda x: x.stat().st_mtime)
        print(f"[OK] Latest enhanced predictions: {latest_prediction.name}")
    else:
        print("[ERROR] No enhanced prediction files found")
    
    # System recommendations
    print(f"\n{'='*40}")
    print("SYSTEM RECOMMENDATIONS:")
    print(f"{'='*40}")
    
    if not data_dir.exists():
        print("1. Fetch historical data: --fetch-data")
    elif not models_dir.exists():
        print("1. Train enhanced models: --train")
    elif not prediction_files:
        print("1. Generate predictions: --predict")
    else:
        print("1. System is ready for use!")
        print("2. Update data periodically: --fetch-data")
        print("3. Retrain models monthly: --train")

def validate_enhanced_models():
    """Validate enhanced models performance"""
    print("=" * 60)
    print("ENHANCED MODEL VALIDATION")
    print("=" * 60)
    
    models_dir = Path("enhanced_results")
    if not models_dir.exists():
        print("Enhanced models not found. Please train models first.")
        return False
    
    try:
        from model_validator import ModelValidator
        validator = ModelValidator(models_dir="enhanced_results")
        
        print("Starting enhanced model validation...")
        results = validator.validate_all_models()
        
        if results:
            print("Enhanced model validation completed!")
            print(f"Results saved in: {validator.validation_dir}")
            return True
        else:
            print("No models found for validation.")
            return False
            
    except Exception as e:
        print(f"Validation failed: {e}")
        return False

def main():
    """Main function with enhanced command line interface"""
    setup_logging()
    
    parser = argparse.ArgumentParser(description="Enhanced Cryptocurrency Prediction System")
    parser.add_argument('--fetch-data', action='store_true', help='Fetch 4+ years of historical data')
    parser.add_argument('--train', action='store_true', help='Train enhanced models')
    parser.add_argument('--predict', action='store_true', help='Generate enhanced predictions')
    parser.add_argument('--validate', action='store_true', help='Validate enhanced models')
    parser.add_argument('--status', action='store_true', help='Show enhanced system status')
    parser.add_argument('--full-pipeline', action='store_true', help='Run complete pipeline (fetch + train + predict)')
    
    parser.add_argument('--models', nargs='+', 
                       default=['random_forest_classifier', 'xgboost_classifier', 'random_forest_regressor'],
                       help='Model types to train')
    parser.add_argument('--workers', type=int, default=2, help='Number of parallel workers')
    parser.add_argument('--top', type=int, default=25, help='Number of top predictions to show')
    
    args = parser.parse_args()
    
    # Check system requirements first
    if not check_system_requirements():
        sys.exit(1)
    
    # If no arguments provided, show status and help
    if len(sys.argv) == 1:
        show_enhanced_status()
        print(f"\n{'='*60}")
        print("USAGE EXAMPLES:")
        print(f"{'='*60}")
        print("Complete pipeline:")
        print("  python run_enhanced_system.py --full-pipeline")
        print("")
        print("Step by step:")
        print("  python run_enhanced_system.py --fetch-data     # Fetch 4+ years data")
        print("  python run_enhanced_system.py --train          # Train enhanced models")
        print("  python run_enhanced_system.py --predict        # Generate predictions")
        print("  python run_enhanced_system.py --validate       # Validate models")
        print("")
        print("Status and monitoring:")
        print("  python run_enhanced_system.py --status         # Show system status")
        return
    
    success = True
    
    if args.full_pipeline:
        print("Running complete enhanced pipeline...")
        success &= fetch_historical_data(max_workers=args.workers)
        if success:
            success &= train_enhanced_models(max_workers=args.workers, selected_models=args.models)
        if success:
            generate_enhanced_predictions(top_n=args.top)
        if success:
            validate_enhanced_models()
    
    elif args.fetch_data:
        success = fetch_historical_data(max_workers=args.workers)
    
    elif args.train:
        success = train_enhanced_models(max_workers=args.workers, selected_models=args.models)
    
    elif args.predict:
        generate_enhanced_predictions(top_n=args.top)
    
    elif args.validate:
        validate_enhanced_models()
    
    elif args.status:
        show_enhanced_status()
    
    else:
        parser.print_help()
    
    if success:
        print(f"\n{'='*60}")
        print("OPERATION COMPLETED SUCCESSFULLY!")
        print(f"{'='*60}")
    else:
        print(f"\n{'='*60}")
        print("OPERATION COMPLETED WITH ERRORS")
        print("Check logs for details: logs/enhanced_crypto_system.log")
        print(f"{'='*60}")

if __name__ == "__main__":
    main()
