"""
Model Validation and Performance Analysis System
Validates trained models and analyzes their performance across different timeframes
"""

import logging
import pandas as pd
import numpy as np
from pathlib import Path
import json
import pickle
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
import warnings
warnings.filterwarnings('ignore')

from data_loader import get_dataset
from omegaconf import OmegaConf

logger = logging.getLogger(__name__)

class ModelValidator:
    """
    Validates and analyzes the performance of trained cryptocurrency prediction models
    """
    
    def __init__(self, models_dir: str = "multi_currency_results"):
        self.models_dir = Path(models_dir)
        self.timeframes = ['8h', '24h', '48h']
        self.validation_results = {}
        
        # Create validation results directory
        self.validation_dir = Path("validation_results")
        self.validation_dir.mkdir(exist_ok=True)
    
    def _prepare_features(self, dataset: pd.DataFrame) -> pd.DataFrame:
        """Prepare features (same as training)"""
        df = dataset.copy()
        
        # Basic price features
        df['Price_Change'] = df['Close'].pct_change()
        df['High_Low_Ratio'] = df['High'] / df['Low']
        df['Volume_Change'] = df['Volume'].pct_change()
        
        # Moving averages
        for window in [5, 10, 20, 50]:
            df[f'MA_{window}'] = df['Close'].rolling(window=window).mean()
            df[f'MA_{window}_Ratio'] = df['Close'] / df[f'MA_{window}']
        
        # RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        
        # MACD
        exp1 = df['Close'].ewm(span=12).mean()
        exp2 = df['Close'].ewm(span=26).mean()
        df['MACD'] = exp1 - exp2
        df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
        
        # Bollinger Bands
        df['BB_Middle'] = df['Close'].rolling(window=20).mean()
        bb_std = df['Close'].rolling(window=20).std()
        df['BB_Upper'] = df['BB_Middle'] + (bb_std * 2)
        df['BB_Lower'] = df['BB_Middle'] - (bb_std * 2)
        df['BB_Position'] = (df['Close'] - df['BB_Lower']) / (df['BB_Upper'] - df['BB_Lower'])
        
        # Volume indicators
        df['Volume_MA'] = df['Volume'].rolling(window=20).mean()
        df['Volume_Ratio'] = df['Volume'] / df['Volume_MA']
        
        return df.dropna()
    
    def _create_validation_targets(self, dataset: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """Create validation targets"""
        df = dataset.copy()
        
        hours_map = {'8h': 8, '24h': 24, '48h': 48}
        hours = hours_map[timeframe]
        
        df['Future_Price'] = df['Close'].shift(-hours)
        df['Rise_Percentage'] = ((df['Future_Price'] - df['Close']) / df['Close']) * 100
        df['Will_Rise'] = (df['Rise_Percentage'] >= 2.0).astype(int)
        
        return df.dropna()
    
    def validate_single_model(self, model_path: Path, symbol: str, timeframe: str) -> Dict:
        """Validate a single model"""
        try:
            # Load model
            with open(model_path, 'rb') as f:
                model = pickle.load(f)
            
            # Get validation data (recent data not used in training)
            cfg = OmegaConf.create({
                'dataset_loader': {
                    'name': 'CoinMarket',
                    'train_start_date': (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d %H:%M:%S'),
                    'valid_end_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'window_size': 30,
                    'symbol': symbol.replace('/USDT', '').replace('USDT', ''),
                    'batch_size': 32,
                    'features': ['High', 'Low', 'Close', 'Open', 'Volume']
                }
            })
            
            dataset, _ = get_dataset(
                cfg.dataset_loader.name,
                cfg.dataset_loader.train_start_date,
                cfg.dataset_loader.valid_end_date,
                cfg
            )
            
            # Prepare features and targets
            dataset = self._prepare_features(dataset)
            dataset = self._create_validation_targets(dataset, timeframe)
            
            if len(dataset) < 50:
                return {'error': 'Insufficient validation data'}
            
            # Prepare features for prediction
            feature_cols = [col for col in dataset.columns if col not in ['Date', 'Future_Price', 'Rise_Percentage', 'Will_Rise']]
            X = dataset[feature_cols]
            y_true = dataset['Will_Rise'].values
            
            # Make predictions
            try:
                predictions = model.predict(X)
                if hasattr(predictions, 'shape') and len(predictions.shape) > 1:
                    predictions = predictions.flatten()
                
                # Convert to binary predictions
                y_pred = (predictions > 0.5).astype(int)
                
                # Calculate metrics
                accuracy = accuracy_score(y_true, y_pred)
                precision = precision_score(y_true, y_pred, zero_division=0)
                recall = recall_score(y_true, y_pred, zero_division=0)
                f1 = f1_score(y_true, y_pred, zero_division=0)
                
                # Calculate profit simulation
                profit_results = self._simulate_trading(dataset, predictions)
                
                return {
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'model_path': str(model_path),
                    'validation_samples': len(dataset),
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1_score': f1,
                    'profit_simulation': profit_results,
                    'status': 'success'
                }
                
            except Exception as e:
                return {'error': f'Prediction failed: {str(e)}'}
                
        except Exception as e:
            return {'error': f'Model validation failed: {str(e)}'}
    
    def _simulate_trading(self, dataset: pd.DataFrame, predictions: np.ndarray) -> Dict:
        """Simulate trading based on predictions"""
        initial_balance = 10000  # $10,000 starting balance
        balance = initial_balance
        positions = []
        
        # Simple trading strategy: buy when prediction > 0.7, sell when < 0.3
        buy_threshold = 0.7
        sell_threshold = 0.3
        
        position = 0  # 0 = no position, 1 = long position
        entry_price = 0
        
        for i, (pred, price) in enumerate(zip(predictions, dataset['Close'].values)):
            if position == 0 and pred > buy_threshold:
                # Enter long position
                position = 1
                entry_price = price
                positions.append({'action': 'buy', 'price': price, 'prediction': pred})
                
            elif position == 1 and (pred < sell_threshold or i == len(predictions) - 1):
                # Exit long position
                profit_pct = (price - entry_price) / entry_price
                balance *= (1 + profit_pct)
                position = 0
                positions.append({'action': 'sell', 'price': price, 'prediction': pred, 'profit_pct': profit_pct})
        
        total_return = (balance - initial_balance) / initial_balance
        
        return {
            'initial_balance': initial_balance,
            'final_balance': balance,
            'total_return': total_return,
            'num_trades': len([p for p in positions if p['action'] == 'sell']),
            'positions': positions[-10:]  # Keep last 10 positions for analysis
        }
    
    def validate_all_models(self) -> Dict:
        """Validate all trained models"""
        if not self.models_dir.exists():
            logger.error(f"Models directory {self.models_dir} not found")
            return {}
        
        all_results = {}
        
        for timeframe in self.timeframes:
            timeframe_dir = self.models_dir / timeframe
            if not timeframe_dir.exists():
                continue
            
            logger.info(f"Validating models for {timeframe} timeframe")
            timeframe_results = {}
            
            for model_file in timeframe_dir.glob("*.pkl"):
                # Extract symbol from filename
                filename = model_file.stem
                parts = filename.split('_')
                if len(parts) >= 2:
                    symbol = '_'.join(parts[:-1]).replace('_', '/')
                    
                    logger.info(f"Validating {symbol} for {timeframe}")
                    result = self.validate_single_model(model_file, symbol, timeframe)
                    timeframe_results[symbol] = result
            
            all_results[timeframe] = timeframe_results
        
        # Save validation results
        results_file = self.validation_dir / "validation_results.json"
        with open(results_file, 'w') as f:
            json.dump(all_results, f, indent=2, default=str)
        
        logger.info(f"Validation results saved to {results_file}")
        
        # Generate validation report
        self._generate_validation_report(all_results)
        
        return all_results
    
    def _generate_validation_report(self, results: Dict):
        """Generate comprehensive validation report"""
        report = {
            'validation_date': datetime.now().isoformat(),
            'summary': {},
            'best_performers': {},
            'worst_performers': {},
            'timeframe_analysis': {}
        }
        
        all_successful = []
        
        # Collect all successful validations
        for timeframe, timeframe_results in results.items():
            successful_models = [
                {**result, 'timeframe': timeframe} 
                for result in timeframe_results.values() 
                if result.get('status') == 'success'
            ]
            all_successful.extend(successful_models)
        
        if not all_successful:
            logger.warning("No successful validations found")
            return
        
        # Overall summary
        report['summary'] = {
            'total_models_validated': len(all_successful),
            'avg_accuracy': np.mean([m['accuracy'] for m in all_successful]),
            'avg_precision': np.mean([m['precision'] for m in all_successful]),
            'avg_recall': np.mean([m['recall'] for m in all_successful]),
            'avg_f1_score': np.mean([m['f1_score'] for m in all_successful]),
            'avg_total_return': np.mean([m['profit_simulation']['total_return'] for m in all_successful])
        }
        
        # Best and worst performers
        sorted_by_accuracy = sorted(all_successful, key=lambda x: x['accuracy'], reverse=True)
        sorted_by_return = sorted(all_successful, key=lambda x: x['profit_simulation']['total_return'], reverse=True)
        
        report['best_performers'] = {
            'by_accuracy': sorted_by_accuracy[:10],
            'by_return': sorted_by_return[:10]
        }
        
        report['worst_performers'] = {
            'by_accuracy': sorted_by_accuracy[-5:],
            'by_return': sorted_by_return[-5:]
        }
        
        # Timeframe analysis
        for timeframe in self.timeframes:
            timeframe_models = [m for m in all_successful if m['timeframe'] == timeframe]
            if timeframe_models:
                report['timeframe_analysis'][timeframe] = {
                    'count': len(timeframe_models),
                    'avg_accuracy': np.mean([m['accuracy'] for m in timeframe_models]),
                    'avg_return': np.mean([m['profit_simulation']['total_return'] for m in timeframe_models]),
                    'best_model': max(timeframe_models, key=lambda x: x['accuracy'])['symbol']
                }
        
        # Save report
        report_file = self.validation_dir / "validation_report.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        # Generate text report
        self._generate_text_report(report)
        
        logger.info(f"Validation report saved to {report_file}")
    
    def _generate_text_report(self, report: Dict):
        """Generate human-readable text report"""
        text_report = f"""
=== MODEL VALIDATION REPORT ===
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

OVERALL SUMMARY:
- Total models validated: {report['summary']['total_models_validated']}
- Average accuracy: {report['summary']['avg_accuracy']:.3f}
- Average precision: {report['summary']['avg_precision']:.3f}
- Average recall: {report['summary']['avg_recall']:.3f}
- Average F1 score: {report['summary']['avg_f1_score']:.3f}
- Average return: {report['summary']['avg_total_return']:.2%}

TIMEFRAME ANALYSIS:
"""
        
        for timeframe, analysis in report['timeframe_analysis'].items():
            text_report += f"""
{timeframe} Timeframe:
  - Models: {analysis['count']}
  - Avg Accuracy: {analysis['avg_accuracy']:.3f}
  - Avg Return: {analysis['avg_return']:.2%}
  - Best Model: {analysis['best_model']}
"""
        
        text_report += f"""
TOP 5 PERFORMERS (by accuracy):
"""
        for i, model in enumerate(report['best_performers']['by_accuracy'][:5], 1):
            text_report += f"  {i}. {model['symbol']} ({model['timeframe']}): {model['accuracy']:.3f} accuracy, {model['profit_simulation']['total_return']:.2%} return\n"
        
        text_report += f"""
TOP 5 PERFORMERS (by return):
"""
        for i, model in enumerate(report['best_performers']['by_return'][:5], 1):
            text_report += f"  {i}. {model['symbol']} ({model['timeframe']}): {model['profit_simulation']['total_return']:.2%} return, {model['accuracy']:.3f} accuracy\n"
        
        # Save text report
        text_file = self.validation_dir / "validation_report.txt"
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(text_report)
        
        print(text_report)


def main():
    """Main function for model validation"""
    logging.basicConfig(level=logging.INFO)
    
    print("Starting model validation...")
    validator = ModelValidator()
    
    results = validator.validate_all_models()
    
    if results:
        print("Validation completed successfully!")
        print(f"Results saved in: {validator.validation_dir}")
    else:
        print("No models found for validation. Please train models first.")


if __name__ == "__main__":
    main()
