"""
Environment Setup Script for Multi-Currency Crypto Prediction System
This script helps set up the environment and install required dependencies
"""

import subprocess
import sys
import os
from pathlib import Path
import logging

def setup_logging():
    """Setup logging for the setup process"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def check_python_version():
    """Check if Python version is compatible"""
    logger = logging.getLogger(__name__)
    
    if sys.version_info < (3, 7):
        logger.error("Python 3.7 or higher is required")
        return False
    
    logger.info(f"Python version: {sys.version}")
    return True

def install_requirements():
    """Install required packages"""
    logger = logging.getLogger(__name__)
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        logger.error("requirements.txt file not found")
        return False
    
    try:
        logger.info("Installing required packages...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        logger.info("Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install requirements: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    logger = logging.getLogger(__name__)
    
    directories = [
        "multi_currency_results",
        "multi_currency_results/8h",
        "multi_currency_results/24h", 
        "multi_currency_results/48h",
        "validation_results",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"Created directory: {directory}")

def check_data_files():
    """Check if required data files exist"""
    logger = logging.getLogger(__name__)
    
    required_files = ["symbols.txt"]
    missing_files = []
    
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        logger.warning(f"Missing files: {missing_files}")
        return False
    
    logger.info("All required data files found")
    return True

def test_imports():
    """Test if all required modules can be imported"""
    logger = logging.getLogger(__name__)
    
    required_modules = [
        "pandas", "numpy", "sklearn", "hydra", "omegaconf",
        "matplotlib", "seaborn", "xgboost", "tensorflow"
    ]
    
    failed_imports = []
    
    for module in required_modules:
        try:
            __import__(module)
            logger.info(f"✓ {module}")
        except ImportError:
            failed_imports.append(module)
            logger.error(f"✗ {module}")
    
    if failed_imports:
        logger.error(f"Failed to import: {failed_imports}")
        return False
    
    logger.info("All required modules imported successfully")
    return True

def run_system_check():
    """Run a quick system check"""
    logger = logging.getLogger(__name__)
    
    try:
        # Try to import our custom modules
        sys.path.append(str(Path.cwd()))
        
        logger.info("Testing system components...")
        
        # Test if we can load symbols
        symbols_file = Path("symbols.txt")
        if symbols_file.exists():
            with open(symbols_file, 'r') as f:
                symbols = [line.strip() for line in f.readlines() if line.strip()]
            logger.info(f"Loaded {len(symbols)} cryptocurrency symbols")
        
        logger.info("System check completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"System check failed: {e}")
        return False

def main():
    """Main setup function"""
    logger = setup_logging()
    
    print("=" * 60)
    print("CRYPTOCURRENCY PREDICTION SYSTEM - ENVIRONMENT SETUP")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Install requirements
    if not install_requirements():
        print("\nFailed to install requirements. Please install manually:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    
    # Test imports
    if not test_imports():
        print("\nSome modules failed to import. Please check the installation.")
        sys.exit(1)
    
    # Check data files
    check_data_files()
    
    # Run system check
    if run_system_check():
        print("\n" + "=" * 60)
        print("SETUP COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("\nYou can now use the system:")
        print("1. Check status: python run_crypto_system.py --status")
        print("2. Train models: python run_crypto_system.py --train")
        print("3. Generate predictions: python run_crypto_system.py --predict")
        print("4. Validate models: python model_validator.py")
    else:
        print("\nSetup completed with warnings. Please check the logs.")

if __name__ == "__main__":
    main()
