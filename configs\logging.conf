[loggers]
keys=root,consoleLogger

[handlers]
keys=consoleHandler

[formatters]
keys=simpleFormatter, consoleFormatter

[logger_root]
level=INFO
handlers=consoleHandler

[logger_consoleLogger]
level=INFO
handlers=consoleHandler
qualname=consoleLogger

[handler_consoleHandler]
class=StreamHandler
level=DEBUG
formatter=consoleFormatter
args=(sys.stdout,)

[formatter_simpleFormatter]
format=%(asctime)s - %(name)s - %(levelname)s - %(message)s

[formatter_consoleFormatter]
format=%(name)s - %(levelname)s - %(message)s