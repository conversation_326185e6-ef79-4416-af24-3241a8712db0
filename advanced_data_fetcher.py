"""
Advanced Data Fetcher for Cryptocurrency Historical Data
Fetches at least 4 years of historical data for each currency with optimal timeframes
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import requests
import time
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import concurrent.futures
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class AdvancedDataFetcher:
    """
    Advanced data fetcher that ensures high-quality historical data for training
    """
    
    def __init__(self, data_dir: str = "historical_data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # Create subdirectories for different timeframes
        self.timeframes = {
            '1h': {'binance_interval': '1h', 'min_samples': 35040},  # 4 years * 365 * 24
            '4h': {'binance_interval': '4h', 'min_samples': 8760},   # 4 years * 365 * 6
            '1d': {'binance_interval': '1d', 'min_samples': 1460}    # 4 years * 365
        }
        
        for timeframe in self.timeframes.keys():
            (self.data_dir / timeframe).mkdir(exist_ok=True)
        
        # API endpoints and configurations
        self.binance_base_url = "https://api.binance.com/api/v3/klines"
        self.coinmarketcap_base_url = "https://web-api.coinmarketcap.com/v1/cryptocurrency/ohlcv/historical"
        
        # Rate limiting
        self.request_delay = 0.1  # 100ms between requests
        self.max_retries = 3
        
        # Data quality thresholds
        self.min_years = 4
        self.min_volume_threshold = 1000  # Minimum daily volume in USD
        
    def _load_symbols(self) -> List[str]:
        """Load symbols from symbols.txt"""
        symbols_file = Path("symbols.txt")
        if not symbols_file.exists():
            raise FileNotFoundError("symbols.txt file not found")
        
        with open(symbols_file, 'r') as f:
            symbols = [line.strip() for line in f.readlines() if line.strip()]
        
        logger.info(f"Loaded {len(symbols)} symbols")
        return symbols
    
    def _convert_symbol_format(self, symbol: str, target_format: str = 'binance') -> str:
        """Convert symbol format for different APIs"""
        if target_format == 'binance':
            # Convert BTC/USDT to BTCUSDT
            return symbol.replace('/', '').replace('-', '')
        elif target_format == 'coinmarketcap':
            # Convert to slug format
            base_currency = symbol.split('/')[0].lower()
            return base_currency
        return symbol
    
    def _fetch_binance_data(self, symbol: str, timeframe: str, start_date: datetime, end_date: datetime) -> Optional[pd.DataFrame]:
        """Fetch data from Binance API"""
        try:
            binance_symbol = self._convert_symbol_format(symbol, 'binance')
            interval = self.timeframes[timeframe]['binance_interval']
            
            # Convert dates to timestamps
            start_timestamp = int(start_date.timestamp() * 1000)
            end_timestamp = int(end_date.timestamp() * 1000)
            
            all_data = []
            current_start = start_timestamp
            
            # Fetch data in chunks (Binance limits to 1000 candles per request)
            while current_start < end_timestamp:
                params = {
                    'symbol': binance_symbol,
                    'interval': interval,
                    'startTime': current_start,
                    'endTime': end_timestamp,
                    'limit': 1000
                }
                
                for attempt in range(self.max_retries):
                    try:
                        response = requests.get(self.binance_base_url, params=params, timeout=30)
                        response.raise_for_status()
                        
                        data = response.json()
                        if not data:
                            break
                        
                        all_data.extend(data)
                        
                        # Update start time for next chunk
                        current_start = data[-1][6] + 1  # Close time + 1ms
                        
                        time.sleep(self.request_delay)
                        break
                        
                    except Exception as e:
                        if attempt == self.max_retries - 1:
                            logger.warning(f"Failed to fetch Binance data for {symbol}: {str(e)}")
                            return None
                        time.sleep(1)
            
            if not all_data:
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(all_data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Convert data types and clean
            df['Date'] = pd.to_datetime(df['timestamp'], unit='ms')
            df['Open'] = df['open'].astype(float)
            df['High'] = df['high'].astype(float)
            df['Low'] = df['low'].astype(float)
            df['Close'] = df['close'].astype(float)
            df['Volume'] = df['volume'].astype(float)
            
            # Select relevant columns
            df = df[['Date', 'Open', 'High', 'Low', 'Close', 'Volume']].copy()
            
            # Remove duplicates and sort
            df = df.drop_duplicates(subset=['Date']).sort_values('Date').reset_index(drop=True)
            
            return df
            
        except Exception as e:
            logger.error(f"Error fetching Binance data for {symbol}: {str(e)}")
            return None
    
    def _fetch_coinmarketcap_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """Fetch data from CoinMarketCap API (fallback)"""
        try:
            slug = self._convert_symbol_format(symbol, 'coinmarketcap')
            
            # Calculate date range (4+ years)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=1500)  # ~4.1 years
            
            params = {
                "convert": "USD",
                "slug": slug,
                "time_end": str(int(end_date.timestamp())),
                "time_start": str(int(start_date.timestamp()))
            }
            
            response = requests.get(self.coinmarketcap_base_url, params=params, timeout=30)
            response.raise_for_status()
            
            content = response.json()
            
            if 'data' not in content or 'quotes' not in content['data']:
                return None
            
            df = pd.json_normalize(content['data']['quotes'])
            
            if df.empty:
                return None
            
            # Extract and rename columns
            df['Date'] = pd.to_datetime(df['quote.USD.timestamp']).dt.tz_localize(None)
            df['Low'] = df['quote.USD.low']
            df['High'] = df['quote.USD.high']
            df['Open'] = df['quote.USD.open']
            df['Close'] = df['quote.USD.close']
            df['Volume'] = df['quote.USD.volume']
            
            # Select relevant columns
            df = df[['Date', 'Open', 'High', 'Low', 'Close', 'Volume']].copy()
            
            # Clean data
            df = df.dropna().drop_duplicates(subset=['Date']).sort_values('Date').reset_index(drop=True)
            
            return df
            
        except Exception as e:
            logger.warning(f"CoinMarketCap fallback failed for {symbol}: {str(e)}")
            return None
    
    def _validate_data_quality(self, df: pd.DataFrame, symbol: str, timeframe: str) -> Tuple[bool, Dict]:
        """Validate data quality and completeness"""
        if df is None or df.empty:
            return False, {'error': 'No data available'}
        
        # Check date range
        date_range = (df['Date'].max() - df['Date'].min()).days
        min_days = self.min_years * 365
        
        # Check data completeness
        expected_samples = self.timeframes[timeframe]['min_samples']
        actual_samples = len(df)
        completeness_ratio = actual_samples / expected_samples
        
        # Check for gaps in data
        if timeframe == '1h':
            expected_freq = '1H'
        elif timeframe == '4h':
            expected_freq = '4H'
        else:
            expected_freq = '1D'
        
        # Generate expected date range
        full_range = pd.date_range(start=df['Date'].min(), end=df['Date'].max(), freq=expected_freq)
        missing_dates = len(full_range) - len(df)
        gap_ratio = missing_dates / len(full_range) if len(full_range) > 0 else 1
        
        # Check volume quality
        avg_volume = df['Volume'].mean()
        volume_quality = avg_volume >= self.min_volume_threshold
        
        # Overall quality score
        quality_score = (
            (date_range >= min_days) * 0.3 +
            (completeness_ratio >= 0.8) * 0.3 +
            (gap_ratio <= 0.1) * 0.2 +
            volume_quality * 0.2
        )
        
        quality_info = {
            'date_range_days': date_range,
            'min_required_days': min_days,
            'actual_samples': actual_samples,
            'expected_samples': expected_samples,
            'completeness_ratio': completeness_ratio,
            'gap_ratio': gap_ratio,
            'avg_volume': avg_volume,
            'quality_score': quality_score,
            'is_high_quality': quality_score >= 0.7
        }
        
        return quality_score >= 0.7, quality_info
    
    def _enhance_data_with_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add technical indicators and features to improve training"""
        df = df.copy()
        
        # Basic price features
        df['Price_Change'] = df['Close'].pct_change()
        df['High_Low_Ratio'] = df['High'] / df['Low']
        df['Volume_Change'] = df['Volume'].pct_change()
        df['Price_Range'] = (df['High'] - df['Low']) / df['Close']
        
        # Moving averages (multiple timeframes)
        for window in [7, 14, 21, 50, 100, 200]:
            df[f'MA_{window}'] = df['Close'].rolling(window=window).mean()
            df[f'MA_{window}_Ratio'] = df['Close'] / df[f'MA_{window}']
            df[f'MA_{window}_Slope'] = df[f'MA_{window}'].diff(5) / df[f'MA_{window}']
        
        # Exponential moving averages
        for span in [12, 26, 50]:
            df[f'EMA_{span}'] = df['Close'].ewm(span=span).mean()
            df[f'EMA_{span}_Ratio'] = df['Close'] / df[f'EMA_{span}']
        
        # RSI (multiple periods)
        for period in [14, 21, 30]:
            delta = df['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            df[f'RSI_{period}'] = 100 - (100 / (1 + rs))
        
        # MACD
        exp1 = df['Close'].ewm(span=12).mean()
        exp2 = df['Close'].ewm(span=26).mean()
        df['MACD'] = exp1 - exp2
        df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
        df['MACD_Histogram'] = df['MACD'] - df['MACD_Signal']
        
        # Bollinger Bands (multiple periods)
        for period in [20, 50]:
            df[f'BB_Middle_{period}'] = df['Close'].rolling(window=period).mean()
            bb_std = df['Close'].rolling(window=period).std()
            df[f'BB_Upper_{period}'] = df[f'BB_Middle_{period}'] + (bb_std * 2)
            df[f'BB_Lower_{period}'] = df[f'BB_Middle_{period}'] - (bb_std * 2)
            df[f'BB_Position_{period}'] = (df['Close'] - df[f'BB_Lower_{period}']) / (df[f'BB_Upper_{period}'] - df[f'BB_Lower_{period}'])
            df[f'BB_Width_{period}'] = (df[f'BB_Upper_{period}'] - df[f'BB_Lower_{period}']) / df[f'BB_Middle_{period}']
        
        # Volume indicators
        for window in [10, 20, 50]:
            df[f'Volume_MA_{window}'] = df['Volume'].rolling(window=window).mean()
            df[f'Volume_Ratio_{window}'] = df['Volume'] / df[f'Volume_MA_{window}']
        
        # Price momentum
        for period in [5, 10, 20]:
            df[f'Momentum_{period}'] = df['Close'] / df['Close'].shift(period) - 1
        
        # Volatility
        for window in [10, 20, 30]:
            df[f'Volatility_{window}'] = df['Close'].rolling(window=window).std() / df['Close'].rolling(window=window).mean()
        
        # Support and resistance levels
        df['Support'] = df['Low'].rolling(window=20).min()
        df['Resistance'] = df['High'].rolling(window=20).max()
        df['Support_Distance'] = (df['Close'] - df['Support']) / df['Close']
        df['Resistance_Distance'] = (df['Resistance'] - df['Close']) / df['Close']
        
        return df
    
    def fetch_symbol_data(self, symbol: str, timeframes: List[str] = None) -> Dict:
        """Fetch high-quality data for a single symbol"""
        if timeframes is None:
            timeframes = ['1h', '4h', '1d']
        
        results = {}
        
        for timeframe in timeframes:
            logger.info(f"Fetching {timeframe} data for {symbol}")
            
            # Check if data already exists and is recent
            cache_file = self.data_dir / timeframe / f"{symbol.replace('/', '_')}.csv"
            if cache_file.exists():
                try:
                    cached_df = pd.read_csv(cache_file)
                    cached_df['Date'] = pd.to_datetime(cached_df['Date'])
                    
                    # Check if cached data is recent (within last 24 hours)
                    if not cached_df.empty:
                        last_date = cached_df['Date'].max()
                        if (datetime.now() - last_date).days < 1:
                            is_valid, quality_info = self._validate_data_quality(cached_df, symbol, timeframe)
                            if is_valid:
                                logger.info(f"Using cached data for {symbol} {timeframe}")
                                results[timeframe] = {
                                    'data': cached_df,
                                    'quality_info': quality_info,
                                    'source': 'cache'
                                }
                                continue
                except Exception as e:
                    logger.warning(f"Failed to load cached data for {symbol}: {e}")
            
            # Calculate date range for fetching
            end_date = datetime.now()
            start_date = end_date - timedelta(days=1500)  # ~4.1 years
            
            # Try Binance first
            df = self._fetch_binance_data(symbol, timeframe, start_date, end_date)
            source = 'binance'
            
            # Fallback to CoinMarketCap if Binance fails (only for daily data)
            if df is None and timeframe == '1d':
                df = self._fetch_coinmarketcap_data(symbol)
                source = 'coinmarketcap'
            
            if df is None:
                results[timeframe] = {
                    'error': f'Failed to fetch data from all sources',
                    'source': 'none'
                }
                continue
            
            # Validate data quality
            is_valid, quality_info = self._validate_data_quality(df, symbol, timeframe)
            
            if not is_valid:
                logger.warning(f"Low quality data for {symbol} {timeframe}: {quality_info}")
            
            # Enhance data with technical features
            enhanced_df = self._enhance_data_with_features(df)
            
            # Save to cache
            try:
                enhanced_df.to_csv(cache_file, index=False)
                logger.info(f"Cached data for {symbol} {timeframe}")
            except Exception as e:
                logger.warning(f"Failed to cache data for {symbol}: {e}")
            
            results[timeframe] = {
                'data': enhanced_df,
                'quality_info': quality_info,
                'source': source,
                'is_high_quality': is_valid
            }
        
        return results
    
    def fetch_all_symbols_data(self, max_workers: int = 5) -> Dict:
        """Fetch data for all symbols with parallel processing"""
        symbols = self._load_symbols()
        all_results = {}
        
        logger.info(f"Starting data fetch for {len(symbols)} symbols")
        
        # Create progress tracking
        progress_file = self.data_dir / "fetch_progress.json"
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_symbol = {
                executor.submit(self.fetch_symbol_data, symbol): symbol
                for symbol in symbols
            }
            
            # Process completed tasks
            completed = 0
            for future in tqdm(concurrent.futures.as_completed(future_to_symbol), 
                             total=len(symbols), desc="Fetching data"):
                symbol = future_to_symbol[future]
                
                try:
                    result = future.result()
                    all_results[symbol] = result
                    
                    completed += 1
                    
                    # Save progress periodically
                    if completed % 10 == 0:
                        with open(progress_file, 'w') as f:
                            json.dump({
                                'completed': completed,
                                'total': len(symbols),
                                'timestamp': datetime.now().isoformat()
                            }, f)
                    
                except Exception as e:
                    logger.error(f"Failed to fetch data for {symbol}: {str(e)}")
                    all_results[symbol] = {'error': str(e)}
        
        # Generate summary report
        self._generate_data_summary(all_results)
        
        return all_results
    
    def _generate_data_summary(self, results: Dict):
        """Generate data quality summary report"""
        summary = {
            'fetch_date': datetime.now().isoformat(),
            'total_symbols': len(results),
            'successful_symbols': 0,
            'failed_symbols': 0,
            'timeframe_stats': {},
            'quality_distribution': {'high': 0, 'medium': 0, 'low': 0},
            'data_sources': {'binance': 0, 'coinmarketcap': 0, 'cache': 0},
            'top_quality_symbols': [],
            'failed_symbols': []
        }
        
        for symbol, symbol_results in results.items():
            if 'error' in symbol_results:
                summary['failed_symbols'].append(symbol)
                summary['failed_symbols'] += 1
                continue
            
            summary['successful_symbols'] += 1
            symbol_quality_scores = []
            
            for timeframe, timeframe_data in symbol_results.items():
                if 'error' in timeframe_data:
                    continue
                
                # Update timeframe stats
                if timeframe not in summary['timeframe_stats']:
                    summary['timeframe_stats'][timeframe] = {
                        'successful': 0,
                        'avg_quality_score': 0,
                        'avg_samples': 0
                    }
                
                quality_info = timeframe_data.get('quality_info', {})
                quality_score = quality_info.get('quality_score', 0)
                samples = quality_info.get('actual_samples', 0)
                
                summary['timeframe_stats'][timeframe]['successful'] += 1
                summary['timeframe_stats'][timeframe]['avg_quality_score'] += quality_score
                summary['timeframe_stats'][timeframe]['avg_samples'] += samples
                
                symbol_quality_scores.append(quality_score)
                
                # Update source stats
                source = timeframe_data.get('source', 'unknown')
                if source in summary['data_sources']:
                    summary['data_sources'][source] += 1
            
            # Calculate average quality for symbol
            if symbol_quality_scores:
                avg_quality = np.mean(symbol_quality_scores)
                
                if avg_quality >= 0.8:
                    summary['quality_distribution']['high'] += 1
                elif avg_quality >= 0.6:
                    summary['quality_distribution']['medium'] += 1
                else:
                    summary['quality_distribution']['low'] += 1
                
                summary['top_quality_symbols'].append({
                    'symbol': symbol,
                    'avg_quality_score': avg_quality
                })
        
        # Calculate averages
        for timeframe_stats in summary['timeframe_stats'].values():
            if timeframe_stats['successful'] > 0:
                timeframe_stats['avg_quality_score'] /= timeframe_stats['successful']
                timeframe_stats['avg_samples'] /= timeframe_stats['successful']
        
        # Sort top quality symbols
        summary['top_quality_symbols'].sort(key=lambda x: x['avg_quality_score'], reverse=True)
        summary['top_quality_symbols'] = summary['top_quality_symbols'][:20]
        
        # Save summary
        summary_file = self.data_dir / "data_summary.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        # Generate text report
        self._generate_text_summary(summary)
        
        logger.info(f"Data summary saved to {summary_file}")
    
    def _generate_text_summary(self, summary: Dict):
        """Generate human-readable summary"""
        text_summary = f"""
=== DATA FETCH SUMMARY ===
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

OVERALL STATISTICS:
- Total symbols processed: {summary['total_symbols']}
- Successful: {summary['successful_symbols']}
- Failed: {summary['failed_symbols']}
- Success rate: {summary['successful_symbols']/summary['total_symbols']*100:.1f}%

QUALITY DISTRIBUTION:
- High quality (≥80%): {summary['quality_distribution']['high']} symbols
- Medium quality (60-80%): {summary['quality_distribution']['medium']} symbols
- Low quality (<60%): {summary['quality_distribution']['low']} symbols

TIMEFRAME STATISTICS:
"""
        
        for timeframe, stats in summary['timeframe_stats'].items():
            text_summary += f"""
{timeframe.upper()} Timeframe:
  - Successful fetches: {stats['successful']}
  - Average quality score: {stats['avg_quality_score']:.3f}
  - Average samples: {stats['avg_samples']:.0f}
"""
        
        text_summary += f"""
DATA SOURCES:
- Binance: {summary['data_sources']['binance']} datasets
- CoinMarketCap: {summary['data_sources']['coinmarketcap']} datasets
- Cache: {summary['data_sources']['cache']} datasets

TOP 10 QUALITY SYMBOLS:
"""
        
        for i, symbol_info in enumerate(summary['top_quality_symbols'][:10], 1):
            text_summary += f"  {i}. {symbol_info['symbol']}: {symbol_info['avg_quality_score']:.3f}\n"
        
        if summary['failed_symbols']:
            text_summary += f"\nFAILED SYMBOLS ({len(summary['failed_symbols'])}):\n"
            for symbol in summary['failed_symbols'][:10]:
                text_summary += f"  - {symbol}\n"
            if len(summary['failed_symbols']) > 10:
                text_summary += f"  ... and {len(summary['failed_symbols']) - 10} more\n"
        
        # Save text summary
        text_file = self.data_dir / "data_summary.txt"
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(text_summary)
        
        print(text_summary)


def main():
    """Main function for data fetching"""
    logging.basicConfig(level=logging.INFO)
    
    print("Starting advanced data fetching...")
    print("This will fetch at least 4 years of historical data for all symbols")
    
    fetcher = AdvancedDataFetcher()
    
    # Fetch all data
    results = fetcher.fetch_all_symbols_data(max_workers=3)
    
    print("Data fetching completed!")
    print(f"Results saved in: {fetcher.data_dir}")


if __name__ == "__main__":
    main()
