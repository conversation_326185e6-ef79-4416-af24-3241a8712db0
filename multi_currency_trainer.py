"""
Multi-Currency Training System for CryptoPredictions
This script trains models on all currencies listed in symbols.txt for multiple timeframes
"""

import logging
import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import concurrent.futures
from pathlib import Path
import json
import pickle
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

import hydra
from omegaconf import DictConfig, OmegaConf
from models import MODELS
from data_loader import get_dataset
from factory.trainer import Trainer
from factory.evaluator import Evaluator
from factory.profit_calculator import ProfitCalculator
from sklearn.model_selection import TimeSeriesSplit
from path_definition import HYDRA_PATH
from utils.reporter import Reporter
from data_loader.creator import create_dataset, preprocess

logger = logging.getLogger(__name__)

class MultiCurrencyTrainer:
    """
    Handles training of multiple cryptocurrency prediction models
    for different timeframes (8h, 24h, 48h)
    """
    
    def __init__(self, config_path: str = None):
        self.symbols = self._load_symbols()
        self.timeframes = ['8h', '24h', '48h']
        self.models_results = {}
        self.config_path = config_path or HYDRA_PATH
        
        # Create directories for results
        self.results_dir = Path("multi_currency_results")
        self.results_dir.mkdir(exist_ok=True)
        
        for timeframe in self.timeframes:
            (self.results_dir / timeframe).mkdir(exist_ok=True)
    
    def _load_symbols(self) -> List[str]:
        """Load cryptocurrency symbols from symbols.txt"""
        symbols_file = Path("symbols.txt")
        if not symbols_file.exists():
            raise FileNotFoundError("symbols.txt file not found")
        
        with open(symbols_file, 'r') as f:
            symbols = [line.strip() for line in f.readlines() if line.strip()]
        
        logger.info(f"Loaded {len(symbols)} symbols from symbols.txt")
        return symbols
    
    def _create_prediction_targets(self, dataset: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """
        Create prediction targets for rise detection and percentage calculation
        
        Args:
            dataset: Input dataset with OHLCV data
            timeframe: Target timeframe ('8h', '24h', '48h')
        
        Returns:
            Dataset with prediction targets
        """
        df = dataset.copy()
        
        # Convert timeframe to hours
        hours_map = {'8h': 8, '24h': 24, '48h': 48}
        hours = hours_map[timeframe]
        
        # Calculate future price (assuming hourly data)
        df['Future_Price'] = df['Close'].shift(-hours)
        
        # Calculate rise percentage
        df['Rise_Percentage'] = ((df['Future_Price'] - df['Close']) / df['Close']) * 100
        
        # Binary target: 1 if price rises by at least 2%, 0 otherwise
        df['Will_Rise'] = (df['Rise_Percentage'] >= 2.0).astype(int)
        
        # Remove rows with NaN values
        df = df.dropna()
        
        return df
    
    def _prepare_features(self, dataset: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare technical indicators and features for training
        """
        df = dataset.copy()
        
        # Basic price features
        df['Price_Change'] = df['Close'].pct_change()
        df['High_Low_Ratio'] = df['High'] / df['Low']
        df['Volume_Change'] = df['Volume'].pct_change()
        
        # Moving averages
        for window in [5, 10, 20, 50]:
            df[f'MA_{window}'] = df['Close'].rolling(window=window).mean()
            df[f'MA_{window}_Ratio'] = df['Close'] / df[f'MA_{window}']
        
        # RSI (Relative Strength Index)
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        
        # MACD
        exp1 = df['Close'].ewm(span=12).mean()
        exp2 = df['Close'].ewm(span=26).mean()
        df['MACD'] = exp1 - exp2
        df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
        
        # Bollinger Bands
        df['BB_Middle'] = df['Close'].rolling(window=20).mean()
        bb_std = df['Close'].rolling(window=20).std()
        df['BB_Upper'] = df['BB_Middle'] + (bb_std * 2)
        df['BB_Lower'] = df['BB_Middle'] - (bb_std * 2)
        df['BB_Position'] = (df['Close'] - df['BB_Lower']) / (df['BB_Upper'] - df['BB_Lower'])
        
        # Volume indicators
        df['Volume_MA'] = df['Volume'].rolling(window=20).mean()
        df['Volume_Ratio'] = df['Volume'] / df['Volume_MA']
        
        # Remove NaN values
        df = df.dropna()
        
        return df
    
    def train_single_currency(self, symbol: str, timeframe: str, model_type: str = 'random_forest') -> Dict:
        """
        Train a model for a single currency and timeframe
        
        Args:
            symbol: Cryptocurrency symbol (e.g., 'BTC/USDT')
            timeframe: Target timeframe ('8h', '24h', '48h')
            model_type: Type of model to use
        
        Returns:
            Dictionary with training results
        """
        try:
            logger.info(f"Training {symbol} for {timeframe} timeframe using {model_type}")
            
            # Create temporary config for this symbol
            cfg = OmegaConf.create({
                'model': {'type': model_type},
                'dataset_loader': {
                    'name': 'CoinMarket',
                    'train_start_date': '2020-01-01 00:00:00',
                    'valid_end_date': '2024-01-01 00:00:00',
                    'window_size': 30,
                    'symbol': symbol.replace('/USDT', '').replace('USDT', ''),
                    'batch_size': 32,
                    'features': ['High', 'Low', 'Close', 'Open', 'Volume']
                },
                'validation_method': 'simple',
                'save_dir': str(self.results_dir / timeframe / symbol.replace('/', '_'))
            })
            
            # Try to load data (this might fail for some symbols)
            try:
                dataset, profit_calculator = get_dataset(
                    cfg.dataset_loader.name,
                    cfg.dataset_loader.train_start_date,
                    cfg.dataset_loader.valid_end_date,
                    cfg
                )
            except Exception as e:
                logger.warning(f"Failed to load data for {symbol}: {str(e)}")
                return {'symbol': symbol, 'timeframe': timeframe, 'status': 'failed', 'error': str(e)}
            
            # Prepare features and targets
            dataset = self._prepare_features(dataset)
            dataset = self._create_prediction_targets(dataset, timeframe)
            
            if len(dataset) < 100:  # Minimum data requirement
                logger.warning(f"Insufficient data for {symbol}: {len(dataset)} rows")
                return {'symbol': symbol, 'timeframe': timeframe, 'status': 'insufficient_data'}
            
            # Initialize model
            model = MODELS[model_type](cfg.model)
            
            # Split data
            split_idx = int(len(dataset) * 0.8)
            train_data = dataset[:split_idx]
            test_data = dataset[split_idx:]
            
            # Train model
            trainer = Trainer(cfg)
            trained_model = trainer.train(model, train_data)
            
            # Evaluate model
            evaluator = Evaluator(cfg)
            metrics = evaluator.evaluate(trained_model, test_data)
            
            # Calculate prediction accuracy for rise detection
            predictions = trained_model.predict(test_data)
            
            # Calculate rise prediction accuracy
            if 'Will_Rise' in test_data.columns:
                actual_rises = test_data['Will_Rise'].values
                predicted_rises = (predictions > 0.5).astype(int)  # Assuming binary classification
                rise_accuracy = np.mean(actual_rises == predicted_rises)
            else:
                rise_accuracy = 0.0
            
            # Save model
            model_path = self.results_dir / timeframe / f"{symbol.replace('/', '_')}_{model_type}.pkl"
            with open(model_path, 'wb') as f:
                pickle.dump(trained_model, f)
            
            result = {
                'symbol': symbol,
                'timeframe': timeframe,
                'model_type': model_type,
                'status': 'success',
                'rise_accuracy': rise_accuracy,
                'metrics': metrics,
                'model_path': str(model_path),
                'data_points': len(dataset)
            }
            
            logger.info(f"Successfully trained {symbol} for {timeframe}: Rise Accuracy = {rise_accuracy:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"Error training {symbol} for {timeframe}: {str(e)}")
            return {'symbol': symbol, 'timeframe': timeframe, 'status': 'error', 'error': str(e)}
    
    def train_all_currencies(self, model_types: List[str] = None, max_workers: int = 4) -> Dict:
        """
        Train models for all currencies and timeframes
        
        Args:
            model_types: List of model types to use
            max_workers: Maximum number of parallel workers
        
        Returns:
            Dictionary with all training results
        """
        if model_types is None:
            model_types = ['random_forest', 'xgboost', 'lstm']
        
        all_results = {}
        
        # Create tasks for all combinations
        tasks = []
        for symbol in self.symbols:
            for timeframe in self.timeframes:
                for model_type in model_types:
                    tasks.append((symbol, timeframe, model_type))
        
        logger.info(f"Starting training for {len(tasks)} tasks with {max_workers} workers")
        
        # Execute tasks in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_task = {
                executor.submit(self.train_single_currency, symbol, timeframe, model_type): (symbol, timeframe, model_type)
                for symbol, timeframe, model_type in tasks
            }
            
            completed = 0
            for future in concurrent.futures.as_completed(future_to_task):
                symbol, timeframe, model_type = future_to_task[future]
                try:
                    result = future.result()
                    
                    # Store result
                    key = f"{symbol}_{timeframe}_{model_type}"
                    all_results[key] = result
                    
                    completed += 1
                    if completed % 10 == 0:
                        logger.info(f"Completed {completed}/{len(tasks)} tasks")
                        
                except Exception as e:
                    logger.error(f"Task failed for {symbol}_{timeframe}_{model_type}: {str(e)}")
                    all_results[f"{symbol}_{timeframe}_{model_type}"] = {
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'model_type': model_type,
                        'status': 'failed',
                        'error': str(e)
                    }
        
        # Save results
        results_file = self.results_dir / "training_results.json"
        with open(results_file, 'w') as f:
            json.dump(all_results, f, indent=2, default=str)
        
        logger.info(f"Training completed. Results saved to {results_file}")
        
        # Generate summary
        self._generate_summary(all_results)
        
        return all_results
    
    def _generate_summary(self, results: Dict) -> None:
        """Generate training summary report"""
        summary = {
            'total_tasks': len(results),
            'successful': len([r for r in results.values() if r.get('status') == 'success']),
            'failed': len([r for r in results.values() if r.get('status') == 'failed']),
            'errors': len([r for r in results.values() if r.get('status') == 'error']),
            'insufficient_data': len([r for r in results.values() if r.get('status') == 'insufficient_data']),
            'by_timeframe': {},
            'by_model': {},
            'top_performers': []
        }
        
        # Analyze by timeframe
        for timeframe in self.timeframes:
            timeframe_results = [r for r in results.values() if r.get('timeframe') == timeframe and r.get('status') == 'success']
            if timeframe_results:
                avg_accuracy = np.mean([r.get('rise_accuracy', 0) for r in timeframe_results])
                summary['by_timeframe'][timeframe] = {
                    'count': len(timeframe_results),
                    'avg_rise_accuracy': avg_accuracy
                }
        
        # Find top performers
        successful_results = [r for r in results.values() if r.get('status') == 'success']
        if successful_results:
            top_performers = sorted(successful_results, key=lambda x: x.get('rise_accuracy', 0), reverse=True)[:20]
            summary['top_performers'] = [
                {
                    'symbol': r['symbol'],
                    'timeframe': r['timeframe'],
                    'model_type': r['model_type'],
                    'rise_accuracy': r.get('rise_accuracy', 0)
                }
                for r in top_performers
            ]
        
        # Save summary
        summary_file = self.results_dir / "training_summary.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        logger.info(f"Training summary saved to {summary_file}")
        print(f"\n=== Training Summary ===")
        print(f"Total tasks: {summary['total_tasks']}")
        print(f"Successful: {summary['successful']}")
        print(f"Failed: {summary['failed']}")
        print(f"Errors: {summary['errors']}")
        print(f"Insufficient data: {summary['insufficient_data']}")
        
        if summary['top_performers']:
            print(f"\nTop 10 Performers:")
            for i, performer in enumerate(summary['top_performers'][:10], 1):
                print(f"{i}. {performer['symbol']} ({performer['timeframe']}, {performer['model_type']}): {performer['rise_accuracy']:.3f}")


def main():
    """Main function to run multi-currency training"""
    logging.basicConfig(level=logging.INFO)
    
    # Initialize trainer
    trainer = MultiCurrencyTrainer()
    
    # Define models to use
    model_types = ['random_forest', 'xgboost']  # Start with faster models
    
    # Start training
    print("Starting multi-currency training...")
    print(f"Symbols to train: {len(trainer.symbols)}")
    print(f"Timeframes: {trainer.timeframes}")
    print(f"Models: {model_types}")
    print(f"Total combinations: {len(trainer.symbols) * len(trainer.timeframes) * len(model_types)}")
    
    results = trainer.train_all_currencies(model_types=model_types, max_workers=2)
    
    print("Training completed!")


if __name__ == "__main__":
    main()
