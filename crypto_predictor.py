"""
Cryptocurrency Price Prediction System
Predicts which cryptocurrencies will rise in 8h, 24h, and 48h timeframes
"""

import logging
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pickle
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

from data_loader import get_dataset
from data_loader.creator import create_dataset, preprocess
from omegaconf import OmegaConf

logger = logging.getLogger(__name__)

class CryptoPricePredictor:
    """
    Main prediction system for cryptocurrency price movements
    """
    
    def __init__(self, models_dir: str = "multi_currency_results"):
        self.models_dir = Path(models_dir)
        self.timeframes = ['8h', '24h', '48h']
        self.loaded_models = {}
        self.symbols = self._load_symbols()
        
        # Load trained models
        self._load_trained_models()
    
    def _load_symbols(self) -> List[str]:
        """Load cryptocurrency symbols from symbols.txt"""
        symbols_file = Path("symbols.txt")
        if not symbols_file.exists():
            raise FileNotFoundError("symbols.txt file not found")
        
        with open(symbols_file, 'r') as f:
            symbols = [line.strip() for line in f.readlines() if line.strip()]
        
        return symbols
    
    def _load_trained_models(self):
        """Load all trained models from disk"""
        if not self.models_dir.exists():
            logger.warning(f"Models directory {self.models_dir} not found")
            return
        
        for timeframe in self.timeframes:
            timeframe_dir = self.models_dir / timeframe
            if not timeframe_dir.exists():
                continue
                
            self.loaded_models[timeframe] = {}
            
            # Load all model files in timeframe directory
            for model_file in timeframe_dir.glob("*.pkl"):
                try:
                    with open(model_file, 'rb') as f:
                        model = pickle.load(f)
                    
                    # Extract symbol and model type from filename
                    filename = model_file.stem
                    parts = filename.split('_')
                    if len(parts) >= 2:
                        symbol = '_'.join(parts[:-1]).replace('_', '/')
                        model_type = parts[-1]
                        
                        if symbol not in self.loaded_models[timeframe]:
                            self.loaded_models[timeframe][symbol] = {}
                        
                        self.loaded_models[timeframe][symbol][model_type] = model
                        
                except Exception as e:
                    logger.warning(f"Failed to load model {model_file}: {str(e)}")
        
        total_models = sum(
            len(models) for timeframe_models in self.loaded_models.values() 
            for models in timeframe_models.values()
        )
        logger.info(f"Loaded {total_models} trained models")
    
    def _prepare_features(self, dataset: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare the same features used during training
        """
        df = dataset.copy()
        
        # Basic price features
        df['Price_Change'] = df['Close'].pct_change()
        df['High_Low_Ratio'] = df['High'] / df['Low']
        df['Volume_Change'] = df['Volume'].pct_change()
        
        # Moving averages
        for window in [5, 10, 20, 50]:
            df[f'MA_{window}'] = df['Close'].rolling(window=window).mean()
            df[f'MA_{window}_Ratio'] = df['Close'] / df[f'MA_{window}']
        
        # RSI (Relative Strength Index)
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        
        # MACD
        exp1 = df['Close'].ewm(span=12).mean()
        exp2 = df['Close'].ewm(span=26).mean()
        df['MACD'] = exp1 - exp2
        df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
        
        # Bollinger Bands
        df['BB_Middle'] = df['Close'].rolling(window=20).mean()
        bb_std = df['Close'].rolling(window=20).std()
        df['BB_Upper'] = df['BB_Middle'] + (bb_std * 2)
        df['BB_Lower'] = df['BB_Middle'] - (bb_std * 2)
        df['BB_Position'] = (df['Close'] - df['BB_Lower']) / (df['BB_Upper'] - df['BB_Lower'])
        
        # Volume indicators
        df['Volume_MA'] = df['Volume'].rolling(window=20).mean()
        df['Volume_Ratio'] = df['Volume'] / df['Volume_MA']
        
        # Remove NaN values
        df = df.dropna()
        
        return df
    
    def get_current_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """
        Get current market data for a symbol
        """
        try:
            # Create config for data loading
            cfg = OmegaConf.create({
                'dataset_loader': {
                    'name': 'CoinMarket',
                    'train_start_date': (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d %H:%M:%S'),
                    'valid_end_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'window_size': 30,
                    'symbol': symbol.replace('/USDT', '').replace('USDT', ''),
                    'batch_size': 32,
                    'features': ['High', 'Low', 'Close', 'Open', 'Volume']
                }
            })
            
            # Load recent data
            dataset, _ = get_dataset(
                cfg.dataset_loader.name,
                cfg.dataset_loader.train_start_date,
                cfg.dataset_loader.valid_end_date,
                cfg
            )
            
            return dataset
            
        except Exception as e:
            logger.warning(f"Failed to get current data for {symbol}: {str(e)}")
            return None
    
    def predict_single_currency(self, symbol: str, timeframe: str) -> Dict:
        """
        Predict price movement for a single currency and timeframe
        """
        if timeframe not in self.loaded_models:
            return {'error': f'No models loaded for timeframe {timeframe}'}
        
        if symbol not in self.loaded_models[timeframe]:
            return {'error': f'No models loaded for symbol {symbol}'}
        
        # Get current data
        current_data = self.get_current_data(symbol)
        if current_data is None:
            return {'error': f'Failed to get current data for {symbol}'}
        
        # Prepare features
        try:
            features_data = self._prepare_features(current_data)
            if len(features_data) == 0:
                return {'error': f'No valid feature data for {symbol}'}
            
            # Use the most recent data point for prediction
            latest_features = features_data.iloc[-1:].drop(['Date'], axis=1, errors='ignore')
            
        except Exception as e:
            return {'error': f'Feature preparation failed for {symbol}: {str(e)}'}
        
        # Get predictions from all available models for this symbol
        predictions = {}
        models = self.loaded_models[timeframe][symbol]
        
        for model_type, model in models.items():
            try:
                # Make prediction
                pred = model.predict(latest_features)
                
                # Convert prediction to probability and percentage
                if hasattr(pred, '__len__') and len(pred) > 0:
                    pred_value = float(pred[0]) if hasattr(pred[0], '__float__') else float(pred)
                else:
                    pred_value = float(pred)
                
                # Assume prediction is probability of rise
                rise_probability = max(0, min(1, pred_value))
                
                # Estimate rise percentage (this is a simplified approach)
                # In practice, you might want to train separate models for percentage prediction
                estimated_rise_pct = rise_probability * 10  # Simple scaling
                
                predictions[model_type] = {
                    'rise_probability': rise_probability,
                    'estimated_rise_percentage': estimated_rise_pct,
                    'will_rise': rise_probability > 0.5
                }
                
            except Exception as e:
                logger.warning(f"Prediction failed for {symbol} {timeframe} {model_type}: {str(e)}")
                predictions[model_type] = {'error': str(e)}
        
        if not predictions:
            return {'error': f'All predictions failed for {symbol}'}
        
        # Calculate ensemble prediction
        valid_predictions = [p for p in predictions.values() if 'error' not in p]
        if valid_predictions:
            avg_rise_prob = np.mean([p['rise_probability'] for p in valid_predictions])
            avg_rise_pct = np.mean([p['estimated_rise_percentage'] for p in valid_predictions])
            
            ensemble_result = {
                'symbol': symbol,
                'timeframe': timeframe,
                'current_price': float(current_data['Close'].iloc[-1]),
                'timestamp': datetime.now().isoformat(),
                'ensemble_prediction': {
                    'rise_probability': avg_rise_prob,
                    'estimated_rise_percentage': avg_rise_pct,
                    'will_rise': avg_rise_prob > 0.5,
                    'confidence': len(valid_predictions) / len(predictions)
                },
                'individual_predictions': predictions
            }
        else:
            ensemble_result = {
                'symbol': symbol,
                'timeframe': timeframe,
                'error': 'All individual predictions failed',
                'individual_predictions': predictions
            }
        
        return ensemble_result
    
    def predict_all_currencies(self, timeframe: str = '24h', min_rise_probability: float = 0.6) -> List[Dict]:
        """
        Predict price movements for all available currencies
        
        Args:
            timeframe: Target timeframe ('8h', '24h', '48h')
            min_rise_probability: Minimum probability threshold for filtering results
        
        Returns:
            List of predictions sorted by rise probability
        """
        if timeframe not in self.loaded_models:
            logger.error(f"No models available for timeframe {timeframe}")
            return []
        
        all_predictions = []
        available_symbols = list(self.loaded_models[timeframe].keys())
        
        logger.info(f"Making predictions for {len(available_symbols)} symbols in {timeframe} timeframe")
        
        for i, symbol in enumerate(available_symbols):
            if i % 50 == 0:
                logger.info(f"Processing symbol {i+1}/{len(available_symbols)}: {symbol}")
            
            prediction = self.predict_single_currency(symbol, timeframe)
            
            if 'error' not in prediction and 'ensemble_prediction' in prediction:
                rise_prob = prediction['ensemble_prediction']['rise_probability']
                if rise_prob >= min_rise_probability:
                    all_predictions.append(prediction)
        
        # Sort by rise probability (descending)
        all_predictions.sort(
            key=lambda x: x['ensemble_prediction']['rise_probability'], 
            reverse=True
        )
        
        logger.info(f"Found {len(all_predictions)} currencies with rise probability >= {min_rise_probability}")
        
        return all_predictions
    
    def generate_prediction_report(self, timeframe: str = '24h', top_n: int = 20) -> str:
        """
        Generate a formatted prediction report
        """
        predictions = self.predict_all_currencies(timeframe)
        
        if not predictions:
            return f"No predictions available for {timeframe} timeframe"
        
        # Take top N predictions
        top_predictions = predictions[:top_n]
        
        report = f"""
=== Cryptocurrency Rise Predictions - {timeframe} Timeframe ===
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Total currencies analyzed: {len(self.loaded_models.get(timeframe, {}))}
Currencies with positive predictions: {len(predictions)}
Top {len(top_predictions)} predictions:

Rank | Symbol        | Current Price | Rise Prob | Est. Rise % | Confidence
-----|---------------|---------------|-----------|-------------|------------"""
        
        for i, pred in enumerate(top_predictions, 1):
            symbol = pred['symbol']
            current_price = pred['current_price']
            ensemble = pred['ensemble_prediction']
            rise_prob = ensemble['rise_probability']
            rise_pct = ensemble['estimated_rise_percentage']
            confidence = ensemble['confidence']
            
            report += f"\n{i:4d} | {symbol:13s} | ${current_price:11.4f} | {rise_prob:8.1%} | {rise_pct:10.2f}% | {confidence:9.1%}"
        
        report += f"\n\nNote: Predictions are based on historical patterns and technical indicators."
        report += f"\nAlways conduct your own research before making investment decisions."
        
        return report
    
    def save_predictions(self, timeframe: str = '24h', filename: str = None):
        """
        Save predictions to JSON file
        """
        predictions = self.predict_all_currencies(timeframe)
        
        if filename is None:
            filename = f"predictions_{timeframe}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        output_file = Path(filename)
        
        with open(output_file, 'w') as f:
            json.dump(predictions, f, indent=2, default=str)
        
        logger.info(f"Predictions saved to {output_file}")
        return str(output_file)


def main():
    """Main function for running predictions"""
    logging.basicConfig(level=logging.INFO)
    
    # Initialize predictor
    predictor = CryptoPricePredictor()
    
    # Generate predictions for all timeframes
    for timeframe in ['8h', '24h', '48h']:
        print(f"\n{predictor.generate_prediction_report(timeframe, top_n=15)}")
        
        # Save predictions
        predictor.save_predictions(timeframe)
    
    print("\nPrediction reports generated and saved!")


if __name__ == "__main__":
    main()
