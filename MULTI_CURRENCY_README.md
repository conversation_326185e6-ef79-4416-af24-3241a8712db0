# نظام التنبؤ بالعملات المشفرة المتعدد - Multi-Currency Crypto Prediction System

## نظرة عامة

تم تطوير هذا النظام للتنبؤ بحركة أسعار العملات المشفرة على فترات زمنية مختلفة (8 ساعات، 24 ساعة، 48 ساعة) مع توقع نسبة الصعود المحتملة. النظام يدرب نماذج التعلم الآلي على جميع العملات الموجودة في ملف `symbols.txt` (386 عملة).

## الميزات الرئيسية

- **تدريب متعدد العملات**: تدريب تلقائي على 386 عملة مشفرة
- **توقعات متعددة الأطر الزمنية**: 8h, 24h, 48h
- **نماذج متعددة**: Random Forest, XGBoost, LSTM, GRU, وغيرها
- **مؤشرات تقنية متقدمة**: RSI, MACD, Bollinger Bands, Moving Averages
- **تقييم الأداء**: دقة التنبؤ ونسبة الصعود المتوقعة
- **تقارير شاملة**: ترتيب العملات حسب احتمالية الصعود

## الملفات الجديدة

### 1. `multi_currency_trainer.py`
سكريبت التدريب الرئيسي الذي يتعامل مع:
- تحميل جميع العملات من `symbols.txt`
- إنشاء المؤشرات التقنية
- تدريب النماذج بشكل متوازي
- حفظ النتائج والتقارير

### 2. `crypto_predictor.py`
نظام التنبؤ الذي يوفر:
- تحميل النماذج المدربة
- توليد التنبؤات للعملات
- حساب احتمالية الصعود ونسبة الصعود المتوقعة
- إنشاء تقارير مفصلة

### 3. `run_crypto_system.py`
واجهة موحدة لتشغيل النظام بالكامل

## طريقة الاستخدام

### 1. التحقق من حالة النظام
```bash
python run_crypto_system.py --status
```

### 2. تدريب النماذج (المرحلة الأولى)
```bash
# تدريب باستخدام Random Forest و XGBoost
python run_crypto_system.py --train

# تدريب باستخدام نماذج محددة
python run_crypto_system.py --train --models random_forest xgboost lstm

# تدريب مع عدد أكبر من العمليات المتوازية
python run_crypto_system.py --train --workers 4
```

### 3. توليد التنبؤات
```bash
# توليد تنبؤات لجميع الأطر الزمنية
python run_crypto_system.py --predict

# عرض أفضل 30 توقع
python run_crypto_system.py --predict --top 30
```

### 4. تنبؤ سريع لعملة محددة
```bash
# تنبؤ لعملة البيتكوين لمدة 24 ساعة
python run_crypto_system.py --quick BTC/USDT

# تنبؤ لعملة الإيثيريوم لمدة 8 ساعات
python run_crypto_system.py --quick ETH/USDT --timeframe 8h
```

## بنية النتائج

### مجلد النتائج: `multi_currency_results/`
```
multi_currency_results/
├── 8h/                     # نماذج الـ 8 ساعات
│   ├── BTC_USDT_random_forest.pkl
│   ├── ETH_USDT_xgboost.pkl
│   └── ...
├── 24h/                    # نماذج الـ 24 ساعة
├── 48h/                    # نماذج الـ 48 ساعة
├── training_results.json   # نتائج التدريب التفصيلية
└── training_summary.json   # ملخص التدريب
```

### ملفات التنبؤات
```
predictions_24h_20240128_030000.json  # تنبؤات مفصلة
predictions_8h_20240128_030000.json
predictions_48h_20240128_030000.json
```

## مثال على تقرير التنبؤات

```
=== Cryptocurrency Rise Predictions - 24h Timeframe ===
Generated: 2024-01-28 03:00:00
Total currencies analyzed: 250
Currencies with positive predictions: 45
Top 15 predictions:

Rank | Symbol        | Current Price | Rise Prob | Est. Rise % | Confidence
-----|---------------|---------------|-----------|-------------|------------
   1 | DOGE/USDT     |    $0.0850    |    85.2%  |      8.52%  |     100.0%
   2 | ADA/USDT      |    $0.4520    |    82.1%  |      8.21%  |     100.0%
   3 | SOL/USDT      |   $98.7500    |    79.8%  |      7.98%  |     100.0%
   4 | MATIC/USDT    |    $0.8910    |    76.5%  |      7.65%  |     100.0%
   5 | DOT/USDT      |    $7.2300    |    74.2%  |      7.42%  |     100.0%
```

## المؤشرات التقنية المستخدمة

1. **مؤشرات الأسعار الأساسية**:
   - نسبة التغيير في السعر
   - نسبة أعلى/أقل سعر
   - تغيير الحجم

2. **المتوسطات المتحركة**:
   - MA 5, 10, 20, 50
   - نسب السعر إلى المتوسطات

3. **مؤشرات الزخم**:
   - RSI (Relative Strength Index)
   - MACD و MACD Signal

4. **مؤشرات التقلبات**:
   - Bollinger Bands
   - موقع السعر في النطاق

5. **مؤشرات الحجم**:
   - متوسط الحجم
   - نسبة الحجم الحالي للمتوسط

## معايير التقييم

- **احتمالية الصعود**: احتمالية ارتفاع السعر بنسبة 2% على الأقل
- **نسبة الصعود المتوقعة**: النسبة المئوية المتوقعة للارتفاع
- **الثقة**: نسبة النماذج التي نجحت في التنبؤ
- **دقة التنبؤ**: نسبة التنبؤات الصحيحة

## نصائح للاستخدام

1. **التدريب الأولي**: قم بتدريب النماذج أولاً قبل محاولة التنبؤ
2. **التحديث الدوري**: أعد تدريب النماذج دورياً للحصول على أفضل النتائج
3. **التنويع**: استخدم عدة نماذج للحصول على تنبؤات أكثر دقة
4. **إدارة المخاطر**: لا تعتمد على التنبؤات فقط، قم بإجراء بحثك الخاص

## متطلبات النظام

- Python 3.7+
- المكتبات المطلوبة في `requirements.txt`
- مساحة تخزين كافية للنماذج المدربة
- اتصال بالإنترنت لتحميل البيانات

## استكشاف الأخطاء

### خطأ "No models loaded"
- تأكد من تدريب النماذج أولاً باستخدام `--train`

### خطأ "Failed to get current data"
- تحقق من اتصال الإنترنت
- تأكد من صحة رمز العملة

### خطأ "Insufficient data"
- بعض العملات قد لا تحتوي على بيانات كافية للتدريب
- هذا أمر طبيعي ولن يؤثر على باقي العملات

## الدعم والتطوير

لأي استفسارات أو مشاكل، يرجى مراجعة ملفات السجل (`crypto_system.log`) للحصول على تفاصيل إضافية.

---

**تحذير**: هذا النظام مخصص للأغراض التعليمية والبحثية. لا تعتمد عليه فقط في اتخاذ قرارات الاستثمار. قم دائماً بإجراء بحثك الخاص وإدارة المخاطر بحكمة.
