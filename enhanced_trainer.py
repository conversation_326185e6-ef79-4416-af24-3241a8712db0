"""
Enhanced Multi-Currency Trainer with 4+ Years Historical Data
Uses high-quality historical data with optimal timeframes for maximum accuracy
"""

import logging
import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import concurrent.futures
from pathlib import Path
import json
import pickle
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import TimeSeriesSplit, train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, mean_squared_error, r2_score
import xgboost as xgb
from tqdm import tqdm

# Try to import deep learning libraries
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, GRU, Dense, Dropout, BatchNormalization
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    DEEP_LEARNING_AVAILABLE = True
except ImportError:
    DEEP_LEARNING_AVAILABLE = False
    logging.warning("TensorFlow not available. Deep learning models will be skipped.")

from advanced_data_fetcher import AdvancedDataFetcher

logger = logging.getLogger(__name__)

class EnhancedMultiCurrencyTrainer:
    """
    Enhanced trainer that uses 4+ years of historical data with optimal timeframes
    """
    
    def __init__(self, data_dir: str = "historical_data", results_dir: str = "enhanced_results"):
        self.data_dir = Path(data_dir)
        self.results_dir = Path(results_dir)
        self.results_dir.mkdir(exist_ok=True)
        
        # Create subdirectories for different prediction timeframes
        self.prediction_timeframes = ['8h', '24h', '48h']
        for timeframe in self.prediction_timeframes:
            (self.results_dir / timeframe).mkdir(exist_ok=True)
        
        # Data timeframes (for training)
        self.data_timeframes = ['1h', '4h', '1d']
        
        # Model configurations
        self.models_config = {
            'random_forest_classifier': {
                'type': 'classification',
                'class': RandomForestClassifier,
                'params': {
                    'n_estimators': 200,
                    'max_depth': 15,
                    'min_samples_split': 5,
                    'min_samples_leaf': 2,
                    'random_state': 42,
                    'n_jobs': -1
                }
            },
            'random_forest_regressor': {
                'type': 'regression',
                'class': RandomForestRegressor,
                'params': {
                    'n_estimators': 200,
                    'max_depth': 15,
                    'min_samples_split': 5,
                    'min_samples_leaf': 2,
                    'random_state': 42,
                    'n_jobs': -1
                }
            },
            'xgboost_classifier': {
                'type': 'classification',
                'class': xgb.XGBClassifier,
                'params': {
                    'n_estimators': 200,
                    'max_depth': 8,
                    'learning_rate': 0.1,
                    'subsample': 0.8,
                    'colsample_bytree': 0.8,
                    'random_state': 42,
                    'n_jobs': -1
                }
            },
            'xgboost_regressor': {
                'type': 'regression',
                'class': xgb.XGBRegressor,
                'params': {
                    'n_estimators': 200,
                    'max_depth': 8,
                    'learning_rate': 0.1,
                    'subsample': 0.8,
                    'colsample_bytree': 0.8,
                    'random_state': 42,
                    'n_jobs': -1
                }
            }
        }
        
        # Add deep learning models if available
        if DEEP_LEARNING_AVAILABLE:
            self.models_config.update({
                'lstm_classifier': {
                    'type': 'deep_classification',
                    'class': 'lstm',
                    'params': {
                        'sequence_length': 60,
                        'lstm_units': [128, 64],
                        'dropout': 0.3,
                        'epochs': 100,
                        'batch_size': 32
                    }
                },
                'gru_regressor': {
                    'type': 'deep_regression',
                    'class': 'gru',
                    'params': {
                        'sequence_length': 60,
                        'gru_units': [128, 64],
                        'dropout': 0.3,
                        'epochs': 100,
                        'batch_size': 32
                    }
                }
            })
        
        self.training_results = {}
        self.scalers = {}
    
    def _load_symbols(self) -> List[str]:
        """Load symbols from symbols.txt"""
        symbols_file = Path("symbols.txt")
        if not symbols_file.exists():
            raise FileNotFoundError("symbols.txt file not found")
        
        with open(symbols_file, 'r') as f:
            symbols = [line.strip() for line in f.readlines() if line.strip()]
        
        return symbols
    
    def _load_historical_data(self, symbol: str, data_timeframe: str) -> Optional[pd.DataFrame]:
        """Load historical data for a symbol"""
        try:
            data_file = self.data_dir / data_timeframe / f"{symbol.replace('/', '_')}.csv"
            
            if not data_file.exists():
                logger.warning(f"Data file not found: {data_file}")
                return None
            
            df = pd.read_csv(data_file)
            df['Date'] = pd.to_datetime(df['Date'])
            
            # Ensure minimum data requirements (4 years)
            if len(df) < 1000:  # Minimum threshold
                logger.warning(f"Insufficient data for {symbol} {data_timeframe}: {len(df)} samples")
                return None
            
            # Check data quality
            date_range = (df['Date'].max() - df['Date'].min()).days
            if date_range < 1400:  # Less than ~4 years
                logger.warning(f"Insufficient date range for {symbol} {data_timeframe}: {date_range} days")
                return None
            
            return df.sort_values('Date').reset_index(drop=True)
            
        except Exception as e:
            logger.error(f"Error loading data for {symbol} {data_timeframe}: {str(e)}")
            return None
    
    def _create_prediction_targets(self, df: pd.DataFrame, prediction_timeframe: str, data_timeframe: str) -> pd.DataFrame:
        """Create prediction targets based on timeframes"""
        df = df.copy()
        
        # Convert prediction timeframe to data points
        timeframe_map = {
            '1h': {'8h': 8, '24h': 24, '48h': 48},
            '4h': {'8h': 2, '24h': 6, '48h': 12},
            '1d': {'8h': 1, '24h': 1, '48h': 2}  # For daily data, use 1-2 days
        }
        
        if data_timeframe not in timeframe_map:
            raise ValueError(f"Unsupported data timeframe: {data_timeframe}")
        
        shift_periods = timeframe_map[data_timeframe][prediction_timeframe]
        
        # Calculate future price
        df['Future_Price'] = df['Close'].shift(-shift_periods)
        
        # Calculate rise percentage
        df['Rise_Percentage'] = ((df['Future_Price'] - df['Close']) / df['Close']) * 100
        
        # Binary classification targets (different thresholds for different timeframes)
        thresholds = {'8h': 1.5, '24h': 2.0, '48h': 3.0}
        threshold = thresholds[prediction_timeframe]
        
        df['Will_Rise'] = (df['Rise_Percentage'] >= threshold).astype(int)
        df['Will_Rise_Strong'] = (df['Rise_Percentage'] >= threshold * 2).astype(int)
        
        # Multi-class targets
        df['Price_Direction'] = 0  # Neutral
        df.loc[df['Rise_Percentage'] >= threshold, 'Price_Direction'] = 1  # Up
        df.loc[df['Rise_Percentage'] <= -threshold, 'Price_Direction'] = -1  # Down
        df.loc[df['Rise_Percentage'] >= threshold * 2, 'Price_Direction'] = 2  # Strong Up
        df.loc[df['Rise_Percentage'] <= -threshold * 2, 'Price_Direction'] = -2  # Strong Down
        
        # Regression targets (capped for stability)
        df['Rise_Percentage_Capped'] = np.clip(df['Rise_Percentage'], -20, 20)
        
        return df.dropna()
    
    def _prepare_features(self, df: pd.DataFrame, data_timeframe: str) -> Tuple[np.ndarray, List[str]]:
        """Prepare feature matrix for training"""
        # Select relevant feature columns (exclude target and date columns)
        exclude_cols = [
            'Date', 'Future_Price', 'Rise_Percentage', 'Will_Rise', 'Will_Rise_Strong',
            'Price_Direction', 'Rise_Percentage_Capped'
        ]
        
        feature_cols = [col for col in df.columns if col not in exclude_cols]
        
        # Handle missing values
        feature_df = df[feature_cols].copy()
        
        # Forward fill and backward fill
        feature_df = feature_df.fillna(method='ffill').fillna(method='bfill')
        
        # Replace any remaining NaN with median
        for col in feature_df.columns:
            if feature_df[col].isna().any():
                feature_df[col] = feature_df[col].fillna(feature_df[col].median())
        
        # Remove infinite values
        feature_df = feature_df.replace([np.inf, -np.inf], np.nan)
        feature_df = feature_df.fillna(feature_df.median())
        
        return feature_df.values, feature_cols
    
    def _create_sequences(self, X: np.ndarray, y: np.ndarray, sequence_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """Create sequences for deep learning models"""
        X_seq, y_seq = [], []
        
        for i in range(sequence_length, len(X)):
            X_seq.append(X[i-sequence_length:i])
            y_seq.append(y[i])
        
        return np.array(X_seq), np.array(y_seq)
    
    def _build_lstm_model(self, input_shape: Tuple, model_params: Dict, task_type: str) -> tf.keras.Model:
        """Build LSTM model"""
        model = Sequential()
        
        # First LSTM layer
        model.add(LSTM(
            model_params['lstm_units'][0],
            return_sequences=len(model_params['lstm_units']) > 1,
            input_shape=input_shape
        ))
        model.add(BatchNormalization())
        model.add(Dropout(model_params['dropout']))
        
        # Additional LSTM layers
        for i, units in enumerate(model_params['lstm_units'][1:], 1):
            return_sequences = i < len(model_params['lstm_units']) - 1
            model.add(LSTM(units, return_sequences=return_sequences))
            model.add(BatchNormalization())
            model.add(Dropout(model_params['dropout']))
        
        # Dense layers
        model.add(Dense(64, activation='relu'))
        model.add(Dropout(model_params['dropout']))
        model.add(Dense(32, activation='relu'))
        model.add(Dropout(model_params['dropout']))
        
        # Output layer
        if task_type == 'deep_classification':
            model.add(Dense(1, activation='sigmoid'))
            model.compile(optimizer=Adam(learning_rate=0.001), loss='binary_crossentropy', metrics=['accuracy'])
        else:  # regression
            model.add(Dense(1, activation='linear'))
            model.compile(optimizer=Adam(learning_rate=0.001), loss='mse', metrics=['mae'])
        
        return model
    
    def _build_gru_model(self, input_shape: Tuple, model_params: Dict, task_type: str) -> tf.keras.Model:
        """Build GRU model"""
        model = Sequential()
        
        # First GRU layer
        model.add(GRU(
            model_params['gru_units'][0],
            return_sequences=len(model_params['gru_units']) > 1,
            input_shape=input_shape
        ))
        model.add(BatchNormalization())
        model.add(Dropout(model_params['dropout']))
        
        # Additional GRU layers
        for i, units in enumerate(model_params['gru_units'][1:], 1):
            return_sequences = i < len(model_params['gru_units']) - 1
            model.add(GRU(units, return_sequences=return_sequences))
            model.add(BatchNormalization())
            model.add(Dropout(model_params['dropout']))
        
        # Dense layers
        model.add(Dense(64, activation='relu'))
        model.add(Dropout(model_params['dropout']))
        model.add(Dense(32, activation='relu'))
        model.add(Dropout(model_params['dropout']))
        
        # Output layer
        if task_type == 'deep_regression':
            model.add(Dense(1, activation='linear'))
            model.compile(optimizer=Adam(learning_rate=0.001), loss='mse', metrics=['mae'])
        else:  # classification
            model.add(Dense(1, activation='sigmoid'))
            model.compile(optimizer=Adam(learning_rate=0.001), loss='binary_crossentropy', metrics=['accuracy'])
        
        return model
    
    def _train_single_model(self, symbol: str, data_timeframe: str, prediction_timeframe: str, model_name: str) -> Dict:
        """Train a single model for a symbol"""
        try:
            logger.info(f"Training {model_name} for {symbol} ({data_timeframe} -> {prediction_timeframe})")
            
            # Load data
            df = self._load_historical_data(symbol, data_timeframe)
            if df is None:
                return {'error': 'Failed to load data', 'symbol': symbol, 'model': model_name}
            
            # Create targets
            df = self._create_prediction_targets(df, prediction_timeframe, data_timeframe)
            if len(df) < 500:
                return {'error': 'Insufficient data after preprocessing', 'symbol': symbol, 'model': model_name}
            
            # Prepare features
            X, feature_names = self._prepare_features(df, data_timeframe)
            
            model_config = self.models_config[model_name]
            task_type = model_config['type']
            
            # Select target variable
            if 'classification' in task_type:
                y = df['Will_Rise'].values
                metric_name = 'accuracy'
            else:  # regression
                y = df['Rise_Percentage_Capped'].values
                metric_name = 'r2_score'
            
            # Scale features
            scaler_key = f"{symbol}_{data_timeframe}_{model_name}"
            if 'deep' in task_type:
                scaler = RobustScaler()
            else:
                scaler = StandardScaler()
            
            X_scaled = scaler.fit_transform(X)
            self.scalers[scaler_key] = scaler
            
            # Split data (time series split)
            split_idx = int(len(X_scaled) * 0.8)
            X_train, X_test = X_scaled[:split_idx], X_scaled[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]
            
            # Train model
            if 'deep' in task_type:
                # Deep learning models
                sequence_length = model_config['params']['sequence_length']
                
                # Create sequences
                X_train_seq, y_train_seq = self._create_sequences(X_train, y_train, sequence_length)
                X_test_seq, y_test_seq = self._create_sequences(X_test, y_test, sequence_length)
                
                if len(X_train_seq) < 100:
                    return {'error': 'Insufficient data for sequence creation', 'symbol': symbol, 'model': model_name}
                
                # Build model
                input_shape = (sequence_length, X_train.shape[1])
                
                if model_config['class'] == 'lstm':
                    model = self._build_lstm_model(input_shape, model_config['params'], task_type)
                elif model_config['class'] == 'gru':
                    model = self._build_gru_model(input_shape, model_config['params'], task_type)
                
                # Callbacks
                callbacks = [
                    EarlyStopping(patience=20, restore_best_weights=True),
                    ReduceLROnPlateau(patience=10, factor=0.5)
                ]
                
                # Train
                history = model.fit(
                    X_train_seq, y_train_seq,
                    validation_data=(X_test_seq, y_test_seq),
                    epochs=model_config['params']['epochs'],
                    batch_size=model_config['params']['batch_size'],
                    callbacks=callbacks,
                    verbose=0
                )
                
                # Evaluate
                if 'classification' in task_type:
                    y_pred = (model.predict(X_test_seq) > 0.5).astype(int).flatten()
                    score = accuracy_score(y_test_seq, y_pred)
                else:
                    y_pred = model.predict(X_test_seq).flatten()
                    score = r2_score(y_test_seq, y_pred)
                
                # Prepare for saving
                trained_model = {
                    'model': model,
                    'scaler': scaler,
                    'feature_names': feature_names,
                    'sequence_length': sequence_length,
                    'model_type': 'deep_learning'
                }
                
            else:
                # Traditional ML models
                model_class = model_config['class']
                model_params = model_config['params']
                
                model = model_class(**model_params)
                model.fit(X_train, y_train)
                
                # Evaluate
                y_pred = model.predict(X_test)
                
                if 'classification' in task_type:
                    score = accuracy_score(y_test, y_pred)
                else:
                    score = r2_score(y_test, y_pred)
                
                # Prepare for saving
                trained_model = {
                    'model': model,
                    'scaler': scaler,
                    'feature_names': feature_names,
                    'model_type': 'traditional'
                }
            
            # Save model
            model_filename = f"{symbol.replace('/', '_')}_{data_timeframe}_{prediction_timeframe}_{model_name}.pkl"
            model_path = self.results_dir / prediction_timeframe / model_filename
            
            with open(model_path, 'wb') as f:
                pickle.dump(trained_model, f)
            
            result = {
                'symbol': symbol,
                'data_timeframe': data_timeframe,
                'prediction_timeframe': prediction_timeframe,
                'model_name': model_name,
                'score': score,
                'metric_name': metric_name,
                'model_path': str(model_path),
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'feature_count': len(feature_names),
                'status': 'success'
            }
            
            logger.info(f"Successfully trained {model_name} for {symbol}: {metric_name} = {score:.4f}")
            return result
            
        except Exception as e:
            logger.error(f"Error training {model_name} for {symbol}: {str(e)}")
            return {
                'symbol': symbol,
                'data_timeframe': data_timeframe,
                'prediction_timeframe': prediction_timeframe,
                'model_name': model_name,
                'error': str(e),
                'status': 'failed'
            }
    
    def train_all_models(self, max_workers: int = 2, selected_models: List[str] = None) -> Dict:
        """Train all models for all symbols and timeframes"""
        if selected_models is None:
            selected_models = ['random_forest_classifier', 'xgboost_classifier', 'random_forest_regressor']
        
        symbols = self._load_symbols()
        
        # Create all training tasks
        tasks = []
        for symbol in symbols:
            for data_timeframe in self.data_timeframes:
                for prediction_timeframe in self.prediction_timeframes:
                    for model_name in selected_models:
                        tasks.append((symbol, data_timeframe, prediction_timeframe, model_name))
        
        logger.info(f"Starting training for {len(tasks)} tasks with {max_workers} workers")
        
        all_results = {}
        
        # Execute tasks with progress tracking
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_task = {
                executor.submit(self._train_single_model, symbol, data_tf, pred_tf, model): 
                (symbol, data_tf, pred_tf, model)
                for symbol, data_tf, pred_tf, model in tasks
            }
            
            completed = 0
            for future in tqdm(concurrent.futures.as_completed(future_to_task), 
                             total=len(tasks), desc="Training models"):
                
                task_info = future_to_task[future]
                try:
                    result = future.result()
                    task_key = f"{task_info[0]}_{task_info[1]}_{task_info[2]}_{task_info[3]}"
                    all_results[task_key] = result
                    
                    completed += 1
                    
                    # Save progress periodically
                    if completed % 50 == 0:
                        progress_file = self.results_dir / "training_progress.json"
                        with open(progress_file, 'w') as f:
                            json.dump({
                                'completed': completed,
                                'total': len(tasks),
                                'timestamp': datetime.now().isoformat()
                            }, f)
                    
                except Exception as e:
                    logger.error(f"Task failed: {str(e)}")
                    task_key = f"{task_info[0]}_{task_info[1]}_{task_info[2]}_{task_info[3]}"
                    all_results[task_key] = {
                        'symbol': task_info[0],
                        'error': str(e),
                        'status': 'failed'
                    }
        
        # Save final results
        results_file = self.results_dir / "enhanced_training_results.json"
        with open(results_file, 'w') as f:
            json.dump(all_results, f, indent=2, default=str)
        
        # Generate summary
        self._generate_training_summary(all_results)
        
        logger.info(f"Training completed. Results saved to {results_file}")
        return all_results
    
    def _generate_training_summary(self, results: Dict):
        """Generate comprehensive training summary"""
        summary = {
            'training_date': datetime.now().isoformat(),
            'total_tasks': len(results),
            'successful_tasks': len([r for r in results.values() if r.get('status') == 'success']),
            'failed_tasks': len([r for r in results.values() if r.get('status') == 'failed']),
            'by_model': {},
            'by_timeframe': {},
            'by_symbol': {},
            'top_performers': [],
            'performance_distribution': {}
        }
        
        successful_results = [r for r in results.values() if r.get('status') == 'success']
        
        # Analyze by model
        for result in successful_results:
            model_name = result.get('model_name', 'unknown')
            if model_name not in summary['by_model']:
                summary['by_model'][model_name] = {'count': 0, 'avg_score': 0, 'scores': []}
            
            summary['by_model'][model_name]['count'] += 1
            score = result.get('score', 0)
            summary['by_model'][model_name]['scores'].append(score)
        
        # Calculate averages
        for model_stats in summary['by_model'].values():
            if model_stats['scores']:
                model_stats['avg_score'] = np.mean(model_stats['scores'])
                model_stats['std_score'] = np.std(model_stats['scores'])
                del model_stats['scores']  # Remove raw scores to save space
        
        # Analyze by timeframe
        for result in successful_results:
            pred_tf = result.get('prediction_timeframe', 'unknown')
            if pred_tf not in summary['by_timeframe']:
                summary['by_timeframe'][pred_tf] = {'count': 0, 'avg_score': 0, 'scores': []}
            
            summary['by_timeframe'][pred_tf]['count'] += 1
            summary['by_timeframe'][pred_tf]['scores'].append(result.get('score', 0))
        
        for tf_stats in summary['by_timeframe'].values():
            if tf_stats['scores']:
                tf_stats['avg_score'] = np.mean(tf_stats['scores'])
                tf_stats['std_score'] = np.std(tf_stats['scores'])
                del tf_stats['scores']
        
        # Top performers
        summary['top_performers'] = sorted(
            successful_results, 
            key=lambda x: x.get('score', 0), 
            reverse=True
        )[:30]
        
        # Save summary
        summary_file = self.results_dir / "enhanced_training_summary.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        # Generate text summary
        self._generate_text_training_summary(summary)
        
        logger.info(f"Training summary saved to {summary_file}")
    
    def _generate_text_training_summary(self, summary: Dict):
        """Generate human-readable training summary"""
        text_summary = f"""
=== ENHANCED TRAINING SUMMARY ===
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

OVERALL STATISTICS:
- Total training tasks: {summary['total_tasks']}
- Successful: {summary['successful_tasks']}
- Failed: {summary['failed_tasks']}
- Success rate: {summary['successful_tasks']/summary['total_tasks']*100:.1f}%

MODEL PERFORMANCE:
"""
        
        for model_name, stats in summary['by_model'].items():
            text_summary += f"""
{model_name}:
  - Tasks completed: {stats['count']}
  - Average score: {stats['avg_score']:.4f} ± {stats.get('std_score', 0):.4f}
"""
        
        text_summary += f"""
TIMEFRAME ANALYSIS:
"""
        
        for timeframe, stats in summary['by_timeframe'].items():
            text_summary += f"""
{timeframe} predictions:
  - Models trained: {stats['count']}
  - Average score: {stats['avg_score']:.4f} ± {stats.get('std_score', 0):.4f}
"""
        
        text_summary += f"""
TOP 10 PERFORMERS:
"""
        
        for i, result in enumerate(summary['top_performers'][:10], 1):
            text_summary += f"  {i}. {result['symbol']} ({result['model_name']}, {result['prediction_timeframe']}): {result['score']:.4f}\n"
        
        # Save text summary
        text_file = self.results_dir / "enhanced_training_summary.txt"
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(text_summary)
        
        print(text_summary)


def main():
    """Main function for enhanced training"""
    logging.basicConfig(level=logging.INFO)
    
    print("=" * 60)
    print("ENHANCED CRYPTOCURRENCY PREDICTION TRAINING")
    print("Using 4+ Years of Historical Data")
    print("=" * 60)
    
    # Check if data is available
    data_dir = Path("historical_data")
    if not data_dir.exists():
        print("Historical data not found. Please run advanced_data_fetcher.py first.")
        return
    
    # Initialize trainer
    trainer = EnhancedMultiCurrencyTrainer()
    
    # Select models to train
    available_models = list(trainer.models_config.keys())
    print(f"Available models: {available_models}")
    
    selected_models = ['random_forest_classifier', 'xgboost_classifier', 'random_forest_regressor']
    if DEEP_LEARNING_AVAILABLE:
        selected_models.append('lstm_classifier')
    
    print(f"Selected models: {selected_models}")
    
    # Confirm training
    response = input(f"\nStart enhanced training? This will take several hours. (y/N): ")
    if response.lower() != 'y':
        print("Training cancelled.")
        return
    
    # Start training
    print("Starting enhanced training...")
    start_time = datetime.now()
    
    results = trainer.train_all_models(max_workers=2, selected_models=selected_models)
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    print(f"\nEnhanced training completed in {duration}")
    print(f"Results saved in: {trainer.results_dir}")


if __name__ == "__main__":
    main()
