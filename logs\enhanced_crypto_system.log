2025-07-28 11:57:57,146 - __main__ - INFO - [OK] All system requirements met
2025-07-28 11:57:57,169 - advanced_data_fetcher - INFO - Loaded 386 symbols
2025-07-28 11:58:45,215 - advanced_data_fetcher - INFO - Loaded 386 symbols
2025-07-28 11:58:45,215 - advanced_data_fetcher - INFO - Starting data fetch for 386 symbols
2025-07-28 11:58:45,215 - advanced_data_fetcher - INFO - Fetching 1h data for 1INCH/USDT
2025-07-28 11:58:45,215 - advanced_data_fetcher - INFO - Fetching 1h data for A/USDT
2025-07-28 11:58:46,496 - advanced_data_fetcher - WARNING - Low quality data for A/USDT 1h: {'date_range_days': 61, 'min_required_days': 1460, 'actual_samples': 1465, 'expected_samples': 35040, 'completeness_ratio': 0.04180936073059361, 'gap_ratio': 0.0, 'avg_volume': np.float64(732052.1604095564), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 11:58:46,764 - advanced_data_fetcher - INFO - Cached data for A/USDT 1h
2025-07-28 11:58:46,764 - advanced_data_fetcher - INFO - Fetching 4h data for A/USDT
2025-07-28 11:58:47,265 - advanced_data_fetcher - WARNING - Low quality data for A/USDT 4h: {'date_range_days': 61, 'min_required_days': 1460, 'actual_samples': 367, 'expected_samples': 8760, 'completeness_ratio': 0.04189497716894977, 'gap_ratio': 0.0, 'avg_volume': np.float64(2922224.5640326976), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 11:58:47,381 - advanced_data_fetcher - INFO - Cached data for A/USDT 4h
2025-07-28 11:58:47,381 - advanced_data_fetcher - INFO - Fetching 1d data for A/USDT
2025-07-28 11:58:47,816 - advanced_data_fetcher - WARNING - Low quality data for A/USDT 1d: {'date_range_days': 61, 'min_required_days': 1460, 'actual_samples': 62, 'expected_samples': 1460, 'completeness_ratio': 0.04246575342465753, 'gap_ratio': 0.0, 'avg_volume': np.float64(17297684.112903226), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 11:58:47,897 - advanced_data_fetcher - INFO - Cached data for A/USDT 1d
2025-07-28 11:58:47,897 - advanced_data_fetcher - INFO - Fetching 1h data for AAVE/USDT
2025-07-28 11:59:10,380 - advanced_data_fetcher - INFO - Cached data for 1INCH/USDT 1h
2025-07-28 11:59:10,380 - advanced_data_fetcher - INFO - Fetching 4h data for 1INCH/USDT
2025-07-28 11:59:14,065 - advanced_data_fetcher - INFO - Cached data for AAVE/USDT 1h
2025-07-28 11:59:14,065 - advanced_data_fetcher - INFO - Fetching 4h data for AAVE/USDT
2025-07-28 11:59:16,334 - advanced_data_fetcher - INFO - Cached data for 1INCH/USDT 4h
2025-07-28 11:59:16,334 - advanced_data_fetcher - INFO - Fetching 1d data for 1INCH/USDT
2025-07-28 11:59:17,527 - advanced_data_fetcher - INFO - Cached data for 1INCH/USDT 1d
2025-07-28 11:59:17,527 - advanced_data_fetcher - INFO - Fetching 1h data for ACA/USDT
2025-07-28 11:59:19,834 - advanced_data_fetcher - INFO - Cached data for AAVE/USDT 4h
2025-07-28 11:59:19,834 - advanced_data_fetcher - INFO - Fetching 1d data for AAVE/USDT
2025-07-28 11:59:21,135 - advanced_data_fetcher - INFO - Cached data for AAVE/USDT 1d
2025-07-28 11:59:21,135 - advanced_data_fetcher - INFO - Fetching 1h data for ACE/USDT
2025-07-28 11:59:28,639 - advanced_data_fetcher - WARNING - Low quality data for ACE/USDT 1h: {'date_range_days': 588, 'min_required_days': 1460, 'actual_samples': 14115, 'expected_samples': 35040, 'completeness_ratio': 0.4028253424657534, 'gap_ratio': 0.0, 'avg_volume': np.float64(127034.1973361672), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 11:59:30,387 - advanced_data_fetcher - INFO - Cached data for ACE/USDT 1h
2025-07-28 11:59:30,387 - advanced_data_fetcher - INFO - Fetching 4h data for ACE/USDT
2025-07-28 11:59:32,365 - advanced_data_fetcher - WARNING - Low quality data for ACE/USDT 4h: {'date_range_days': 588, 'min_required_days': 1460, 'actual_samples': 3530, 'expected_samples': 8760, 'completeness_ratio': 0.4029680365296804, 'gap_ratio': 0.0, 'avg_volume': np.float64(507956.8542209632), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 11:59:33,015 - advanced_data_fetcher - INFO - Cached data for ACE/USDT 4h
2025-07-28 11:59:33,015 - advanced_data_fetcher - INFO - Fetching 1d data for ACE/USDT
2025-07-28 11:59:33,531 - advanced_data_fetcher - WARNING - Low quality data for ACE/USDT 1d: {'date_range_days': 588, 'min_required_days': 1460, 'actual_samples': 589, 'expected_samples': 1460, 'completeness_ratio': 0.40342465753424656, 'gap_ratio': 0.0, 'avg_volume': np.float64(3044291.5032258066), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 11:59:33,954 - advanced_data_fetcher - INFO - Cached data for ACE/USDT 1d
2025-07-28 11:59:33,966 - advanced_data_fetcher - INFO - Fetching 1h data for ACH/USDT
2025-07-28 11:59:37,074 - advanced_data_fetcher - INFO - Cached data for ACA/USDT 1h
2025-07-28 11:59:37,075 - advanced_data_fetcher - INFO - Fetching 4h data for ACA/USDT
2025-07-28 11:59:41,996 - advanced_data_fetcher - INFO - Cached data for ACA/USDT 4h
2025-07-28 11:59:41,996 - advanced_data_fetcher - INFO - Fetching 1d data for ACA/USDT
2025-07-28 11:59:43,281 - advanced_data_fetcher - INFO - Cached data for ACA/USDT 1d
2025-07-28 11:59:43,281 - advanced_data_fetcher - INFO - Fetching 1h data for ACM/USDT
2025-07-28 11:59:55,315 - advanced_data_fetcher - INFO - Cached data for ACH/USDT 1h
2025-07-28 11:59:55,315 - advanced_data_fetcher - INFO - Fetching 4h data for ACH/USDT
2025-07-28 12:00:00,596 - advanced_data_fetcher - INFO - Cached data for ACH/USDT 4h
2025-07-28 12:00:00,596 - advanced_data_fetcher - INFO - Fetching 1d data for ACH/USDT
2025-07-28 12:00:01,765 - advanced_data_fetcher - INFO - Cached data for ACH/USDT 1d
2025-07-28 12:00:01,765 - advanced_data_fetcher - INFO - Fetching 1h data for ACT/USDT
2025-07-28 12:00:05,565 - advanced_data_fetcher - WARNING - Low quality data for ACT/USDT 1h: {'date_range_days': 258, 'min_required_days': 1460, 'actual_samples': 6216, 'expected_samples': 35040, 'completeness_ratio': 0.1773972602739726, 'gap_ratio': 0.0, 'avg_volume': np.float64(10070535.399372587), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:00:07,290 - advanced_data_fetcher - INFO - Cached data for ACT/USDT 1h
2025-07-28 12:00:07,290 - advanced_data_fetcher - INFO - Fetching 4h data for ACT/USDT
2025-07-28 12:00:07,521 - advanced_data_fetcher - INFO - Cached data for ACM/USDT 1h
2025-07-28 12:00:07,521 - advanced_data_fetcher - INFO - Fetching 4h data for ACM/USDT
2025-07-28 12:00:08,681 - advanced_data_fetcher - WARNING - Low quality data for ACT/USDT 4h: {'date_range_days': 259, 'min_required_days': 1460, 'actual_samples': 1555, 'expected_samples': 8760, 'completeness_ratio': 0.17751141552511415, 'gap_ratio': 0.0, 'avg_volume': np.float64(40256236.683279745), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:00:08,931 - advanced_data_fetcher - INFO - Cached data for ACT/USDT 4h
2025-07-28 12:00:08,931 - advanced_data_fetcher - INFO - Fetching 1d data for ACT/USDT
2025-07-28 12:00:09,381 - advanced_data_fetcher - WARNING - Low quality data for ACT/USDT 1d: {'date_range_days': 259, 'min_required_days': 1460, 'actual_samples': 260, 'expected_samples': 1460, 'completeness_ratio': 0.1780821917808219, 'gap_ratio': 0.0, 'avg_volume': np.float64(240763261.70192304), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:00:09,465 - advanced_data_fetcher - INFO - Cached data for ACT/USDT 1d
2025-07-28 12:00:09,480 - advanced_data_fetcher - INFO - Fetching 1h data for ACX/USDT
2025-07-28 12:00:12,381 - advanced_data_fetcher - WARNING - Low quality data for ACX/USDT 1h: {'date_range_days': 233, 'min_required_days': 1460, 'actual_samples': 5613, 'expected_samples': 35040, 'completeness_ratio': 0.16018835616438357, 'gap_ratio': 0.0, 'avg_volume': np.float64(740333.9962943167), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:00:13,901 - advanced_data_fetcher - INFO - Cached data for ACX/USDT 1h
2025-07-28 12:00:13,901 - advanced_data_fetcher - INFO - Fetching 4h data for ACX/USDT
2025-07-28 12:00:14,234 - advanced_data_fetcher - INFO - Cached data for ACM/USDT 4h
2025-07-28 12:00:14,234 - advanced_data_fetcher - INFO - Fetching 1d data for ACM/USDT
2025-07-28 12:00:14,908 - advanced_data_fetcher - WARNING - Low quality data for ACX/USDT 4h: {'date_range_days': 233, 'min_required_days': 1460, 'actual_samples': 1404, 'expected_samples': 8760, 'completeness_ratio': 0.16027397260273973, 'gap_ratio': 0.0, 'avg_volume': np.float64(2959754.074928775), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:00:15,129 - advanced_data_fetcher - INFO - Cached data for ACX/USDT 4h
2025-07-28 12:00:15,129 - advanced_data_fetcher - INFO - Fetching 1d data for ACX/USDT
2025-07-28 12:00:15,451 - advanced_data_fetcher - INFO - Cached data for ACM/USDT 1d
2025-07-28 12:00:15,451 - advanced_data_fetcher - INFO - Fetching 1h data for ADX/USDT
2025-07-28 12:00:15,565 - advanced_data_fetcher - WARNING - Low quality data for ACX/USDT 1d: {'date_range_days': 234, 'min_required_days': 1460, 'actual_samples': 235, 'expected_samples': 1460, 'completeness_ratio': 0.16095890410958905, 'gap_ratio': 0.0, 'avg_volume': np.float64(17682956.26042553), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:00:15,620 - advanced_data_fetcher - INFO - Cached data for ACX/USDT 1d
2025-07-28 12:00:15,620 - advanced_data_fetcher - INFO - Fetching 1h data for AEUR/USDT
2025-07-28 12:00:22,681 - advanced_data_fetcher - WARNING - Low quality data for AEUR/USDT 1h: {'date_range_days': 601, 'min_required_days': 1460, 'actual_samples': 14380, 'expected_samples': 35040, 'completeness_ratio': 0.4103881278538813, 'gap_ratio': 0.004706533776301218, 'avg_volume': np.float64(30761.968532684285), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:00:24,367 - advanced_data_fetcher - INFO - Cached data for AEUR/USDT 1h
2025-07-28 12:00:24,368 - advanced_data_fetcher - INFO - Fetching 4h data for AEUR/USDT
2025-07-28 12:00:26,415 - advanced_data_fetcher - WARNING - Low quality data for AEUR/USDT 4h: {'date_range_days': 602, 'min_required_days': 1460, 'actual_samples': 3597, 'expected_samples': 8760, 'completeness_ratio': 0.4106164383561644, 'gap_ratio': 0.004428452809299751, 'avg_volume': np.float64(122979.45718654434), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:00:26,881 - advanced_data_fetcher - INFO - Cached data for AEUR/USDT 4h
2025-07-28 12:00:26,896 - advanced_data_fetcher - INFO - Fetching 1d data for AEUR/USDT
2025-07-28 12:00:27,370 - advanced_data_fetcher - WARNING - Low quality data for AEUR/USDT 1d: {'date_range_days': 602, 'min_required_days': 1460, 'actual_samples': 601, 'expected_samples': 1460, 'completeness_ratio': 0.41164383561643836, 'gap_ratio': 0.003316749585406302, 'avg_volume': np.float64(736035.1206322795), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:00:27,515 - advanced_data_fetcher - INFO - Cached data for AEUR/USDT 1d
2025-07-28 12:00:27,515 - advanced_data_fetcher - INFO - Fetching 1h data for AEVO/USDT
2025-07-28 12:00:34,498 - advanced_data_fetcher - WARNING - Low quality data for AEVO/USDT 1h: {'date_range_days': 501, 'min_required_days': 1460, 'actual_samples': 12048, 'expected_samples': 35040, 'completeness_ratio': 0.34383561643835614, 'gap_ratio': 0.0, 'avg_volume': np.float64(1176282.6901684927), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:00:38,281 - advanced_data_fetcher - INFO - Cached data for ADX/USDT 1h
2025-07-28 12:00:38,281 - advanced_data_fetcher - INFO - Fetching 4h data for ADX/USDT
2025-07-28 12:00:38,479 - advanced_data_fetcher - INFO - Cached data for AEVO/USDT 1h
2025-07-28 12:00:38,479 - advanced_data_fetcher - INFO - Fetching 4h data for AEVO/USDT
2025-07-28 12:00:40,365 - advanced_data_fetcher - WARNING - Low quality data for AEVO/USDT 4h: {'date_range_days': 502, 'min_required_days': 1460, 'actual_samples': 3013, 'expected_samples': 8760, 'completeness_ratio': 0.3439497716894977, 'gap_ratio': 0.0, 'avg_volume': np.float64(4703569.150730169), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:00:40,774 - advanced_data_fetcher - INFO - Cached data for AEVO/USDT 4h
2025-07-28 12:00:40,774 - advanced_data_fetcher - INFO - Fetching 1d data for AEVO/USDT
2025-07-28 12:00:41,285 - advanced_data_fetcher - WARNING - Low quality data for AEVO/USDT 1d: {'date_range_days': 502, 'min_required_days': 1460, 'actual_samples': 503, 'expected_samples': 1460, 'completeness_ratio': 0.3445205479452055, 'gap_ratio': 0.0, 'avg_volume': np.float64(28174659.74383698), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:00:41,396 - advanced_data_fetcher - INFO - Cached data for AEVO/USDT 1d
2025-07-28 12:00:41,396 - advanced_data_fetcher - INFO - Fetching 1h data for AGLD/USDT
2025-07-28 12:00:43,696 - advanced_data_fetcher - INFO - Cached data for ADX/USDT 4h
2025-07-28 12:00:43,696 - advanced_data_fetcher - INFO - Fetching 1d data for ADX/USDT
2025-07-28 12:00:44,915 - advanced_data_fetcher - INFO - Cached data for ADX/USDT 1d
2025-07-28 12:00:44,915 - advanced_data_fetcher - INFO - Fetching 1h data for AI/USDT
2025-07-28 12:00:51,580 - advanced_data_fetcher - WARNING - Low quality data for AI/USDT 1h: {'date_range_days': 570, 'min_required_days': 1460, 'actual_samples': 13704, 'expected_samples': 35040, 'completeness_ratio': 0.3910958904109589, 'gap_ratio': 0.0, 'avg_volume': np.float64(866321.9329684763), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:00:53,331 - advanced_data_fetcher - INFO - Cached data for AI/USDT 1h
2025-07-28 12:00:53,331 - advanced_data_fetcher - INFO - Fetching 4h data for AI/USDT
2025-07-28 12:00:55,306 - advanced_data_fetcher - WARNING - Low quality data for AI/USDT 4h: {'date_range_days': 571, 'min_required_days': 1460, 'actual_samples': 3427, 'expected_samples': 8760, 'completeness_ratio': 0.39121004566210044, 'gap_ratio': 0.0, 'avg_volume': np.float64(3464276.6546542165), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:00:55,831 - advanced_data_fetcher - INFO - Cached data for AI/USDT 4h
2025-07-28 12:00:55,831 - advanced_data_fetcher - INFO - Fetching 1d data for AI/USDT
2025-07-28 12:00:56,316 - advanced_data_fetcher - WARNING - Low quality data for AI/USDT 1d: {'date_range_days': 571, 'min_required_days': 1460, 'actual_samples': 572, 'expected_samples': 1460, 'completeness_ratio': 0.3917808219178082, 'gap_ratio': 0.0, 'avg_volume': np.float64(20755377.868181817), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:00:56,431 - advanced_data_fetcher - INFO - Cached data for AI/USDT 1d
2025-07-28 12:00:56,431 - advanced_data_fetcher - INFO - Fetching 1h data for AIXBT/USDT
2025-07-28 12:00:59,465 - advanced_data_fetcher - WARNING - Low quality data for AIXBT/USDT 1h: {'date_range_days': 198, 'min_required_days': 1460, 'actual_samples': 4773, 'expected_samples': 35040, 'completeness_ratio': 0.13621575342465753, 'gap_ratio': 0.0, 'avg_volume': np.float64(5404100.980515399), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:01:01,281 - advanced_data_fetcher - INFO - Cached data for AIXBT/USDT 1h
2025-07-28 12:01:01,281 - advanced_data_fetcher - INFO - Fetching 4h data for AIXBT/USDT
2025-07-28 12:01:02,400 - advanced_data_fetcher - WARNING - Low quality data for AIXBT/USDT 4h: {'date_range_days': 198, 'min_required_days': 1460, 'actual_samples': 1194, 'expected_samples': 8760, 'completeness_ratio': 0.1363013698630137, 'gap_ratio': 0.0, 'avg_volume': np.float64(21602825.778894473), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:01:02,880 - advanced_data_fetcher - INFO - Cached data for AIXBT/USDT 4h
2025-07-28 12:01:02,880 - advanced_data_fetcher - INFO - Fetching 1d data for AIXBT/USDT
2025-07-28 12:01:03,389 - advanced_data_fetcher - WARNING - Low quality data for AIXBT/USDT 1d: {'date_range_days': 199, 'min_required_days': 1460, 'actual_samples': 200, 'expected_samples': 1460, 'completeness_ratio': 0.136986301369863, 'gap_ratio': 0.0, 'avg_volume': np.float64(128968869.9), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:01:03,504 - advanced_data_fetcher - INFO - Cached data for AIXBT/USDT 1d
2025-07-28 12:01:03,504 - advanced_data_fetcher - INFO - Fetching 1h data for ALCX/USDT
2025-07-28 12:01:03,641 - advanced_data_fetcher - INFO - Cached data for AGLD/USDT 1h
2025-07-28 12:01:03,641 - advanced_data_fetcher - INFO - Fetching 4h data for AGLD/USDT
2025-07-28 12:01:09,281 - advanced_data_fetcher - INFO - Cached data for AGLD/USDT 4h
2025-07-28 12:01:09,281 - advanced_data_fetcher - INFO - Fetching 1d data for AGLD/USDT
2025-07-28 12:01:10,515 - advanced_data_fetcher - INFO - Cached data for AGLD/USDT 1d
2025-07-28 12:01:10,515 - advanced_data_fetcher - INFO - Fetching 1h data for ALGO/USDT
2025-07-28 12:01:23,979 - advanced_data_fetcher - INFO - Cached data for ALCX/USDT 1h
2025-07-28 12:01:23,979 - advanced_data_fetcher - INFO - Fetching 4h data for ALCX/USDT
2025-07-28 12:01:29,997 - advanced_data_fetcher - INFO - Cached data for ALCX/USDT 4h
2025-07-28 12:01:29,997 - advanced_data_fetcher - INFO - Fetching 1d data for ALCX/USDT
2025-07-28 12:01:31,915 - advanced_data_fetcher - INFO - Cached data for ALCX/USDT 1d
2025-07-28 12:01:31,925 - advanced_data_fetcher - INFO - Fetching 1h data for ALICE/USDT
2025-07-28 12:01:34,507 - advanced_data_fetcher - INFO - Cached data for ALGO/USDT 1h
2025-07-28 12:01:34,507 - advanced_data_fetcher - INFO - Fetching 4h data for ALGO/USDT
2025-07-28 12:01:40,265 - advanced_data_fetcher - INFO - Cached data for ALGO/USDT 4h
2025-07-28 12:01:40,265 - advanced_data_fetcher - INFO - Fetching 1d data for ALGO/USDT
2025-07-28 12:01:41,446 - advanced_data_fetcher - INFO - Cached data for ALGO/USDT 1d
2025-07-28 12:01:41,446 - advanced_data_fetcher - INFO - Fetching 1h data for ALPINE/USDT
2025-07-28 12:01:54,735 - advanced_data_fetcher - INFO - Cached data for ALICE/USDT 1h
2025-07-28 12:01:54,736 - advanced_data_fetcher - INFO - Fetching 4h data for ALICE/USDT
2025-07-28 12:02:02,043 - advanced_data_fetcher - INFO - Cached data for ALPINE/USDT 1h
2025-07-28 12:02:02,043 - advanced_data_fetcher - INFO - Fetching 4h data for ALPINE/USDT
2025-07-28 12:02:02,216 - advanced_data_fetcher - INFO - Cached data for ALICE/USDT 4h
2025-07-28 12:02:02,217 - advanced_data_fetcher - INFO - Fetching 1d data for ALICE/USDT
2025-07-28 12:02:03,439 - advanced_data_fetcher - INFO - Cached data for ALICE/USDT 1d
2025-07-28 12:02:03,439 - advanced_data_fetcher - INFO - Fetching 1h data for ALT/USDT
2025-07-28 12:02:06,902 - advanced_data_fetcher - INFO - Cached data for ALPINE/USDT 4h
2025-07-28 12:02:06,902 - advanced_data_fetcher - INFO - Fetching 1d data for ALPINE/USDT
2025-07-28 12:02:08,078 - advanced_data_fetcher - INFO - Cached data for ALPINE/USDT 1d
2025-07-28 12:02:08,078 - advanced_data_fetcher - INFO - Fetching 1h data for AMP/USDT
2025-07-28 12:02:10,502 - advanced_data_fetcher - WARNING - Low quality data for ALT/USDT 1h: {'date_range_days': 549, 'min_required_days': 1460, 'actual_samples': 13200, 'expected_samples': 35040, 'completeness_ratio': 0.3767123287671233, 'gap_ratio': 0.0, 'avg_volume': np.float64(5436839.658712122), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:02:12,130 - advanced_data_fetcher - INFO - Cached data for ALT/USDT 1h
2025-07-28 12:02:12,130 - advanced_data_fetcher - INFO - Fetching 4h data for ALT/USDT
2025-07-28 12:02:14,031 - advanced_data_fetcher - WARNING - Low quality data for ALT/USDT 4h: {'date_range_days': 550, 'min_required_days': 1460, 'actual_samples': 3301, 'expected_samples': 8760, 'completeness_ratio': 0.37682648401826485, 'gap_ratio': 0.0, 'avg_volume': np.float64(21740772.648591336), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:02:14,517 - advanced_data_fetcher - INFO - Cached data for ALT/USDT 4h
2025-07-28 12:02:14,517 - advanced_data_fetcher - INFO - Fetching 1d data for ALT/USDT
2025-07-28 12:02:14,993 - advanced_data_fetcher - WARNING - Low quality data for ALT/USDT 1d: {'date_range_days': 550, 'min_required_days': 1460, 'actual_samples': 551, 'expected_samples': 1460, 'completeness_ratio': 0.3773972602739726, 'gap_ratio': 0.0, 'avg_volume': np.float64(130247351.20326678), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:02:15,138 - advanced_data_fetcher - INFO - Cached data for ALT/USDT 1d
2025-07-28 12:02:15,139 - advanced_data_fetcher - INFO - Fetching 1h data for ANIME/USDT
2025-07-28 12:02:17,555 - advanced_data_fetcher - WARNING - Low quality data for ANIME/USDT 1h: {'date_range_days': 185, 'min_required_days': 1460, 'actual_samples': 4459, 'expected_samples': 35040, 'completeness_ratio': 0.12725456621004566, 'gap_ratio': 0.0, 'avg_volume': np.float64(16974785.559116393), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:02:18,115 - advanced_data_fetcher - INFO - Cached data for ANIME/USDT 1h
2025-07-28 12:02:18,115 - advanced_data_fetcher - INFO - Fetching 4h data for ANIME/USDT
2025-07-28 12:02:19,031 - advanced_data_fetcher - WARNING - Low quality data for ANIME/USDT 4h: {'date_range_days': 185, 'min_required_days': 1460, 'actual_samples': 1116, 'expected_samples': 8760, 'completeness_ratio': 0.1273972602739726, 'gap_ratio': 0.0, 'avg_volume': np.float64(67823090.32983871), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:02:19,246 - advanced_data_fetcher - INFO - Cached data for ANIME/USDT 4h
2025-07-28 12:02:19,246 - advanced_data_fetcher - INFO - Fetching 1d data for ANIME/USDT
2025-07-28 12:02:19,690 - advanced_data_fetcher - WARNING - Low quality data for ANIME/USDT 1d: {'date_range_days': 186, 'min_required_days': 1460, 'actual_samples': 187, 'expected_samples': 1460, 'completeness_ratio': 0.12808219178082192, 'gap_ratio': 0.0, 'avg_volume': np.float64(404762400.04331553), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:02:19,751 - advanced_data_fetcher - INFO - Cached data for ANIME/USDT 1d
2025-07-28 12:02:19,752 - advanced_data_fetcher - INFO - Fetching 1h data for ANKR/USDT
2025-07-28 12:02:29,191 - advanced_data_fetcher - INFO - Cached data for AMP/USDT 1h
2025-07-28 12:02:29,191 - advanced_data_fetcher - INFO - Fetching 4h data for AMP/USDT
2025-07-28 12:03:04,514 - advanced_data_fetcher - INFO - Cached data for AMP/USDT 4h
2025-07-28 12:03:04,514 - advanced_data_fetcher - INFO - Fetching 1d data for AMP/USDT
2025-07-28 12:03:15,976 - advanced_data_fetcher - INFO - Cached data for AMP/USDT 1d
2025-07-28 12:03:15,977 - advanced_data_fetcher - INFO - Fetching 1h data for APE/USDT
2025-07-28 12:04:21,087 - advanced_data_fetcher - INFO - Cached data for ANKR/USDT 1h
2025-07-28 12:04:21,088 - advanced_data_fetcher - INFO - Fetching 4h data for ANKR/USDT
2025-07-28 12:05:07,314 - advanced_data_fetcher - INFO - Cached data for ANKR/USDT 4h
2025-07-28 12:05:07,329 - advanced_data_fetcher - INFO - Fetching 1d data for ANKR/USDT
2025-07-28 12:05:08,516 - advanced_data_fetcher - INFO - Cached data for ANKR/USDT 1d
2025-07-28 12:05:08,516 - advanced_data_fetcher - INFO - Fetching 1h data for API3/USDT
2025-07-28 12:05:19,769 - advanced_data_fetcher - INFO - Cached data for APE/USDT 1h
2025-07-28 12:05:19,775 - advanced_data_fetcher - INFO - Fetching 4h data for APE/USDT
2025-07-28 12:05:25,617 - advanced_data_fetcher - INFO - Cached data for APE/USDT 4h
2025-07-28 12:05:25,619 - advanced_data_fetcher - INFO - Fetching 1d data for APE/USDT
2025-07-28 12:05:27,115 - advanced_data_fetcher - INFO - Cached data for APE/USDT 1d
2025-07-28 12:05:27,115 - advanced_data_fetcher - INFO - Fetching 1h data for APT/USDT
2025-07-28 12:05:30,365 - advanced_data_fetcher - INFO - Cached data for API3/USDT 1h
2025-07-28 12:05:30,365 - advanced_data_fetcher - INFO - Fetching 4h data for API3/USDT
2025-07-28 12:05:36,632 - advanced_data_fetcher - INFO - Cached data for API3/USDT 4h
2025-07-28 12:05:36,632 - advanced_data_fetcher - INFO - Fetching 1d data for API3/USDT
2025-07-28 12:05:37,780 - advanced_data_fetcher - INFO - Cached data for API3/USDT 1d
2025-07-28 12:05:37,790 - advanced_data_fetcher - INFO - Fetching 1h data for AR/USDT
2025-07-28 12:05:40,650 - advanced_data_fetcher - WARNING - Low quality data for APT/USDT 1h: {'date_range_days': 1013, 'min_required_days': 1460, 'actual_samples': 24320, 'expected_samples': 35040, 'completeness_ratio': 0.6940639269406392, 'gap_ratio': 4.1116730397598784e-05, 'avg_volume': np.float64(233044.3494580592), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:05:44,336 - advanced_data_fetcher - INFO - Cached data for APT/USDT 1h
2025-07-28 12:05:44,336 - advanced_data_fetcher - INFO - Fetching 4h data for APT/USDT
2025-07-28 12:05:47,654 - advanced_data_fetcher - WARNING - Low quality data for APT/USDT 4h: {'date_range_days': 1013, 'min_required_days': 1460, 'actual_samples': 6081, 'expected_samples': 8760, 'completeness_ratio': 0.6941780821917808, 'gap_ratio': 0.0, 'avg_volume': np.float64(932024.1047525078), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:05:48,473 - advanced_data_fetcher - INFO - Cached data for APT/USDT 4h
2025-07-28 12:05:48,474 - advanced_data_fetcher - INFO - Fetching 1d data for APT/USDT
2025-07-28 12:05:49,393 - advanced_data_fetcher - WARNING - Low quality data for APT/USDT 1d: {'date_range_days': 1013, 'min_required_days': 1460, 'actual_samples': 1014, 'expected_samples': 1460, 'completeness_ratio': 0.6945205479452055, 'gap_ratio': 0.0, 'avg_volume': np.float64(5589387.160749507), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:05:49,554 - advanced_data_fetcher - INFO - Cached data for APT/USDT 1d
2025-07-28 12:05:49,554 - advanced_data_fetcher - INFO - Fetching 1h data for ARB/USDT
2025-07-28 12:06:00,762 - advanced_data_fetcher - WARNING - Low quality data for ARB/USDT 1h: {'date_range_days': 857, 'min_required_days': 1460, 'actual_samples': 20586, 'expected_samples': 35040, 'completeness_ratio': 0.5875, 'gap_ratio': 4.857434303201049e-05, 'avg_volume': np.float64(3389488.8929806664), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:06:00,957 - advanced_data_fetcher - INFO - Cached data for AR/USDT 1h
2025-07-28 12:06:00,957 - advanced_data_fetcher - INFO - Fetching 4h data for AR/USDT
2025-07-28 12:06:03,950 - advanced_data_fetcher - INFO - Cached data for ARB/USDT 1h
2025-07-28 12:06:03,950 - advanced_data_fetcher - INFO - Fetching 4h data for ARB/USDT
2025-07-28 12:07:31,366 - advanced_data_fetcher - WARNING - Low quality data for ARB/USDT 4h: {'date_range_days': 857, 'min_required_days': 1460, 'actual_samples': 5148, 'expected_samples': 8760, 'completeness_ratio': 0.5876712328767123, 'gap_ratio': 0.0, 'avg_volume': np.float64(13554059.653710179), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:07:32,386 - advanced_data_fetcher - INFO - Cached data for ARB/USDT 4h
2025-07-28 12:07:32,387 - advanced_data_fetcher - INFO - Fetching 1d data for ARB/USDT
2025-07-28 12:07:33,341 - advanced_data_fetcher - INFO - Cached data for AR/USDT 4h
2025-07-28 12:07:33,341 - advanced_data_fetcher - WARNING - Low quality data for ARB/USDT 1d: {'date_range_days': 858, 'min_required_days': 1460, 'actual_samples': 859, 'expected_samples': 1460, 'completeness_ratio': 0.5883561643835616, 'gap_ratio': 0.0, 'avg_volume': np.float64(81229697.80908033), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:07:33,341 - advanced_data_fetcher - INFO - Fetching 1d data for AR/USDT
2025-07-28 12:07:33,489 - advanced_data_fetcher - INFO - Cached data for ARB/USDT 1d
2025-07-28 12:07:33,489 - advanced_data_fetcher - INFO - Fetching 1h data for ARDR/USDT
2025-07-28 12:07:35,384 - advanced_data_fetcher - INFO - Cached data for AR/USDT 1d
2025-07-28 12:07:35,384 - advanced_data_fetcher - INFO - Fetching 1h data for ARK/USDT
2025-07-28 12:07:45,000 - advanced_data_fetcher - WARNING - Low quality data for ARK/USDT 1h: {'date_range_days': 675, 'min_required_days': 1460, 'actual_samples': 16202, 'expected_samples': 35040, 'completeness_ratio': 0.46238584474885847, 'gap_ratio': 0.0, 'avg_volume': np.float64(398334.07134921616), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:07:46,915 - advanced_data_fetcher - INFO - Cached data for ARK/USDT 1h
2025-07-28 12:07:46,915 - advanced_data_fetcher - INFO - Fetching 4h data for ARK/USDT
2025-07-28 12:07:49,300 - advanced_data_fetcher - WARNING - Low quality data for ARK/USDT 4h: {'date_range_days': 675, 'min_required_days': 1460, 'actual_samples': 4051, 'expected_samples': 8760, 'completeness_ratio': 0.46244292237442924, 'gap_ratio': 0.0, 'avg_volume': np.float64(1593139.6257714145), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:07:49,915 - advanced_data_fetcher - INFO - Cached data for ARK/USDT 4h
2025-07-28 12:07:49,915 - advanced_data_fetcher - INFO - Fetching 1d data for ARK/USDT
2025-07-28 12:07:50,400 - advanced_data_fetcher - WARNING - Low quality data for ARK/USDT 1d: {'date_range_days': 675, 'min_required_days': 1460, 'actual_samples': 676, 'expected_samples': 1460, 'completeness_ratio': 0.46301369863013697, 'gap_ratio': 0.0, 'avg_volume': np.float64(9547054.177514793), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:07:50,515 - advanced_data_fetcher - INFO - Cached data for ARK/USDT 1d
2025-07-28 12:07:50,515 - advanced_data_fetcher - INFO - Fetching 1h data for ARKM/USDT
2025-07-28 12:07:58,500 - advanced_data_fetcher - INFO - Cached data for ARDR/USDT 1h
2025-07-28 12:07:58,500 - advanced_data_fetcher - INFO - Fetching 4h data for ARDR/USDT
2025-07-28 12:08:00,550 - advanced_data_fetcher - WARNING - Low quality data for ARKM/USDT 1h: {'date_range_days': 740, 'min_required_days': 1460, 'actual_samples': 17782, 'expected_samples': 35040, 'completeness_ratio': 0.5074771689497717, 'gap_ratio': 0.0, 'avg_volume': np.float64(824659.7177764031), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:08:02,950 - advanced_data_fetcher - INFO - Cached data for ARKM/USDT 1h
2025-07-28 12:08:02,950 - advanced_data_fetcher - INFO - Fetching 4h data for ARKM/USDT
2025-07-28 12:08:04,550 - advanced_data_fetcher - INFO - Cached data for ARDR/USDT 4h
2025-07-28 12:08:04,550 - advanced_data_fetcher - INFO - Fetching 1d data for ARDR/USDT
2025-07-28 12:08:05,988 - advanced_data_fetcher - WARNING - Low quality data for ARKM/USDT 4h: {'date_range_days': 740, 'min_required_days': 1460, 'actual_samples': 4446, 'expected_samples': 8760, 'completeness_ratio': 0.5075342465753425, 'gap_ratio': 0.0, 'avg_volume': np.float64(3298267.904071075), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:08:06,475 - advanced_data_fetcher - INFO - Cached data for ARDR/USDT 1d
2025-07-28 12:08:06,476 - advanced_data_fetcher - INFO - Fetching 1h data for ARPA/USDT
2025-07-28 12:08:06,900 - advanced_data_fetcher - INFO - Cached data for ARKM/USDT 4h
2025-07-28 12:08:06,900 - advanced_data_fetcher - INFO - Fetching 1d data for ARKM/USDT
2025-07-28 12:08:07,382 - advanced_data_fetcher - WARNING - Low quality data for ARKM/USDT 1d: {'date_range_days': 741, 'min_required_days': 1460, 'actual_samples': 742, 'expected_samples': 1460, 'completeness_ratio': 0.5082191780821917, 'gap_ratio': 0.0, 'avg_volume': np.float64(19762936.794474393), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:08:07,567 - advanced_data_fetcher - INFO - Cached data for ARKM/USDT 1d
2025-07-28 12:08:07,567 - advanced_data_fetcher - INFO - Fetching 1h data for ASR/USDT
2025-07-28 12:08:34,131 - advanced_data_fetcher - INFO - Cached data for ASR/USDT 1h
2025-07-28 12:08:34,150 - advanced_data_fetcher - INFO - Fetching 4h data for ASR/USDT
2025-07-28 12:08:34,482 - advanced_data_fetcher - INFO - Cached data for ARPA/USDT 1h
2025-07-28 12:08:34,483 - advanced_data_fetcher - INFO - Fetching 4h data for ARPA/USDT
2025-07-28 12:08:40,668 - advanced_data_fetcher - INFO - Cached data for ASR/USDT 4h
2025-07-28 12:08:40,671 - advanced_data_fetcher - INFO - Fetching 1d data for ASR/USDT
2025-07-28 12:08:41,300 - advanced_data_fetcher - INFO - Cached data for ARPA/USDT 4h
2025-07-28 12:08:41,300 - advanced_data_fetcher - INFO - Fetching 1d data for ARPA/USDT
2025-07-28 12:08:41,937 - advanced_data_fetcher - INFO - Cached data for ASR/USDT 1d
2025-07-28 12:08:41,937 - advanced_data_fetcher - INFO - Fetching 1h data for ASTR/USDT
2025-07-28 12:08:42,538 - advanced_data_fetcher - INFO - Cached data for ARPA/USDT 1d
2025-07-28 12:08:42,538 - advanced_data_fetcher - INFO - Fetching 1h data for ATA/USDT
2025-07-28 12:09:01,325 - advanced_data_fetcher - INFO - Cached data for ASTR/USDT 1h
2025-07-28 12:09:01,325 - advanced_data_fetcher - INFO - Fetching 4h data for ASTR/USDT
2025-07-28 12:09:05,665 - advanced_data_fetcher - INFO - Cached data for ATA/USDT 1h
2025-07-28 12:09:05,665 - advanced_data_fetcher - INFO - Fetching 4h data for ATA/USDT
2025-07-28 12:09:06,796 - advanced_data_fetcher - INFO - Cached data for ASTR/USDT 4h
2025-07-28 12:09:06,800 - advanced_data_fetcher - INFO - Fetching 1d data for ASTR/USDT
2025-07-28 12:09:07,920 - advanced_data_fetcher - INFO - Cached data for ASTR/USDT 1d
2025-07-28 12:09:07,920 - advanced_data_fetcher - INFO - Fetching 1h data for ATM/USDT
2025-07-28 12:09:11,650 - advanced_data_fetcher - INFO - Cached data for ATA/USDT 4h
2025-07-28 12:09:11,650 - advanced_data_fetcher - INFO - Fetching 1d data for ATA/USDT
2025-07-28 12:09:12,991 - advanced_data_fetcher - INFO - Cached data for ATA/USDT 1d
2025-07-28 12:09:12,991 - advanced_data_fetcher - INFO - Fetching 1h data for ATOM/USDT
2025-07-28 12:09:30,550 - advanced_data_fetcher - INFO - Cached data for ATM/USDT 1h
2025-07-28 12:09:30,550 - advanced_data_fetcher - INFO - Fetching 4h data for ATM/USDT
2025-07-28 12:09:35,915 - advanced_data_fetcher - INFO - Cached data for ATOM/USDT 1h
2025-07-28 12:09:35,915 - advanced_data_fetcher - INFO - Fetching 4h data for ATOM/USDT
2025-07-28 12:09:37,054 - advanced_data_fetcher - INFO - Cached data for ATM/USDT 4h
2025-07-28 12:09:37,065 - advanced_data_fetcher - INFO - Fetching 1d data for ATM/USDT
2025-07-28 12:09:38,260 - advanced_data_fetcher - INFO - Cached data for ATM/USDT 1d
2025-07-28 12:09:38,260 - advanced_data_fetcher - INFO - Fetching 1h data for AUCTION/USDT
2025-07-28 12:09:41,894 - advanced_data_fetcher - INFO - Cached data for ATOM/USDT 4h
2025-07-28 12:09:41,894 - advanced_data_fetcher - INFO - Fetching 1d data for ATOM/USDT
2025-07-28 12:09:43,125 - advanced_data_fetcher - INFO - Cached data for ATOM/USDT 1d
2025-07-28 12:09:43,125 - advanced_data_fetcher - INFO - Fetching 1h data for AUDIO/USDT
2025-07-28 12:09:58,581 - advanced_data_fetcher - INFO - Cached data for AUCTION/USDT 1h
2025-07-28 12:09:58,581 - advanced_data_fetcher - INFO - Fetching 4h data for AUCTION/USDT
2025-07-28 12:10:05,596 - advanced_data_fetcher - INFO - Cached data for AUCTION/USDT 4h
2025-07-28 12:10:05,600 - advanced_data_fetcher - INFO - Fetching 1d data for AUCTION/USDT
2025-07-28 12:10:07,091 - advanced_data_fetcher - INFO - Cached data for AUCTION/USDT 1d
2025-07-28 12:10:07,091 - advanced_data_fetcher - INFO - Fetching 1h data for AVA/USDT
2025-07-28 12:10:07,775 - advanced_data_fetcher - INFO - Cached data for AUDIO/USDT 1h
2025-07-28 12:10:07,775 - advanced_data_fetcher - INFO - Fetching 4h data for AUDIO/USDT
2025-07-28 12:10:14,966 - advanced_data_fetcher - INFO - Cached data for AUDIO/USDT 4h
2025-07-28 12:10:14,966 - advanced_data_fetcher - INFO - Fetching 1d data for AUDIO/USDT
2025-07-28 12:10:16,165 - advanced_data_fetcher - INFO - Cached data for AUDIO/USDT 1d
2025-07-28 12:10:16,165 - advanced_data_fetcher - INFO - Fetching 1h data for AWE/USDT
2025-07-28 12:10:17,131 - advanced_data_fetcher - WARNING - Low quality data for AWE/USDT 1h: {'date_range_days': 68, 'min_required_days': 1460, 'actual_samples': 1634, 'expected_samples': 35040, 'completeness_ratio': 0.0466324200913242, 'gap_ratio': 0.0, 'avg_volume': np.float64(1328806.70501836), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:10:17,419 - advanced_data_fetcher - INFO - Cached data for AWE/USDT 1h
2025-07-28 12:10:17,419 - advanced_data_fetcher - INFO - Fetching 4h data for AWE/USDT
2025-07-28 12:10:17,865 - advanced_data_fetcher - WARNING - Low quality data for AWE/USDT 4h: {'date_range_days': 68, 'min_required_days': 1460, 'actual_samples': 409, 'expected_samples': 8760, 'completeness_ratio': 0.04668949771689498, 'gap_ratio': 0.0, 'avg_volume': np.float64(5308728.987775061), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:10:17,981 - advanced_data_fetcher - INFO - Cached data for AWE/USDT 4h
2025-07-28 12:10:17,981 - advanced_data_fetcher - INFO - Fetching 1d data for AWE/USDT
2025-07-28 12:10:18,450 - advanced_data_fetcher - WARNING - Low quality data for AWE/USDT 1d: {'date_range_days': 68, 'min_required_days': 1460, 'actual_samples': 69, 'expected_samples': 1460, 'completeness_ratio': 0.04726027397260274, 'gap_ratio': 0.0, 'avg_volume': np.float64(31467683.420289855), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:10:18,582 - advanced_data_fetcher - INFO - Cached data for AWE/USDT 1d
2025-07-28 12:10:18,582 - advanced_data_fetcher - INFO - Fetching 1h data for AXL/USDT
2025-07-28 12:10:24,815 - advanced_data_fetcher - WARNING - Low quality data for AXL/USDT 1h: {'date_range_days': 513, 'min_required_days': 1460, 'actual_samples': 12335, 'expected_samples': 35040, 'completeness_ratio': 0.3520262557077626, 'gap_ratio': 0.0, 'avg_volume': np.float64(371579.31040535064), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:10:27,950 - advanced_data_fetcher - INFO - Cached data for AXL/USDT 1h
2025-07-28 12:10:27,962 - advanced_data_fetcher - INFO - Fetching 4h data for AXL/USDT
2025-07-28 12:10:30,065 - advanced_data_fetcher - WARNING - Low quality data for AXL/USDT 4h: {'date_range_days': 514, 'min_required_days': 1460, 'actual_samples': 3085, 'expected_samples': 8760, 'completeness_ratio': 0.3521689497716895, 'gap_ratio': 0.0, 'avg_volume': np.float64(1485715.0254262562), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:10:31,579 - advanced_data_fetcher - INFO - Cached data for AXL/USDT 4h
2025-07-28 12:10:31,582 - advanced_data_fetcher - INFO - Fetching 1d data for AXL/USDT
2025-07-28 12:10:31,750 - advanced_data_fetcher - INFO - Cached data for AVA/USDT 1h
2025-07-28 12:10:31,750 - advanced_data_fetcher - INFO - Fetching 4h data for AVA/USDT
2025-07-28 12:10:32,058 - advanced_data_fetcher - WARNING - Low quality data for AXL/USDT 1d: {'date_range_days': 514, 'min_required_days': 1460, 'actual_samples': 515, 'expected_samples': 1460, 'completeness_ratio': 0.3527397260273973, 'gap_ratio': 0.0, 'avg_volume': np.float64(8899865.73483495), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:10:32,271 - advanced_data_fetcher - INFO - Cached data for AXL/USDT 1d
2025-07-28 12:10:32,279 - advanced_data_fetcher - INFO - Fetching 1h data for AXS/USDT
2025-07-28 12:10:37,650 - advanced_data_fetcher - INFO - Cached data for AVA/USDT 4h
2025-07-28 12:10:37,650 - advanced_data_fetcher - INFO - Fetching 1d data for AVA/USDT
2025-07-28 12:10:38,775 - advanced_data_fetcher - INFO - Cached data for AVA/USDT 1d
2025-07-28 12:10:38,775 - advanced_data_fetcher - INFO - Fetching 1h data for BABY/USDT
2025-07-28 12:10:40,215 - advanced_data_fetcher - WARNING - Low quality data for BABY/USDT 1h: {'date_range_days': 108, 'min_required_days': 1460, 'actual_samples': 2615, 'expected_samples': 35040, 'completeness_ratio': 0.07462899543378995, 'gap_ratio': 0.0, 'avg_volume': np.float64(6320784.248183557), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:10:40,594 - advanced_data_fetcher - INFO - Cached data for BABY/USDT 1h
2025-07-28 12:10:40,594 - advanced_data_fetcher - INFO - Fetching 4h data for BABY/USDT
2025-07-28 12:10:41,300 - advanced_data_fetcher - WARNING - Low quality data for BABY/USDT 4h: {'date_range_days': 109, 'min_required_days': 1460, 'actual_samples': 655, 'expected_samples': 8760, 'completeness_ratio': 0.0747716894977169, 'gap_ratio': 0.0, 'avg_volume': np.float64(25234887.235114504), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:10:41,431 - advanced_data_fetcher - INFO - Cached data for BABY/USDT 4h
2025-07-28 12:10:41,431 - advanced_data_fetcher - INFO - Fetching 1d data for BABY/USDT
2025-07-28 12:10:42,001 - advanced_data_fetcher - WARNING - Low quality data for BABY/USDT 1d: {'date_range_days': 109, 'min_required_days': 1460, 'actual_samples': 110, 'expected_samples': 1460, 'completeness_ratio': 0.07534246575342465, 'gap_ratio': 0.0, 'avg_volume': np.float64(150262283.8909091), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:10:42,100 - advanced_data_fetcher - INFO - Cached data for BABY/USDT 1d
2025-07-28 12:10:42,100 - advanced_data_fetcher - INFO - Fetching 1h data for BAKE/USDT
2025-07-28 12:10:54,915 - advanced_data_fetcher - INFO - Cached data for AXS/USDT 1h
2025-07-28 12:10:54,915 - advanced_data_fetcher - INFO - Fetching 4h data for AXS/USDT
2025-07-28 12:11:01,531 - advanced_data_fetcher - INFO - Cached data for AXS/USDT 4h
2025-07-28 12:11:01,533 - advanced_data_fetcher - INFO - Fetching 1d data for AXS/USDT
2025-07-28 12:11:03,200 - advanced_data_fetcher - INFO - Cached data for AXS/USDT 1d
2025-07-28 12:11:03,200 - advanced_data_fetcher - INFO - Fetching 1h data for BANANA/USDT
2025-07-28 12:11:05,775 - advanced_data_fetcher - INFO - Cached data for BAKE/USDT 1h
2025-07-28 12:11:05,775 - advanced_data_fetcher - INFO - Fetching 4h data for BAKE/USDT
2025-07-28 12:11:07,931 - advanced_data_fetcher - WARNING - Low quality data for BANANA/USDT 1h: {'date_range_days': 373, 'min_required_days': 1460, 'actual_samples': 8953, 'expected_samples': 35040, 'completeness_ratio': 0.2555079908675799, 'gap_ratio': 0.0, 'avg_volume': np.float64(11389.618944934658), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:11:09,081 - advanced_data_fetcher - INFO - Cached data for BANANA/USDT 1h
2025-07-28 12:11:09,081 - advanced_data_fetcher - INFO - Fetching 4h data for BANANA/USDT
2025-07-28 12:11:10,881 - advanced_data_fetcher - WARNING - Low quality data for BANANA/USDT 4h: {'date_range_days': 373, 'min_required_days': 1460, 'actual_samples': 2239, 'expected_samples': 8760, 'completeness_ratio': 0.25559360730593605, 'gap_ratio': 0.0, 'avg_volume': np.float64(45543.22604466279), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:11:11,950 - advanced_data_fetcher - INFO - Cached data for BANANA/USDT 4h
2025-07-28 12:11:11,950 - advanced_data_fetcher - INFO - Fetching 1d data for BANANA/USDT
2025-07-28 12:11:12,281 - advanced_data_fetcher - INFO - Cached data for BAKE/USDT 4h
2025-07-28 12:11:12,281 - advanced_data_fetcher - INFO - Fetching 1d data for BAKE/USDT
2025-07-28 12:11:12,445 - advanced_data_fetcher - WARNING - Low quality data for BANANA/USDT 1d: {'date_range_days': 373, 'min_required_days': 1460, 'actual_samples': 374, 'expected_samples': 1460, 'completeness_ratio': 0.25616438356164384, 'gap_ratio': 0.0, 'avg_volume': np.float64(272650.4896096257), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:11:12,531 - advanced_data_fetcher - INFO - Cached data for BANANA/USDT 1d
2025-07-28 12:11:12,531 - advanced_data_fetcher - INFO - Fetching 1h data for BANANAS31/USDT
2025-07-28 12:11:13,465 - advanced_data_fetcher - INFO - Cached data for BAKE/USDT 1d
2025-07-28 12:11:13,465 - advanced_data_fetcher - INFO - Fetching 1h data for BAND/USDT
2025-07-28 12:11:13,995 - advanced_data_fetcher - WARNING - Low quality data for BANANAS31/USDT 1h: {'date_range_days': 122, 'min_required_days': 1460, 'actual_samples': 2941, 'expected_samples': 35040, 'completeness_ratio': 0.08393264840182649, 'gap_ratio': 0.0, 'avg_volume': np.float64(51819197.18803128), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:11:14,429 - advanced_data_fetcher - INFO - Cached data for BANANAS31/USDT 1h
2025-07-28 12:11:14,429 - advanced_data_fetcher - INFO - Fetching 4h data for BANANAS31/USDT
2025-07-28 12:11:14,915 - advanced_data_fetcher - WARNING - Low quality data for BANANAS31/USDT 4h: {'date_range_days': 122, 'min_required_days': 1460, 'actual_samples': 736, 'expected_samples': 8760, 'completeness_ratio': 0.08401826484018265, 'gap_ratio': 0.0, 'avg_volume': np.float64(207065569.19836956), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:11:15,073 - advanced_data_fetcher - INFO - Cached data for BANANAS31/USDT 4h
2025-07-28 12:11:15,073 - advanced_data_fetcher - INFO - Fetching 1d data for BANANAS31/USDT
2025-07-28 12:11:15,515 - advanced_data_fetcher - WARNING - Low quality data for BANANAS31/USDT 1d: {'date_range_days': 123, 'min_required_days': 1460, 'actual_samples': 124, 'expected_samples': 1460, 'completeness_ratio': 0.08493150684931507, 'gap_ratio': 0.0, 'avg_volume': np.float64(1229034346.2096775), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:11:15,615 - advanced_data_fetcher - INFO - Cached data for BANANAS31/USDT 1d
2025-07-28 12:11:15,615 - advanced_data_fetcher - INFO - Fetching 1h data for BAR/USDT
2025-07-28 12:11:36,865 - advanced_data_fetcher - INFO - Cached data for BAND/USDT 1h
2025-07-28 12:11:36,875 - advanced_data_fetcher - INFO - Fetching 4h data for BAND/USDT
2025-07-28 12:11:40,315 - advanced_data_fetcher - INFO - Cached data for BAR/USDT 1h
2025-07-28 12:11:40,315 - advanced_data_fetcher - INFO - Fetching 4h data for BAR/USDT
2025-07-28 12:11:42,700 - advanced_data_fetcher - INFO - Cached data for BAND/USDT 4h
2025-07-28 12:11:42,700 - advanced_data_fetcher - INFO - Fetching 1d data for BAND/USDT
2025-07-28 12:11:43,865 - advanced_data_fetcher - INFO - Cached data for BAND/USDT 1d
2025-07-28 12:11:43,875 - advanced_data_fetcher - INFO - Fetching 1h data for BAT/USDT
2025-07-28 12:11:46,266 - advanced_data_fetcher - INFO - Cached data for BAR/USDT 4h
2025-07-28 12:11:46,266 - advanced_data_fetcher - INFO - Fetching 1d data for BAR/USDT
2025-07-28 12:11:47,515 - advanced_data_fetcher - INFO - Cached data for BAR/USDT 1d
2025-07-28 12:11:47,515 - advanced_data_fetcher - INFO - Fetching 1h data for BB/USDT
2025-07-28 12:11:53,065 - advanced_data_fetcher - WARNING - Low quality data for BB/USDT 1h: {'date_range_days': 440, 'min_required_days': 1460, 'actual_samples': 10584, 'expected_samples': 35040, 'completeness_ratio': 0.30205479452054795, 'gap_ratio': 0.0, 'avg_volume': np.float64(1735774.2676587303), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:11:54,422 - advanced_data_fetcher - INFO - Cached data for BB/USDT 1h
2025-07-28 12:11:54,423 - advanced_data_fetcher - INFO - Fetching 4h data for BB/USDT
2025-07-28 12:11:56,131 - advanced_data_fetcher - WARNING - Low quality data for BB/USDT 4h: {'date_range_days': 441, 'min_required_days': 1460, 'actual_samples': 2647, 'expected_samples': 8760, 'completeness_ratio': 0.3021689497716895, 'gap_ratio': 0.0, 'avg_volume': np.float64(6940474.064563657), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:11:56,500 - advanced_data_fetcher - INFO - Cached data for BB/USDT 4h
2025-07-28 12:11:56,500 - advanced_data_fetcher - INFO - Fetching 1d data for BB/USDT
2025-07-28 12:11:56,976 - advanced_data_fetcher - WARNING - Low quality data for BB/USDT 1d: {'date_range_days': 441, 'min_required_days': 1460, 'actual_samples': 442, 'expected_samples': 1460, 'completeness_ratio': 0.30273972602739724, 'gap_ratio': 0.0, 'avg_volume': np.float64(41564332.23733032), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:11:57,065 - advanced_data_fetcher - INFO - Cached data for BB/USDT 1d
2025-07-28 12:11:57,065 - advanced_data_fetcher - INFO - Fetching 1h data for BCH/USDT
2025-07-28 12:12:06,700 - advanced_data_fetcher - INFO - Cached data for BAT/USDT 1h
2025-07-28 12:12:06,700 - advanced_data_fetcher - INFO - Fetching 4h data for BAT/USDT
2025-07-28 12:12:12,515 - advanced_data_fetcher - INFO - Cached data for BAT/USDT 4h
2025-07-28 12:12:12,515 - advanced_data_fetcher - INFO - Fetching 1d data for BAT/USDT
2025-07-28 12:12:13,700 - advanced_data_fetcher - INFO - Cached data for BAT/USDT 1d
2025-07-28 12:12:13,700 - advanced_data_fetcher - INFO - Fetching 1h data for BEAMX/USDT
2025-07-28 12:12:21,012 - advanced_data_fetcher - INFO - Cached data for BCH/USDT 1h
2025-07-28 12:12:21,014 - advanced_data_fetcher - INFO - Fetching 4h data for BCH/USDT
2025-07-28 12:12:21,765 - advanced_data_fetcher - WARNING - Low quality data for BEAMX/USDT 1h: {'date_range_days': 622, 'min_required_days': 1460, 'actual_samples': 14930, 'expected_samples': 35040, 'completeness_ratio': 0.4260844748858447, 'gap_ratio': 0.0, 'avg_volume': np.float64(31131336.505626258), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:12:23,615 - advanced_data_fetcher - INFO - Cached data for BEAMX/USDT 1h
2025-07-28 12:12:23,615 - advanced_data_fetcher - INFO - Fetching 4h data for BEAMX/USDT
2025-07-28 12:12:25,550 - advanced_data_fetcher - WARNING - Low quality data for BEAMX/USDT 4h: {'date_range_days': 622, 'min_required_days': 1460, 'actual_samples': 3733, 'expected_samples': 8760, 'completeness_ratio': 0.4261415525114155, 'gap_ratio': 0.0, 'avg_volume': np.float64(124508689.93544066), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:12:26,750 - advanced_data_fetcher - INFO - Cached data for BEAMX/USDT 4h
2025-07-28 12:12:26,764 - advanced_data_fetcher - INFO - Fetching 1d data for BEAMX/USDT
2025-07-28 12:12:27,365 - advanced_data_fetcher - INFO - Cached data for BCH/USDT 4h
2025-07-28 12:12:27,365 - advanced_data_fetcher - INFO - Fetching 1d data for BCH/USDT
2025-07-28 12:12:27,415 - advanced_data_fetcher - WARNING - Low quality data for BEAMX/USDT 1d: {'date_range_days': 622, 'min_required_days': 1460, 'actual_samples': 623, 'expected_samples': 1460, 'completeness_ratio': 0.4267123287671233, 'gap_ratio': 0.0, 'avg_volume': np.float64(746052872.4382023), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:12:27,525 - advanced_data_fetcher - INFO - Cached data for BEAMX/USDT 1d
2025-07-28 12:12:27,525 - advanced_data_fetcher - INFO - Fetching 1h data for BEL/USDT
2025-07-28 12:12:28,615 - advanced_data_fetcher - INFO - Cached data for BCH/USDT 1d
2025-07-28 12:12:28,615 - advanced_data_fetcher - INFO - Fetching 1h data for BERA/USDT
2025-07-28 12:12:31,181 - advanced_data_fetcher - WARNING - Low quality data for BERA/USDT 1h: {'date_range_days': 171, 'min_required_days': 1460, 'actual_samples': 4125, 'expected_samples': 35040, 'completeness_ratio': 0.11772260273972603, 'gap_ratio': 0.0, 'avg_volume': np.float64(256870.4328780606), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:12:31,765 - advanced_data_fetcher - INFO - Cached data for BERA/USDT 1h
2025-07-28 12:12:31,765 - advanced_data_fetcher - INFO - Fetching 4h data for BERA/USDT
2025-07-28 12:12:32,675 - advanced_data_fetcher - WARNING - Low quality data for BERA/USDT 4h: {'date_range_days': 171, 'min_required_days': 1460, 'actual_samples': 1032, 'expected_samples': 8760, 'completeness_ratio': 0.1178082191780822, 'gap_ratio': 0.0, 'avg_volume': np.float64(1026735.0151375969), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:12:32,904 - advanced_data_fetcher - INFO - Cached data for BERA/USDT 4h
2025-07-28 12:12:32,904 - advanced_data_fetcher - INFO - Fetching 1d data for BERA/USDT
2025-07-28 12:12:33,382 - advanced_data_fetcher - WARNING - Low quality data for BERA/USDT 1d: {'date_range_days': 172, 'min_required_days': 1460, 'actual_samples': 173, 'expected_samples': 1460, 'completeness_ratio': 0.1184931506849315, 'gap_ratio': 0.0, 'avg_volume': np.float64(6124800.783942196), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:12:33,472 - advanced_data_fetcher - INFO - Cached data for BERA/USDT 1d
2025-07-28 12:12:33,472 - advanced_data_fetcher - INFO - Fetching 1h data for BICO/USDT
2025-07-28 12:12:52,181 - advanced_data_fetcher - INFO - Cached data for BEL/USDT 1h
2025-07-28 12:12:52,181 - advanced_data_fetcher - INFO - Fetching 4h data for BEL/USDT
2025-07-28 12:12:56,177 - advanced_data_fetcher - INFO - Cached data for BICO/USDT 1h
2025-07-28 12:12:56,177 - advanced_data_fetcher - INFO - Fetching 4h data for BICO/USDT
2025-07-28 12:12:58,278 - advanced_data_fetcher - INFO - Cached data for BEL/USDT 4h
2025-07-28 12:12:58,278 - advanced_data_fetcher - INFO - Fetching 1d data for BEL/USDT
2025-07-28 12:12:59,435 - advanced_data_fetcher - INFO - Cached data for BEL/USDT 1d
2025-07-28 12:12:59,435 - advanced_data_fetcher - INFO - Fetching 1h data for BIFI/USDT
2025-07-28 12:13:01,776 - advanced_data_fetcher - INFO - Cached data for BICO/USDT 4h
2025-07-28 12:13:01,776 - advanced_data_fetcher - INFO - Fetching 1d data for BICO/USDT
2025-07-28 12:13:02,975 - advanced_data_fetcher - INFO - Cached data for BICO/USDT 1d
2025-07-28 12:13:02,975 - advanced_data_fetcher - INFO - Fetching 1h data for BIGTIME/USDT
2025-07-28 12:13:04,401 - advanced_data_fetcher - WARNING - Low quality data for BIGTIME/USDT 1h: {'date_range_days': 107, 'min_required_days': 1460, 'actual_samples': 2588, 'expected_samples': 35040, 'completeness_ratio': 0.07385844748858447, 'gap_ratio': 0.0, 'avg_volume': np.float64(1764192.0011591963), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:04,760 - advanced_data_fetcher - INFO - Cached data for BIGTIME/USDT 1h
2025-07-28 12:13:04,760 - advanced_data_fetcher - INFO - Fetching 4h data for BIGTIME/USDT
2025-07-28 12:13:05,251 - advanced_data_fetcher - WARNING - Low quality data for BIGTIME/USDT 4h: {'date_range_days': 107, 'min_required_days': 1460, 'actual_samples': 648, 'expected_samples': 8760, 'completeness_ratio': 0.07397260273972603, 'gap_ratio': 0.0, 'avg_volume': np.float64(7045877.930555556), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:05,392 - advanced_data_fetcher - INFO - Cached data for BIGTIME/USDT 4h
2025-07-28 12:13:05,392 - advanced_data_fetcher - INFO - Fetching 1d data for BIGTIME/USDT
2025-07-28 12:13:05,865 - advanced_data_fetcher - WARNING - Low quality data for BIGTIME/USDT 1d: {'date_range_days': 108, 'min_required_days': 1460, 'actual_samples': 109, 'expected_samples': 1460, 'completeness_ratio': 0.07465753424657534, 'gap_ratio': 0.0, 'avg_volume': np.float64(41887421.09174312), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:05,950 - advanced_data_fetcher - INFO - Cached data for BIGTIME/USDT 1d
2025-07-28 12:13:05,950 - advanced_data_fetcher - INFO - Fetching 1h data for BIO/USDT
2025-07-28 12:13:08,408 - advanced_data_fetcher - WARNING - Low quality data for BIO/USDT 1h: {'date_range_days': 205, 'min_required_days': 1460, 'actual_samples': 4944, 'expected_samples': 35040, 'completeness_ratio': 0.1410958904109589, 'gap_ratio': 0.0, 'avg_volume': np.float64(3656105.5422127834), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:09,121 - advanced_data_fetcher - INFO - Cached data for BIO/USDT 1h
2025-07-28 12:13:09,121 - advanced_data_fetcher - INFO - Fetching 4h data for BIO/USDT
2025-07-28 12:13:10,050 - advanced_data_fetcher - WARNING - Low quality data for BIO/USDT 4h: {'date_range_days': 206, 'min_required_days': 1460, 'actual_samples': 1237, 'expected_samples': 8760, 'completeness_ratio': 0.14121004566210046, 'gap_ratio': 0.0, 'avg_volume': np.float64(14612599.67720291), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:10,250 - advanced_data_fetcher - INFO - Cached data for BIO/USDT 4h
2025-07-28 12:13:10,250 - advanced_data_fetcher - INFO - Fetching 1d data for BIO/USDT
2025-07-28 12:13:10,705 - advanced_data_fetcher - WARNING - Low quality data for BIO/USDT 1d: {'date_range_days': 206, 'min_required_days': 1460, 'actual_samples': 207, 'expected_samples': 1460, 'completeness_ratio': 0.14178082191780822, 'gap_ratio': 0.0, 'avg_volume': np.float64(87322636.71835747), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:10,775 - advanced_data_fetcher - INFO - Cached data for BIO/USDT 1d
2025-07-28 12:13:10,775 - advanced_data_fetcher - INFO - Fetching 1h data for BLUR/USDT
2025-07-28 12:13:14,081 - advanced_data_fetcher - WARNING - Low quality data for BIFI/USDT 1h: {'date_range_days': 1214, 'min_required_days': 1460, 'actual_samples': 29136, 'expected_samples': 35040, 'completeness_ratio': 0.8315068493150685, 'gap_ratio': 3.432062326251845e-05, 'avg_volume': np.float64(82.36001214991764), 'quality_score': np.float64(0.5), 'is_high_quality': np.False_}
2025-07-28 12:13:17,665 - advanced_data_fetcher - INFO - Cached data for BIFI/USDT 1h
2025-07-28 12:13:17,665 - advanced_data_fetcher - INFO - Fetching 4h data for BIFI/USDT
2025-07-28 12:13:18,414 - advanced_data_fetcher - WARNING - Low quality data for BLUR/USDT 1h: {'date_range_days': 612, 'min_required_days': 1460, 'actual_samples': 14689, 'expected_samples': 35040, 'completeness_ratio': 0.41920662100456624, 'gap_ratio': 0.0, 'avg_volume': np.float64(1480585.957376268), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:20,400 - advanced_data_fetcher - INFO - Cached data for BLUR/USDT 1h
2025-07-28 12:13:20,400 - advanced_data_fetcher - INFO - Fetching 4h data for BLUR/USDT
2025-07-28 12:13:21,750 - advanced_data_fetcher - WARNING - Low quality data for BIFI/USDT 4h: {'date_range_days': 1214, 'min_required_days': 1460, 'actual_samples': 7285, 'expected_samples': 8760, 'completeness_ratio': 0.83162100456621, 'gap_ratio': 0.0, 'avg_volume': np.float64(329.3948269045985), 'quality_score': np.float64(0.5), 'is_high_quality': np.False_}
2025-07-28 12:13:22,781 - advanced_data_fetcher - WARNING - Low quality data for BLUR/USDT 4h: {'date_range_days': 612, 'min_required_days': 1460, 'actual_samples': 3673, 'expected_samples': 8760, 'completeness_ratio': 0.4192922374429224, 'gap_ratio': 0.0, 'avg_volume': np.float64(5921134.529784918), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:23,199 - advanced_data_fetcher - INFO - Cached data for BIFI/USDT 4h
2025-07-28 12:13:23,199 - advanced_data_fetcher - INFO - Fetching 1d data for BIFI/USDT
2025-07-28 12:13:23,541 - advanced_data_fetcher - INFO - Cached data for BLUR/USDT 4h
2025-07-28 12:13:23,541 - advanced_data_fetcher - INFO - Fetching 1d data for BLUR/USDT
2025-07-28 12:13:24,044 - advanced_data_fetcher - WARNING - Low quality data for BLUR/USDT 1d: {'date_range_days': 612, 'min_required_days': 1460, 'actual_samples': 613, 'expected_samples': 1460, 'completeness_ratio': 0.41986301369863016, 'gap_ratio': 0.0, 'avg_volume': np.float64(35478510.81223491), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:24,216 - advanced_data_fetcher - INFO - Cached data for BLUR/USDT 1d
2025-07-28 12:13:24,216 - advanced_data_fetcher - INFO - Fetching 1h data for BMT/USDT
2025-07-28 12:13:24,424 - advanced_data_fetcher - INFO - Cached data for BIFI/USDT 1d
2025-07-28 12:13:24,424 - advanced_data_fetcher - INFO - Fetching 1h data for BNSOL/USDT
2025-07-28 12:13:26,200 - advanced_data_fetcher - WARNING - Low quality data for BMT/USDT 1h: {'date_range_days': 131, 'min_required_days': 1460, 'actual_samples': 3163, 'expected_samples': 35040, 'completeness_ratio': 0.09026826484018265, 'gap_ratio': 0.0, 'avg_volume': np.float64(2947986.1626936453), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:26,681 - advanced_data_fetcher - INFO - Cached data for BMT/USDT 1h
2025-07-28 12:13:26,697 - advanced_data_fetcher - INFO - Fetching 4h data for BMT/USDT
2025-07-28 12:13:27,200 - advanced_data_fetcher - WARNING - Low quality data for BMT/USDT 4h: {'date_range_days': 131, 'min_required_days': 1460, 'actual_samples': 792, 'expected_samples': 8760, 'completeness_ratio': 0.09041095890410959, 'gap_ratio': 0.0, 'avg_volume': np.float64(11773333.627020203), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:27,350 - advanced_data_fetcher - INFO - Cached data for BMT/USDT 4h
2025-07-28 12:13:27,350 - advanced_data_fetcher - INFO - Fetching 1d data for BMT/USDT
2025-07-28 12:13:27,808 - advanced_data_fetcher - WARNING - Low quality data for BMT/USDT 1d: {'date_range_days': 132, 'min_required_days': 1460, 'actual_samples': 133, 'expected_samples': 1460, 'completeness_ratio': 0.0910958904109589, 'gap_ratio': 0.0, 'avg_volume': np.float64(70108873.9293233), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:27,941 - advanced_data_fetcher - INFO - Cached data for BMT/USDT 1d
2025-07-28 12:13:27,941 - advanced_data_fetcher - INFO - Fetching 1h data for BNT/USDT
2025-07-28 12:13:27,950 - advanced_data_fetcher - WARNING - Low quality data for BNSOL/USDT 1h: {'date_range_days': 284, 'min_required_days': 1460, 'actual_samples': 6818, 'expected_samples': 35040, 'completeness_ratio': 0.19457762557077626, 'gap_ratio': 0.0, 'avg_volume': np.float64(733.3055825755354), 'quality_score': np.float64(0.2), 'is_high_quality': np.False_}
2025-07-28 12:13:28,825 - advanced_data_fetcher - INFO - Cached data for BNSOL/USDT 1h
2025-07-28 12:13:28,825 - advanced_data_fetcher - INFO - Fetching 4h data for BNSOL/USDT
2025-07-28 12:13:29,887 - advanced_data_fetcher - WARNING - Low quality data for BNSOL/USDT 4h: {'date_range_days': 284, 'min_required_days': 1460, 'actual_samples': 1705, 'expected_samples': 8760, 'completeness_ratio': 0.19463470319634704, 'gap_ratio': 0.0, 'avg_volume': np.float64(2932.362147800586), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:30,175 - advanced_data_fetcher - INFO - Cached data for BNSOL/USDT 4h
2025-07-28 12:13:30,175 - advanced_data_fetcher - INFO - Fetching 1d data for BNSOL/USDT
2025-07-28 12:13:31,042 - advanced_data_fetcher - WARNING - Low quality data for BNSOL/USDT 1d: {'date_range_days': 284, 'min_required_days': 1460, 'actual_samples': 285, 'expected_samples': 1460, 'completeness_ratio': 0.1952054794520548, 'gap_ratio': 0.0, 'avg_volume': np.float64(17542.727936842104), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:31,133 - advanced_data_fetcher - INFO - Cached data for BNSOL/USDT 1d
2025-07-28 12:13:31,133 - advanced_data_fetcher - INFO - Fetching 1h data for BOME/USDT
2025-07-28 12:13:37,639 - advanced_data_fetcher - WARNING - Low quality data for BOME/USDT 1h: {'date_range_days': 498, 'min_required_days': 1460, 'actual_samples': 11974, 'expected_samples': 35040, 'completeness_ratio': 0.34172374429223745, 'gap_ratio': 0.0, 'avg_volume': np.float64(384387339.7415233), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:39,116 - advanced_data_fetcher - INFO - Cached data for BOME/USDT 1h
2025-07-28 12:13:39,116 - advanced_data_fetcher - INFO - Fetching 4h data for BOME/USDT
2025-07-28 12:13:40,600 - advanced_data_fetcher - WARNING - Low quality data for BOME/USDT 4h: {'date_range_days': 498, 'min_required_days': 1460, 'actual_samples': 2994, 'expected_samples': 8760, 'completeness_ratio': 0.34178082191780823, 'gap_ratio': 0.0, 'avg_volume': np.float64(1537292587.1960588), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:41,025 - advanced_data_fetcher - INFO - Cached data for BOME/USDT 4h
2025-07-28 12:13:41,025 - advanced_data_fetcher - INFO - Fetching 1d data for BOME/USDT
2025-07-28 12:13:41,501 - advanced_data_fetcher - WARNING - Low quality data for BOME/USDT 1d: {'date_range_days': 499, 'min_required_days': 1460, 'actual_samples': 500, 'expected_samples': 1460, 'completeness_ratio': 0.3424657534246575, 'gap_ratio': 0.0, 'avg_volume': np.float64(9205308012.13), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:41,605 - advanced_data_fetcher - INFO - Cached data for BOME/USDT 1d
2025-07-28 12:13:41,605 - advanced_data_fetcher - INFO - Fetching 1h data for BONK/USDT
2025-07-28 12:13:49,700 - advanced_data_fetcher - WARNING - Low quality data for BONK/USDT 1h: {'date_range_days': 591, 'min_required_days': 1460, 'actual_samples': 14186, 'expected_samples': 35040, 'completeness_ratio': 0.40485159817351596, 'gap_ratio': 0.0, 'avg_volume': np.float64(128705922487.38602), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:53,200 - advanced_data_fetcher - INFO - Cached data for BONK/USDT 1h
2025-07-28 12:13:53,200 - advanced_data_fetcher - INFO - Fetching 4h data for BONK/USDT
2025-07-28 12:13:53,426 - advanced_data_fetcher - INFO - Cached data for BNT/USDT 1h
2025-07-28 12:13:53,426 - advanced_data_fetcher - INFO - Fetching 4h data for BNT/USDT
2025-07-28 12:13:55,215 - advanced_data_fetcher - WARNING - Low quality data for BONK/USDT 4h: {'date_range_days': 591, 'min_required_days': 1460, 'actual_samples': 3547, 'expected_samples': 8760, 'completeness_ratio': 0.40490867579908674, 'gap_ratio': 0.0, 'avg_volume': np.float64(514751120718.386), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:55,700 - advanced_data_fetcher - INFO - Cached data for BONK/USDT 4h
2025-07-28 12:13:55,700 - advanced_data_fetcher - INFO - Fetching 1d data for BONK/USDT
2025-07-28 12:13:56,186 - advanced_data_fetcher - WARNING - Low quality data for BONK/USDT 1d: {'date_range_days': 591, 'min_required_days': 1460, 'actual_samples': 592, 'expected_samples': 1460, 'completeness_ratio': 0.4054794520547945, 'gap_ratio': 0.0, 'avg_volume': np.float64(3084159221672.632), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:56,351 - advanced_data_fetcher - INFO - Cached data for BONK/USDT 1d
2025-07-28 12:13:56,351 - advanced_data_fetcher - INFO - Fetching 1h data for BROCCOLI714/USDT
2025-07-28 12:13:57,800 - advanced_data_fetcher - WARNING - Low quality data for BROCCOLI714/USDT 1h: {'date_range_days': 122, 'min_required_days': 1460, 'actual_samples': 2941, 'expected_samples': 35040, 'completeness_ratio': 0.08393264840182649, 'gap_ratio': 0.0, 'avg_volume': np.float64(6631441.176130568), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:58,210 - advanced_data_fetcher - INFO - Cached data for BROCCOLI714/USDT 1h
2025-07-28 12:13:58,210 - advanced_data_fetcher - INFO - Fetching 4h data for BROCCOLI714/USDT
2025-07-28 12:13:58,700 - advanced_data_fetcher - WARNING - Low quality data for BROCCOLI714/USDT 4h: {'date_range_days': 122, 'min_required_days': 1460, 'actual_samples': 736, 'expected_samples': 8760, 'completeness_ratio': 0.08401826484018265, 'gap_ratio': 0.0, 'avg_volume': np.float64(26498734.373641305), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:58,850 - advanced_data_fetcher - INFO - Cached data for BROCCOLI714/USDT 4h
2025-07-28 12:13:58,850 - advanced_data_fetcher - INFO - Fetching 1d data for BROCCOLI714/USDT
2025-07-28 12:13:59,313 - advanced_data_fetcher - WARNING - Low quality data for BROCCOLI714/USDT 1d: {'date_range_days': 123, 'min_required_days': 1460, 'actual_samples': 124, 'expected_samples': 1460, 'completeness_ratio': 0.08493150684931507, 'gap_ratio': 0.0, 'avg_volume': np.float64(157282810.47580644), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:13:59,495 - advanced_data_fetcher - INFO - Cached data for BROCCOLI714/USDT 1d
2025-07-28 12:13:59,495 - advanced_data_fetcher - INFO - Fetching 1h data for C/USDT
2025-07-28 12:13:59,979 - advanced_data_fetcher - WARNING - Low quality data for C/USDT 1h: {'date_range_days': 9, 'min_required_days': 1460, 'actual_samples': 236, 'expected_samples': 35040, 'completeness_ratio': 0.006735159817351598, 'gap_ratio': 0.0, 'avg_volume': np.float64(6798023.586016949), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:14:00,215 - advanced_data_fetcher - INFO - Cached data for C/USDT 1h
2025-07-28 12:14:00,215 - advanced_data_fetcher - INFO - Fetching 4h data for C/USDT
2025-07-28 12:14:00,452 - advanced_data_fetcher - INFO - Cached data for BNT/USDT 4h
2025-07-28 12:14:00,452 - advanced_data_fetcher - INFO - Fetching 1d data for BNT/USDT
2025-07-28 12:14:00,671 - advanced_data_fetcher - WARNING - Low quality data for C/USDT 4h: {'date_range_days': 9, 'min_required_days': 1460, 'actual_samples': 60, 'expected_samples': 8760, 'completeness_ratio': 0.00684931506849315, 'gap_ratio': 0.0, 'avg_volume': np.float64(26738892.771666665), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:14:00,725 - advanced_data_fetcher - INFO - Cached data for C/USDT 4h
2025-07-28 12:14:00,725 - advanced_data_fetcher - INFO - Fetching 1d data for C/USDT
2025-07-28 12:14:01,201 - advanced_data_fetcher - WARNING - Low quality data for C/USDT 1d: {'date_range_days': 10, 'min_required_days': 1460, 'actual_samples': 11, 'expected_samples': 1460, 'completeness_ratio': 0.007534246575342466, 'gap_ratio': 0.0, 'avg_volume': np.float64(145848506.02727273), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:14:01,265 - advanced_data_fetcher - INFO - Cached data for C/USDT 1d
2025-07-28 12:14:01,265 - advanced_data_fetcher - INFO - Fetching 1h data for C98/USDT
2025-07-28 12:14:01,615 - advanced_data_fetcher - INFO - Cached data for BNT/USDT 1d
2025-07-28 12:14:01,615 - advanced_data_fetcher - INFO - Fetching 1h data for CAKE/USDT
2025-07-28 12:14:25,665 - advanced_data_fetcher - INFO - Cached data for C98/USDT 1h
2025-07-28 12:14:25,665 - advanced_data_fetcher - INFO - Fetching 4h data for C98/USDT
2025-07-28 12:14:28,840 - advanced_data_fetcher - INFO - Cached data for CAKE/USDT 1h
2025-07-28 12:14:28,841 - advanced_data_fetcher - INFO - Fetching 4h data for CAKE/USDT
2025-07-28 12:14:31,936 - advanced_data_fetcher - INFO - Cached data for C98/USDT 4h
2025-07-28 12:14:31,936 - advanced_data_fetcher - INFO - Fetching 1d data for C98/USDT
2025-07-28 12:14:33,546 - advanced_data_fetcher - INFO - Cached data for C98/USDT 1d
2025-07-28 12:14:33,547 - advanced_data_fetcher - INFO - Fetching 1h data for CATI/USDT
2025-07-28 12:14:35,050 - advanced_data_fetcher - INFO - Cached data for CAKE/USDT 4h
2025-07-28 12:14:35,050 - advanced_data_fetcher - INFO - Fetching 1d data for CAKE/USDT
2025-07-28 12:14:36,515 - advanced_data_fetcher - INFO - Cached data for CAKE/USDT 1d
2025-07-28 12:14:36,515 - advanced_data_fetcher - INFO - Fetching 1h data for CELO/USDT
2025-07-28 12:14:37,727 - advanced_data_fetcher - WARNING - Low quality data for CATI/USDT 1h: {'date_range_days': 310, 'min_required_days': 1460, 'actual_samples': 7463, 'expected_samples': 35040, 'completeness_ratio': 0.2129851598173516, 'gap_ratio': 0.0, 'avg_volume': np.float64(1471659.6211845102), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:14:38,780 - advanced_data_fetcher - INFO - Cached data for CATI/USDT 1h
2025-07-28 12:14:38,780 - advanced_data_fetcher - INFO - Fetching 4h data for CATI/USDT
2025-07-28 12:14:39,789 - advanced_data_fetcher - WARNING - Low quality data for CATI/USDT 4h: {'date_range_days': 311, 'min_required_days': 1460, 'actual_samples': 1867, 'expected_samples': 8760, 'completeness_ratio': 0.21312785388127853, 'gap_ratio': 0.0, 'avg_volume': np.float64(5882697.2431173), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:14:40,125 - advanced_data_fetcher - INFO - Cached data for CATI/USDT 4h
2025-07-28 12:14:40,125 - advanced_data_fetcher - INFO - Fetching 1d data for CATI/USDT
2025-07-28 12:14:40,656 - advanced_data_fetcher - WARNING - Low quality data for CATI/USDT 1d: {'date_range_days': 311, 'min_required_days': 1460, 'actual_samples': 312, 'expected_samples': 1460, 'completeness_ratio': 0.2136986301369863, 'gap_ratio': 0.0, 'avg_volume': np.float64(35201909.464423075), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:14:40,750 - advanced_data_fetcher - INFO - Cached data for CATI/USDT 1d
2025-07-28 12:14:40,750 - advanced_data_fetcher - INFO - Fetching 1h data for CELR/USDT
2025-07-28 12:14:59,468 - advanced_data_fetcher - INFO - Cached data for CELO/USDT 1h
2025-07-28 12:14:59,468 - advanced_data_fetcher - INFO - Fetching 4h data for CELO/USDT
2025-07-28 12:15:06,436 - advanced_data_fetcher - INFO - Cached data for CELR/USDT 1h
2025-07-28 12:15:06,436 - advanced_data_fetcher - INFO - Fetching 4h data for CELR/USDT
2025-07-28 12:15:06,565 - advanced_data_fetcher - INFO - Cached data for CELO/USDT 4h
2025-07-28 12:15:06,565 - advanced_data_fetcher - INFO - Fetching 1d data for CELO/USDT
2025-07-28 12:15:07,755 - advanced_data_fetcher - INFO - Cached data for CELO/USDT 1d
2025-07-28 12:15:07,755 - advanced_data_fetcher - INFO - Fetching 1h data for CETUS/USDT
2025-07-28 12:15:11,181 - advanced_data_fetcher - WARNING - Low quality data for CETUS/USDT 1h: {'date_range_days': 263, 'min_required_days': 1460, 'actual_samples': 6334, 'expected_samples': 35040, 'completeness_ratio': 0.1807648401826484, 'gap_ratio': 0.0, 'avg_volume': np.float64(2163801.1419955795), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:15:12,934 - advanced_data_fetcher - INFO - Cached data for CELR/USDT 4h
2025-07-28 12:15:12,934 - advanced_data_fetcher - INFO - Fetching 1d data for CELR/USDT
2025-07-28 12:15:13,231 - advanced_data_fetcher - INFO - Cached data for CETUS/USDT 1h
2025-07-28 12:15:13,231 - advanced_data_fetcher - INFO - Fetching 4h data for CETUS/USDT
2025-07-28 12:15:14,150 - advanced_data_fetcher - INFO - Cached data for CELR/USDT 1d
2025-07-28 12:15:14,150 - advanced_data_fetcher - INFO - Fetching 1h data for CFX/USDT
2025-07-28 12:15:14,222 - advanced_data_fetcher - WARNING - Low quality data for CETUS/USDT 4h: {'date_range_days': 263, 'min_required_days': 1460, 'actual_samples': 1584, 'expected_samples': 8760, 'completeness_ratio': 0.18082191780821918, 'gap_ratio': 0.0, 'avg_volume': np.float64(8652499.858459596), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:15:14,486 - advanced_data_fetcher - INFO - Cached data for CETUS/USDT 4h
2025-07-28 12:15:14,486 - advanced_data_fetcher - INFO - Fetching 1d data for CETUS/USDT
2025-07-28 12:15:14,965 - advanced_data_fetcher - WARNING - Low quality data for CETUS/USDT 1d: {'date_range_days': 264, 'min_required_days': 1460, 'actual_samples': 265, 'expected_samples': 1460, 'completeness_ratio': 0.1815068493150685, 'gap_ratio': 0.0, 'avg_volume': np.float64(51719093.4935849), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:15:15,050 - advanced_data_fetcher - INFO - Cached data for CETUS/USDT 1d
2025-07-28 12:15:15,050 - advanced_data_fetcher - INFO - Fetching 1h data for CGPT/USDT
2025-07-28 12:15:17,434 - advanced_data_fetcher - WARNING - Low quality data for CGPT/USDT 1h: {'date_range_days': 198, 'min_required_days': 1460, 'actual_samples': 4773, 'expected_samples': 35040, 'completeness_ratio': 0.13621575342465753, 'gap_ratio': 0.0, 'avg_volume': np.float64(1638544.3331238218), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:15:18,031 - advanced_data_fetcher - INFO - Cached data for CGPT/USDT 1h
2025-07-28 12:15:18,031 - advanced_data_fetcher - INFO - Fetching 4h data for CGPT/USDT
2025-07-28 12:15:19,006 - advanced_data_fetcher - WARNING - Low quality data for CGPT/USDT 4h: {'date_range_days': 198, 'min_required_days': 1460, 'actual_samples': 1194, 'expected_samples': 8760, 'completeness_ratio': 0.1363013698630137, 'gap_ratio': 0.0, 'avg_volume': np.float64(6550060.386934673), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:15:19,225 - advanced_data_fetcher - INFO - Cached data for CGPT/USDT 4h
2025-07-28 12:15:19,225 - advanced_data_fetcher - INFO - Fetching 1d data for CGPT/USDT
2025-07-28 12:15:19,666 - advanced_data_fetcher - WARNING - Low quality data for CGPT/USDT 1d: {'date_range_days': 199, 'min_required_days': 1460, 'actual_samples': 200, 'expected_samples': 1460, 'completeness_ratio': 0.136986301369863, 'gap_ratio': 0.0, 'avg_volume': np.float64(39103860.51), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:15:19,750 - advanced_data_fetcher - INFO - Cached data for CGPT/USDT 1d
2025-07-28 12:15:19,750 - advanced_data_fetcher - INFO - Fetching 1h data for CHESS/USDT
2025-07-28 12:15:36,119 - advanced_data_fetcher - INFO - Cached data for CFX/USDT 1h
2025-07-28 12:15:36,119 - advanced_data_fetcher - INFO - Fetching 4h data for CFX/USDT
2025-07-28 12:15:40,852 - advanced_data_fetcher - INFO - Cached data for CHESS/USDT 1h
2025-07-28 12:15:40,852 - advanced_data_fetcher - INFO - Fetching 4h data for CHESS/USDT
2025-07-28 12:15:42,485 - advanced_data_fetcher - INFO - Cached data for CFX/USDT 4h
2025-07-28 12:15:42,485 - advanced_data_fetcher - INFO - Fetching 1d data for CFX/USDT
2025-07-28 12:15:43,750 - advanced_data_fetcher - INFO - Cached data for CFX/USDT 1d
2025-07-28 12:15:43,750 - advanced_data_fetcher - INFO - Fetching 1h data for CHR/USDT
2025-07-28 12:15:46,649 - advanced_data_fetcher - INFO - Cached data for CHESS/USDT 4h
2025-07-28 12:15:46,650 - advanced_data_fetcher - INFO - Fetching 1d data for CHESS/USDT
2025-07-28 12:15:47,965 - advanced_data_fetcher - INFO - Cached data for CHESS/USDT 1d
2025-07-28 12:15:47,965 - advanced_data_fetcher - INFO - Fetching 1h data for CHZ/USDT
2025-07-28 12:16:06,793 - advanced_data_fetcher - INFO - Cached data for CHR/USDT 1h
2025-07-28 12:16:06,793 - advanced_data_fetcher - INFO - Fetching 4h data for CHR/USDT
2025-07-28 12:16:11,025 - advanced_data_fetcher - INFO - Cached data for CHZ/USDT 1h
2025-07-28 12:16:11,025 - advanced_data_fetcher - INFO - Fetching 4h data for CHZ/USDT
2025-07-28 12:16:12,698 - advanced_data_fetcher - INFO - Cached data for CHR/USDT 4h
2025-07-28 12:16:12,698 - advanced_data_fetcher - INFO - Fetching 1d data for CHR/USDT
2025-07-28 12:16:13,865 - advanced_data_fetcher - INFO - Cached data for CHR/USDT 1d
2025-07-28 12:16:13,865 - advanced_data_fetcher - INFO - Fetching 1h data for CITY/USDT
2025-07-28 12:16:16,765 - advanced_data_fetcher - INFO - Cached data for CHZ/USDT 4h
2025-07-28 12:16:16,765 - advanced_data_fetcher - INFO - Fetching 1d data for CHZ/USDT
2025-07-28 12:16:17,986 - advanced_data_fetcher - INFO - Cached data for CHZ/USDT 1d
2025-07-28 12:16:17,986 - advanced_data_fetcher - INFO - Fetching 1h data for CKB/USDT
2025-07-28 12:16:34,140 - advanced_data_fetcher - INFO - Cached data for CITY/USDT 1h
2025-07-28 12:16:34,140 - advanced_data_fetcher - INFO - Fetching 4h data for CITY/USDT
2025-07-28 12:16:42,350 - advanced_data_fetcher - INFO - Cached data for CITY/USDT 4h
2025-07-28 12:16:42,352 - advanced_data_fetcher - INFO - Fetching 1d data for CITY/USDT
2025-07-28 12:16:43,928 - advanced_data_fetcher - INFO - Cached data for CITY/USDT 1d
2025-07-28 12:16:43,928 - advanced_data_fetcher - INFO - Fetching 1h data for COMP/USDT
2025-07-28 12:16:44,475 - advanced_data_fetcher - INFO - Cached data for CKB/USDT 1h
2025-07-28 12:16:44,475 - advanced_data_fetcher - INFO - Fetching 4h data for CKB/USDT
2025-07-28 12:16:50,197 - advanced_data_fetcher - INFO - Cached data for CKB/USDT 4h
2025-07-28 12:16:50,197 - advanced_data_fetcher - INFO - Fetching 1d data for CKB/USDT
2025-07-28 12:16:51,354 - advanced_data_fetcher - INFO - Cached data for CKB/USDT 1d
2025-07-28 12:16:51,354 - advanced_data_fetcher - INFO - Fetching 1h data for COOKIE/USDT
2025-07-28 12:16:54,250 - advanced_data_fetcher - WARNING - Low quality data for COOKIE/USDT 1h: {'date_range_days': 198, 'min_required_days': 1460, 'actual_samples': 4773, 'expected_samples': 35040, 'completeness_ratio': 0.13621575342465753, 'gap_ratio': 0.0, 'avg_volume': np.float64(1702175.5275717578), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:16:54,900 - advanced_data_fetcher - INFO - Cached data for COOKIE/USDT 1h
2025-07-28 12:16:54,900 - advanced_data_fetcher - INFO - Fetching 4h data for COOKIE/USDT
2025-07-28 12:16:55,836 - advanced_data_fetcher - WARNING - Low quality data for COOKIE/USDT 4h: {'date_range_days': 198, 'min_required_days': 1460, 'actual_samples': 1194, 'expected_samples': 8760, 'completeness_ratio': 0.1363013698630137, 'gap_ratio': 0.0, 'avg_volume': np.float64(6804425.287353435), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:16:56,025 - advanced_data_fetcher - INFO - Cached data for COOKIE/USDT 4h
2025-07-28 12:16:56,025 - advanced_data_fetcher - INFO - Fetching 1d data for COOKIE/USDT
2025-07-28 12:16:56,465 - advanced_data_fetcher - WARNING - Low quality data for COOKIE/USDT 1d: {'date_range_days': 199, 'min_required_days': 1460, 'actual_samples': 200, 'expected_samples': 1460, 'completeness_ratio': 0.136986301369863, 'gap_ratio': 0.0, 'avg_volume': np.float64(40622418.965500005), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:16:56,531 - advanced_data_fetcher - INFO - Cached data for COOKIE/USDT 1d
2025-07-28 12:16:56,531 - advanced_data_fetcher - INFO - Fetching 1h data for COS/USDT
2025-07-28 12:17:06,614 - advanced_data_fetcher - INFO - Cached data for COMP/USDT 1h
2025-07-28 12:17:06,614 - advanced_data_fetcher - INFO - Fetching 4h data for COMP/USDT
2025-07-28 12:17:12,231 - advanced_data_fetcher - INFO - Cached data for COMP/USDT 4h
2025-07-28 12:17:12,231 - advanced_data_fetcher - INFO - Fetching 1d data for COMP/USDT
2025-07-28 12:17:13,416 - advanced_data_fetcher - INFO - Cached data for COMP/USDT 1d
2025-07-28 12:17:13,416 - advanced_data_fetcher - INFO - Fetching 1h data for COTI/USDT
2025-07-28 12:17:19,800 - advanced_data_fetcher - INFO - Cached data for COS/USDT 1h
2025-07-28 12:17:19,810 - advanced_data_fetcher - INFO - Fetching 4h data for COS/USDT
2025-07-28 12:17:25,538 - advanced_data_fetcher - INFO - Cached data for COS/USDT 4h
2025-07-28 12:17:25,538 - advanced_data_fetcher - INFO - Fetching 1d data for COS/USDT
2025-07-28 12:17:26,765 - advanced_data_fetcher - INFO - Cached data for COS/USDT 1d
2025-07-28 12:17:26,765 - advanced_data_fetcher - INFO - Fetching 1h data for COW/USDT
2025-07-28 12:17:30,100 - advanced_data_fetcher - WARNING - Low quality data for COW/USDT 1h: {'date_range_days': 263, 'min_required_days': 1460, 'actual_samples': 6334, 'expected_samples': 35040, 'completeness_ratio': 0.1807648401826484, 'gap_ratio': 0.0, 'avg_volume': np.float64(1169367.8041837702), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:17:30,900 - advanced_data_fetcher - INFO - Cached data for COW/USDT 1h
2025-07-28 12:17:30,900 - advanced_data_fetcher - INFO - Fetching 4h data for COW/USDT
2025-07-28 12:17:31,933 - advanced_data_fetcher - WARNING - Low quality data for COW/USDT 4h: {'date_range_days': 263, 'min_required_days': 1460, 'actual_samples': 1584, 'expected_samples': 8760, 'completeness_ratio': 0.18082191780821918, 'gap_ratio': 0.0, 'avg_volume': np.float64(4675994.837247475), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:17:32,265 - advanced_data_fetcher - INFO - Cached data for COW/USDT 4h
2025-07-28 12:17:32,265 - advanced_data_fetcher - INFO - Fetching 1d data for COW/USDT
2025-07-28 12:17:32,800 - advanced_data_fetcher - WARNING - Low quality data for COW/USDT 1d: {'date_range_days': 264, 'min_required_days': 1460, 'actual_samples': 265, 'expected_samples': 1460, 'completeness_ratio': 0.1815068493150685, 'gap_ratio': 0.0, 'avg_volume': np.float64(27950098.758113205), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:17:33,125 - advanced_data_fetcher - INFO - Cached data for COW/USDT 1d
2025-07-28 12:17:33,125 - advanced_data_fetcher - INFO - Fetching 1h data for CRV/USDT
2025-07-28 12:17:36,940 - advanced_data_fetcher - INFO - Cached data for COTI/USDT 1h
2025-07-28 12:17:36,940 - advanced_data_fetcher - INFO - Fetching 4h data for COTI/USDT
2025-07-28 12:17:42,500 - advanced_data_fetcher - INFO - Cached data for COTI/USDT 4h
2025-07-28 12:17:42,500 - advanced_data_fetcher - INFO - Fetching 1d data for COTI/USDT
2025-07-28 12:17:43,665 - advanced_data_fetcher - INFO - Cached data for COTI/USDT 1d
2025-07-28 12:17:43,665 - advanced_data_fetcher - INFO - Fetching 1h data for CTK/USDT
2025-07-28 12:17:56,525 - advanced_data_fetcher - INFO - Cached data for CRV/USDT 1h
2025-07-28 12:17:56,525 - advanced_data_fetcher - INFO - Fetching 4h data for CRV/USDT
2025-07-28 12:18:02,115 - advanced_data_fetcher - INFO - Cached data for CRV/USDT 4h
2025-07-28 12:18:02,115 - advanced_data_fetcher - INFO - Fetching 1d data for CRV/USDT
2025-07-28 12:18:04,515 - advanced_data_fetcher - INFO - Cached data for CRV/USDT 1d
2025-07-28 12:18:04,525 - advanced_data_fetcher - INFO - Fetching 1h data for CTSI/USDT
2025-07-28 12:18:07,604 - advanced_data_fetcher - INFO - Cached data for CTK/USDT 1h
2025-07-28 12:18:07,604 - advanced_data_fetcher - INFO - Fetching 4h data for CTK/USDT
2025-07-28 12:18:13,965 - advanced_data_fetcher - INFO - Cached data for CTK/USDT 4h
2025-07-28 12:18:13,965 - advanced_data_fetcher - INFO - Fetching 1d data for CTK/USDT
2025-07-28 12:18:15,231 - advanced_data_fetcher - INFO - Cached data for CTK/USDT 1d
2025-07-28 12:18:15,231 - advanced_data_fetcher - INFO - Fetching 1h data for CVC/USDT
2025-07-28 12:18:28,228 - advanced_data_fetcher - INFO - Cached data for CTSI/USDT 1h
2025-07-28 12:18:28,228 - advanced_data_fetcher - INFO - Fetching 4h data for CTSI/USDT
2025-07-28 12:18:36,192 - advanced_data_fetcher - INFO - Cached data for CTSI/USDT 4h
2025-07-28 12:18:36,195 - advanced_data_fetcher - INFO - Fetching 1d data for CTSI/USDT
2025-07-28 12:18:37,715 - advanced_data_fetcher - INFO - Cached data for CTSI/USDT 1d
2025-07-28 12:18:37,715 - advanced_data_fetcher - INFO - Fetching 1h data for CVX/USDT
2025-07-28 12:18:37,728 - advanced_data_fetcher - INFO - Cached data for CVC/USDT 1h
2025-07-28 12:18:37,728 - advanced_data_fetcher - INFO - Fetching 4h data for CVC/USDT
2025-07-28 12:18:43,122 - advanced_data_fetcher - INFO - Cached data for CVC/USDT 4h
2025-07-28 12:18:43,122 - advanced_data_fetcher - INFO - Fetching 1d data for CVC/USDT
2025-07-28 12:18:44,315 - advanced_data_fetcher - INFO - Cached data for CVC/USDT 1d
2025-07-28 12:18:44,315 - advanced_data_fetcher - INFO - Fetching 1h data for CYBER/USDT
2025-07-28 12:18:52,915 - advanced_data_fetcher - WARNING - Low quality data for CYBER/USDT 1h: {'date_range_days': 712, 'min_required_days': 1460, 'actual_samples': 17110, 'expected_samples': 35040, 'completeness_ratio': 0.4882990867579909, 'gap_ratio': 0.0, 'avg_volume': np.float64(93113.51841203975), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:18:56,225 - advanced_data_fetcher - INFO - Cached data for CYBER/USDT 1h
2025-07-28 12:18:56,225 - advanced_data_fetcher - INFO - Fetching 4h data for CYBER/USDT
2025-07-28 12:18:59,002 - advanced_data_fetcher - WARNING - Low quality data for CYBER/USDT 4h: {'date_range_days': 712, 'min_required_days': 1460, 'actual_samples': 4278, 'expected_samples': 8760, 'completeness_ratio': 0.48835616438356166, 'gap_ratio': 0.0, 'avg_volume': np.float64(372410.54231650307), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:19:00,364 - advanced_data_fetcher - INFO - Cached data for CVX/USDT 1h
2025-07-28 12:19:00,365 - advanced_data_fetcher - INFO - Fetching 4h data for CVX/USDT
2025-07-28 12:19:00,374 - advanced_data_fetcher - INFO - Cached data for CYBER/USDT 4h
2025-07-28 12:19:00,375 - advanced_data_fetcher - INFO - Fetching 1d data for CYBER/USDT
2025-07-28 12:19:00,874 - advanced_data_fetcher - WARNING - Low quality data for CYBER/USDT 1d: {'date_range_days': 713, 'min_required_days': 1460, 'actual_samples': 714, 'expected_samples': 1460, 'completeness_ratio': 0.48904109589041095, 'gap_ratio': 0.0, 'avg_volume': np.float64(2231333.767689076), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:19:01,000 - advanced_data_fetcher - INFO - Cached data for CYBER/USDT 1d
2025-07-28 12:19:01,000 - advanced_data_fetcher - INFO - Fetching 1h data for D/USDT
2025-07-28 12:19:03,451 - advanced_data_fetcher - WARNING - Low quality data for D/USDT 1h: {'date_range_days': 200, 'min_required_days': 1460, 'actual_samples': 4802, 'expected_samples': 35040, 'completeness_ratio': 0.13704337899543378, 'gap_ratio': 0.0, 'avg_volume': np.float64(2002449.179092045), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:19:04,050 - advanced_data_fetcher - INFO - Cached data for D/USDT 1h
2025-07-28 12:19:04,050 - advanced_data_fetcher - INFO - Fetching 4h data for D/USDT
2025-07-28 12:19:05,050 - advanced_data_fetcher - WARNING - Low quality data for D/USDT 4h: {'date_range_days': 200, 'min_required_days': 1460, 'actual_samples': 1201, 'expected_samples': 8760, 'completeness_ratio': 0.13710045662100456, 'gap_ratio': 0.0, 'avg_volume': np.float64(8006462.079933389), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:19:05,357 - advanced_data_fetcher - INFO - Cached data for D/USDT 4h
2025-07-28 12:19:05,357 - advanced_data_fetcher - INFO - Fetching 1d data for D/USDT
2025-07-28 12:19:05,715 - advanced_data_fetcher - INFO - Cached data for CVX/USDT 4h
2025-07-28 12:19:05,715 - advanced_data_fetcher - INFO - Fetching 1d data for CVX/USDT
2025-07-28 12:19:05,895 - advanced_data_fetcher - WARNING - Low quality data for D/USDT 1d: {'date_range_days': 200, 'min_required_days': 1460, 'actual_samples': 201, 'expected_samples': 1460, 'completeness_ratio': 0.13767123287671232, 'gap_ratio': 0.0, 'avg_volume': np.float64(47839606.7562189), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:19:06,015 - advanced_data_fetcher - INFO - Cached data for D/USDT 1d
2025-07-28 12:19:06,015 - advanced_data_fetcher - INFO - Fetching 1h data for DASH/USDT
2025-07-28 12:19:06,881 - advanced_data_fetcher - INFO - Cached data for CVX/USDT 1d
2025-07-28 12:19:06,881 - advanced_data_fetcher - INFO - Fetching 1h data for DATA/USDT
2025-07-28 12:19:31,126 - advanced_data_fetcher - INFO - Cached data for DASH/USDT 1h
2025-07-28 12:19:31,130 - advanced_data_fetcher - INFO - Fetching 4h data for DASH/USDT
2025-07-28 12:19:33,915 - advanced_data_fetcher - INFO - Cached data for DATA/USDT 1h
2025-07-28 12:19:33,915 - advanced_data_fetcher - INFO - Fetching 4h data for DATA/USDT
2025-07-28 12:19:37,134 - advanced_data_fetcher - INFO - Cached data for DASH/USDT 4h
2025-07-28 12:19:37,150 - advanced_data_fetcher - INFO - Fetching 1d data for DASH/USDT
2025-07-28 12:19:38,330 - advanced_data_fetcher - INFO - Cached data for DASH/USDT 1d
2025-07-28 12:19:38,330 - advanced_data_fetcher - INFO - Fetching 1h data for DCR/USDT
2025-07-28 12:19:40,200 - advanced_data_fetcher - INFO - Cached data for DATA/USDT 4h
2025-07-28 12:19:40,200 - advanced_data_fetcher - INFO - Fetching 1d data for DATA/USDT
2025-07-28 12:19:41,415 - advanced_data_fetcher - INFO - Cached data for DATA/USDT 1d
2025-07-28 12:19:41,415 - advanced_data_fetcher - INFO - Fetching 1h data for DEGO/USDT
2025-07-28 12:20:03,450 - advanced_data_fetcher - INFO - Cached data for DCR/USDT 1h
2025-07-28 12:20:03,450 - advanced_data_fetcher - INFO - Fetching 4h data for DCR/USDT
2025-07-28 12:20:07,963 - advanced_data_fetcher - INFO - Cached data for DEGO/USDT 1h
2025-07-28 12:20:07,963 - advanced_data_fetcher - INFO - Fetching 4h data for DEGO/USDT
2025-07-28 12:20:09,667 - advanced_data_fetcher - INFO - Cached data for DCR/USDT 4h
2025-07-28 12:20:09,667 - advanced_data_fetcher - INFO - Fetching 1d data for DCR/USDT
2025-07-28 12:20:10,888 - advanced_data_fetcher - INFO - Cached data for DCR/USDT 1d
2025-07-28 12:20:10,888 - advanced_data_fetcher - INFO - Fetching 1h data for DENT/USDT
2025-07-28 12:20:13,881 - advanced_data_fetcher - INFO - Cached data for DEGO/USDT 4h
2025-07-28 12:20:13,881 - advanced_data_fetcher - INFO - Fetching 1d data for DEGO/USDT
2025-07-28 12:20:15,100 - advanced_data_fetcher - INFO - Cached data for DEGO/USDT 1d
2025-07-28 12:20:15,115 - advanced_data_fetcher - INFO - Fetching 1h data for DEXE/USDT
2025-07-28 12:20:36,756 - advanced_data_fetcher - INFO - Cached data for DENT/USDT 1h
2025-07-28 12:20:36,765 - advanced_data_fetcher - INFO - Fetching 4h data for DENT/USDT
2025-07-28 12:20:40,399 - advanced_data_fetcher - INFO - Cached data for DEXE/USDT 1h
2025-07-28 12:20:40,399 - advanced_data_fetcher - INFO - Fetching 4h data for DEXE/USDT
2025-07-28 12:20:43,815 - advanced_data_fetcher - INFO - Cached data for DENT/USDT 4h
2025-07-28 12:20:43,815 - advanced_data_fetcher - INFO - Fetching 1d data for DENT/USDT
2025-07-28 12:20:45,004 - advanced_data_fetcher - INFO - Cached data for DENT/USDT 1d
2025-07-28 12:20:45,004 - advanced_data_fetcher - INFO - Fetching 1h data for DF/USDT
2025-07-28 12:20:46,731 - advanced_data_fetcher - INFO - Cached data for DEXE/USDT 4h
2025-07-28 12:20:46,731 - advanced_data_fetcher - INFO - Fetching 1d data for DEXE/USDT
2025-07-28 12:20:47,981 - advanced_data_fetcher - INFO - Cached data for DEXE/USDT 1d
2025-07-28 12:20:47,981 - advanced_data_fetcher - INFO - Fetching 1h data for DGB/USDT
2025-07-28 12:21:06,458 - advanced_data_fetcher - INFO - Cached data for DF/USDT 1h
2025-07-28 12:21:06,459 - advanced_data_fetcher - INFO - Fetching 4h data for DF/USDT
2025-07-28 12:21:11,350 - advanced_data_fetcher - INFO - Cached data for DGB/USDT 1h
2025-07-28 12:21:11,350 - advanced_data_fetcher - INFO - Fetching 4h data for DGB/USDT
2025-07-28 12:21:12,638 - advanced_data_fetcher - INFO - Cached data for DF/USDT 4h
2025-07-28 12:21:12,638 - advanced_data_fetcher - INFO - Fetching 1d data for DF/USDT
2025-07-28 12:21:13,789 - advanced_data_fetcher - INFO - Cached data for DF/USDT 1d
2025-07-28 12:21:13,789 - advanced_data_fetcher - INFO - Fetching 1h data for DIA/USDT
2025-07-28 12:21:17,465 - advanced_data_fetcher - INFO - Cached data for DGB/USDT 4h
2025-07-28 12:21:17,465 - advanced_data_fetcher - INFO - Fetching 1d data for DGB/USDT
2025-07-28 12:21:18,699 - advanced_data_fetcher - INFO - Cached data for DGB/USDT 1d
2025-07-28 12:21:18,699 - advanced_data_fetcher - INFO - Fetching 1h data for DODO/USDT
2025-07-28 12:21:36,734 - advanced_data_fetcher - INFO - Cached data for DIA/USDT 1h
2025-07-28 12:21:36,735 - advanced_data_fetcher - INFO - Fetching 4h data for DIA/USDT
2025-07-28 12:21:41,369 - advanced_data_fetcher - INFO - Cached data for DODO/USDT 1h
2025-07-28 12:21:41,369 - advanced_data_fetcher - INFO - Fetching 4h data for DODO/USDT
2025-07-28 12:21:43,816 - advanced_data_fetcher - INFO - Cached data for DIA/USDT 4h
2025-07-28 12:21:43,816 - advanced_data_fetcher - INFO - Fetching 1d data for DIA/USDT
2025-07-28 12:21:45,025 - advanced_data_fetcher - INFO - Cached data for DIA/USDT 1d
2025-07-28 12:21:45,025 - advanced_data_fetcher - INFO - Fetching 1h data for DOGS/USDT
2025-07-28 12:21:47,365 - advanced_data_fetcher - INFO - Cached data for DODO/USDT 4h
2025-07-28 12:21:47,365 - advanced_data_fetcher - INFO - Fetching 1d data for DODO/USDT
2025-07-28 12:21:48,550 - advanced_data_fetcher - INFO - Cached data for DODO/USDT 1d
2025-07-28 12:21:48,550 - advanced_data_fetcher - INFO - Fetching 1h data for DUSK/USDT
2025-07-28 12:21:49,681 - advanced_data_fetcher - WARNING - Low quality data for DOGS/USDT 1h: {'date_range_days': 335, 'min_required_days': 1460, 'actual_samples': 8062, 'expected_samples': 35040, 'completeness_ratio': 0.23007990867579908, 'gap_ratio': 0.0, 'avg_volume': np.float64(1972760864.2290995), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:21:50,781 - advanced_data_fetcher - INFO - Cached data for DOGS/USDT 1h
2025-07-28 12:21:50,781 - advanced_data_fetcher - INFO - Fetching 4h data for DOGS/USDT
2025-07-28 12:21:52,175 - advanced_data_fetcher - WARNING - Low quality data for DOGS/USDT 4h: {'date_range_days': 335, 'min_required_days': 1460, 'actual_samples': 2016, 'expected_samples': 8760, 'completeness_ratio': 0.23013698630136986, 'gap_ratio': 0.0, 'avg_volume': np.float64(7889086368.384424), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:21:52,541 - advanced_data_fetcher - INFO - Cached data for DOGS/USDT 4h
2025-07-28 12:21:52,541 - advanced_data_fetcher - INFO - Fetching 1d data for DOGS/USDT
2025-07-28 12:21:52,983 - advanced_data_fetcher - WARNING - Low quality data for DOGS/USDT 1d: {'date_range_days': 336, 'min_required_days': 1460, 'actual_samples': 337, 'expected_samples': 1460, 'completeness_ratio': 0.23082191780821917, 'gap_ratio': 0.0, 'avg_volume': np.float64(47194059699.29674), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:21:53,073 - advanced_data_fetcher - INFO - Cached data for DOGS/USDT 1d
2025-07-28 12:21:53,073 - advanced_data_fetcher - INFO - Fetching 1h data for DYDX/USDT
2025-07-28 12:22:11,548 - advanced_data_fetcher - INFO - Cached data for DUSK/USDT 1h
2025-07-28 12:22:11,548 - advanced_data_fetcher - INFO - Fetching 4h data for DUSK/USDT
2025-07-28 12:22:15,552 - advanced_data_fetcher - INFO - Cached data for DYDX/USDT 1h
2025-07-28 12:22:15,552 - advanced_data_fetcher - INFO - Fetching 4h data for DYDX/USDT
2025-07-28 12:22:17,391 - advanced_data_fetcher - INFO - Cached data for DUSK/USDT 4h
2025-07-28 12:22:17,391 - advanced_data_fetcher - INFO - Fetching 1d data for DUSK/USDT
2025-07-28 12:22:18,528 - advanced_data_fetcher - INFO - Cached data for DUSK/USDT 1d
2025-07-28 12:22:18,528 - advanced_data_fetcher - INFO - Fetching 1h data for DYM/USDT
2025-07-28 12:22:21,217 - advanced_data_fetcher - INFO - Cached data for DYDX/USDT 4h
2025-07-28 12:22:21,217 - advanced_data_fetcher - INFO - Fetching 1d data for DYDX/USDT
2025-07-28 12:22:22,415 - advanced_data_fetcher - INFO - Cached data for DYDX/USDT 1d
2025-07-28 12:22:22,416 - advanced_data_fetcher - INFO - Fetching 1h data for EDU/USDT
2025-07-28 12:22:25,031 - advanced_data_fetcher - WARNING - Low quality data for DYM/USDT 1h: {'date_range_days': 537, 'min_required_days': 1460, 'actual_samples': 12905, 'expected_samples': 35040, 'completeness_ratio': 0.3682933789954338, 'gap_ratio': 0.0, 'avg_volume': np.float64(218810.98463386283), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:22:26,650 - advanced_data_fetcher - INFO - Cached data for DYM/USDT 1h
2025-07-28 12:22:26,650 - advanced_data_fetcher - INFO - Fetching 4h data for DYM/USDT
2025-07-28 12:22:28,550 - advanced_data_fetcher - WARNING - Low quality data for DYM/USDT 4h: {'date_range_days': 537, 'min_required_days': 1460, 'actual_samples': 3227, 'expected_samples': 8760, 'completeness_ratio': 0.36837899543378994, 'gap_ratio': 0.0, 'avg_volume': np.float64(875040.5195847536), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:22:28,981 - advanced_data_fetcher - INFO - Cached data for DYM/USDT 4h
2025-07-28 12:22:28,981 - advanced_data_fetcher - INFO - Fetching 1d data for DYM/USDT
2025-07-28 12:22:29,465 - advanced_data_fetcher - WARNING - Low quality data for DYM/USDT 1d: {'date_range_days': 538, 'min_required_days': 1460, 'actual_samples': 539, 'expected_samples': 1460, 'completeness_ratio': 0.36917808219178083, 'gap_ratio': 0.0, 'avg_volume': np.float64(5238878.954916512), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:22:29,604 - advanced_data_fetcher - INFO - Cached data for DYM/USDT 1d
2025-07-28 12:22:29,604 - advanced_data_fetcher - INFO - Fetching 1h data for EGLD/USDT
2025-07-28 12:22:32,481 - advanced_data_fetcher - WARNING - Low quality data for EDU/USDT 1h: {'date_range_days': 821, 'min_required_days': 1460, 'actual_samples': 19726, 'expected_samples': 35040, 'completeness_ratio': 0.5629566210045662, 'gap_ratio': 0.0, 'avg_volume': np.float64(549897.4988847207), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:22:34,931 - advanced_data_fetcher - INFO - Cached data for EDU/USDT 1h
2025-07-28 12:22:34,931 - advanced_data_fetcher - INFO - Fetching 4h data for EDU/USDT
2025-07-28 12:22:37,554 - advanced_data_fetcher - WARNING - Low quality data for EDU/USDT 4h: {'date_range_days': 821, 'min_required_days': 1460, 'actual_samples': 4932, 'expected_samples': 8760, 'completeness_ratio': 0.563013698630137, 'gap_ratio': 0.0, 'avg_volume': np.float64(2199367.0038523925), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:22:38,215 - advanced_data_fetcher - INFO - Cached data for EDU/USDT 4h
2025-07-28 12:22:38,215 - advanced_data_fetcher - INFO - Fetching 1d data for EDU/USDT
2025-07-28 12:22:38,750 - advanced_data_fetcher - WARNING - Low quality data for EDU/USDT 1d: {'date_range_days': 822, 'min_required_days': 1460, 'actual_samples': 823, 'expected_samples': 1460, 'completeness_ratio': 0.5636986301369863, 'gap_ratio': 0.0, 'avg_volume': np.float64(13180167.755771568), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:22:38,892 - advanced_data_fetcher - INFO - Cached data for EDU/USDT 1d
2025-07-28 12:22:38,892 - advanced_data_fetcher - INFO - Fetching 1h data for EIGEN/USDT
2025-07-28 12:22:42,650 - advanced_data_fetcher - WARNING - Low quality data for EIGEN/USDT 1h: {'date_range_days': 300, 'min_required_days': 1460, 'actual_samples': 7205, 'expected_samples': 35040, 'completeness_ratio': 0.20562214611872145, 'gap_ratio': 0.0, 'avg_volume': np.float64(546807.1371908397), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:22:43,554 - advanced_data_fetcher - INFO - Cached data for EIGEN/USDT 1h
2025-07-28 12:22:43,555 - advanced_data_fetcher - INFO - Fetching 4h data for EIGEN/USDT
2025-07-28 12:22:44,600 - advanced_data_fetcher - WARNING - Low quality data for EIGEN/USDT 4h: {'date_range_days': 300, 'min_required_days': 1460, 'actual_samples': 1802, 'expected_samples': 8760, 'completeness_ratio': 0.20570776255707762, 'gap_ratio': 0.0, 'avg_volume': np.float64(2186318.215016648), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:22:44,850 - advanced_data_fetcher - INFO - Cached data for EIGEN/USDT 4h
2025-07-28 12:22:44,850 - advanced_data_fetcher - INFO - Fetching 1d data for EIGEN/USDT
2025-07-28 12:22:45,340 - advanced_data_fetcher - WARNING - Low quality data for EIGEN/USDT 1d: {'date_range_days': 300, 'min_required_days': 1460, 'actual_samples': 301, 'expected_samples': 1460, 'completeness_ratio': 0.20616438356164385, 'gap_ratio': 0.0, 'avg_volume': np.float64(13088855.22744186), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:22:45,415 - advanced_data_fetcher - INFO - Cached data for EIGEN/USDT 1d
2025-07-28 12:22:45,415 - advanced_data_fetcher - INFO - Fetching 1h data for ENA/USDT
2025-07-28 12:22:51,665 - advanced_data_fetcher - WARNING - Low quality data for ENA/USDT 1h: {'date_range_days': 482, 'min_required_days': 1460, 'actual_samples': 11570, 'expected_samples': 35040, 'completeness_ratio': 0.3301940639269406, 'gap_ratio': 0.0, 'avg_volume': np.float64(7122756.499091615), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:22:52,915 - advanced_data_fetcher - INFO - Cached data for EGLD/USDT 1h
2025-07-28 12:22:52,915 - advanced_data_fetcher - INFO - Fetching 4h data for EGLD/USDT
2025-07-28 12:22:54,180 - advanced_data_fetcher - INFO - Cached data for ENA/USDT 1h
2025-07-28 12:22:54,190 - advanced_data_fetcher - INFO - Fetching 4h data for ENA/USDT
2025-07-28 12:22:55,676 - advanced_data_fetcher - WARNING - Low quality data for ENA/USDT 4h: {'date_range_days': 482, 'min_required_days': 1460, 'actual_samples': 2893, 'expected_samples': 8760, 'completeness_ratio': 0.3302511415525114, 'gap_ratio': 0.0, 'avg_volume': np.float64(28486106.13620463), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:22:56,100 - advanced_data_fetcher - INFO - Cached data for ENA/USDT 4h
2025-07-28 12:22:56,100 - advanced_data_fetcher - INFO - Fetching 1d data for ENA/USDT
2025-07-28 12:22:56,570 - advanced_data_fetcher - WARNING - Low quality data for ENA/USDT 1d: {'date_range_days': 482, 'min_required_days': 1460, 'actual_samples': 483, 'expected_samples': 1460, 'completeness_ratio': 0.3308219178082192, 'gap_ratio': 0.0, 'avg_volume': np.float64(170621749.7648033), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:22:56,665 - advanced_data_fetcher - INFO - Cached data for ENA/USDT 1d
2025-07-28 12:22:56,665 - advanced_data_fetcher - INFO - Fetching 1h data for ENJ/USDT
2025-07-28 12:22:58,765 - advanced_data_fetcher - INFO - Cached data for EGLD/USDT 4h
2025-07-28 12:22:58,765 - advanced_data_fetcher - INFO - Fetching 1d data for EGLD/USDT
2025-07-28 12:23:00,000 - advanced_data_fetcher - INFO - Cached data for EGLD/USDT 1d
2025-07-28 12:23:00,000 - advanced_data_fetcher - INFO - Fetching 1h data for ENS/USDT
2025-07-28 12:23:21,174 - advanced_data_fetcher - INFO - Cached data for ENJ/USDT 1h
2025-07-28 12:23:21,174 - advanced_data_fetcher - INFO - Fetching 4h data for ENJ/USDT
2025-07-28 12:23:23,595 - advanced_data_fetcher - INFO - Cached data for ENS/USDT 1h
2025-07-28 12:23:23,596 - advanced_data_fetcher - INFO - Fetching 4h data for ENS/USDT
2025-07-28 12:23:27,716 - advanced_data_fetcher - INFO - Cached data for ENJ/USDT 4h
2025-07-28 12:23:27,716 - advanced_data_fetcher - INFO - Fetching 1d data for ENJ/USDT
2025-07-28 12:23:29,375 - advanced_data_fetcher - INFO - Cached data for ENJ/USDT 1d
2025-07-28 12:23:29,375 - advanced_data_fetcher - INFO - Fetching 1h data for EPIC/USDT
2025-07-28 12:23:29,950 - advanced_data_fetcher - INFO - Cached data for ENS/USDT 4h
2025-07-28 12:23:29,950 - advanced_data_fetcher - INFO - Fetching 1d data for ENS/USDT
2025-07-28 12:23:31,091 - advanced_data_fetcher - INFO - Cached data for ENS/USDT 1d
2025-07-28 12:23:31,091 - advanced_data_fetcher - INFO - Fetching 1h data for ERA/USDT
2025-07-28 12:23:31,265 - advanced_data_fetcher - WARNING - Low quality data for EPIC/USDT 1h: {'date_range_days': 137, 'min_required_days': 1460, 'actual_samples': 3290, 'expected_samples': 35040, 'completeness_ratio': 0.09389269406392695, 'gap_ratio': 0.0, 'avg_volume': np.float64(111583.30966565348), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:23:31,630 - advanced_data_fetcher - WARNING - Low quality data for ERA/USDT 1h: {'date_range_days': 10, 'min_required_days': 1460, 'actual_samples': 259, 'expected_samples': 35040, 'completeness_ratio': 0.007391552511415525, 'gap_ratio': 0.0, 'avg_volume': np.float64(6572178.870656371), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:23:31,825 - advanced_data_fetcher - INFO - Cached data for ERA/USDT 1h
2025-07-28 12:23:31,825 - advanced_data_fetcher - INFO - Fetching 4h data for ERA/USDT
2025-07-28 12:23:31,900 - advanced_data_fetcher - INFO - Cached data for EPIC/USDT 1h
2025-07-28 12:23:31,900 - advanced_data_fetcher - INFO - Fetching 4h data for EPIC/USDT
2025-07-28 12:23:32,284 - advanced_data_fetcher - WARNING - Low quality data for ERA/USDT 4h: {'date_range_days': 10, 'min_required_days': 1460, 'actual_samples': 66, 'expected_samples': 8760, 'completeness_ratio': 0.007534246575342466, 'gap_ratio': 0.0, 'avg_volume': np.float64(25790863.27878788), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:23:32,350 - advanced_data_fetcher - INFO - Cached data for ERA/USDT 4h
2025-07-28 12:23:32,350 - advanced_data_fetcher - INFO - Fetching 1d data for ERA/USDT
2025-07-28 12:23:32,365 - advanced_data_fetcher - WARNING - Low quality data for EPIC/USDT 4h: {'date_range_days': 137, 'min_required_days': 1460, 'actual_samples': 823, 'expected_samples': 8760, 'completeness_ratio': 0.09394977168949771, 'gap_ratio': 0.0, 'avg_volume': np.float64(446062.07630619686), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:23:32,531 - advanced_data_fetcher - INFO - Cached data for EPIC/USDT 4h
2025-07-28 12:23:32,531 - advanced_data_fetcher - INFO - Fetching 1d data for EPIC/USDT
2025-07-28 12:23:32,765 - advanced_data_fetcher - WARNING - Low quality data for ERA/USDT 1d: {'date_range_days': 11, 'min_required_days': 1460, 'actual_samples': 12, 'expected_samples': 1460, 'completeness_ratio': 0.00821917808219178, 'gap_ratio': 0.0, 'avg_volume': np.float64(141849922.95), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:23:32,831 - advanced_data_fetcher - INFO - Cached data for ERA/USDT 1d
2025-07-28 12:23:32,831 - advanced_data_fetcher - INFO - Fetching 1h data for ETC/USDT
2025-07-28 12:23:32,984 - advanced_data_fetcher - WARNING - Low quality data for EPIC/USDT 1d: {'date_range_days': 137, 'min_required_days': 1460, 'actual_samples': 138, 'expected_samples': 1460, 'completeness_ratio': 0.09452054794520548, 'gap_ratio': 0.0, 'avg_volume': np.float64(2660210.788405797), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:23:33,036 - advanced_data_fetcher - INFO - Cached data for EPIC/USDT 1d
2025-07-28 12:23:33,036 - advanced_data_fetcher - INFO - Fetching 1h data for ETHFI/USDT
2025-07-28 12:23:39,316 - advanced_data_fetcher - WARNING - Low quality data for ETHFI/USDT 1h: {'date_range_days': 496, 'min_required_days': 1460, 'actual_samples': 11926, 'expected_samples': 35040, 'completeness_ratio': 0.34035388127853883, 'gap_ratio': 0.0, 'avg_volume': np.float64(852788.4145815864), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:23:40,815 - advanced_data_fetcher - INFO - Cached data for ETHFI/USDT 1h
2025-07-28 12:23:40,815 - advanced_data_fetcher - INFO - Fetching 4h data for ETHFI/USDT
2025-07-28 12:23:42,276 - advanced_data_fetcher - WARNING - Low quality data for ETHFI/USDT 4h: {'date_range_days': 496, 'min_required_days': 1460, 'actual_samples': 2982, 'expected_samples': 8760, 'completeness_ratio': 0.3404109589041096, 'gap_ratio': 0.0, 'avg_volume': np.float64(3410581.7816230715), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:23:42,729 - advanced_data_fetcher - INFO - Cached data for ETHFI/USDT 4h
2025-07-28 12:23:42,729 - advanced_data_fetcher - INFO - Fetching 1d data for ETHFI/USDT
2025-07-28 12:23:43,205 - advanced_data_fetcher - WARNING - Low quality data for ETHFI/USDT 1d: {'date_range_days': 497, 'min_required_days': 1460, 'actual_samples': 498, 'expected_samples': 1460, 'completeness_ratio': 0.3410958904109589, 'gap_ratio': 0.0, 'avg_volume': np.float64(20422399.342971887), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:23:43,320 - advanced_data_fetcher - INFO - Cached data for ETHFI/USDT 1d
2025-07-28 12:23:43,321 - advanced_data_fetcher - INFO - Fetching 1h data for EUR/USDT
2025-07-28 12:23:56,765 - advanced_data_fetcher - INFO - Cached data for ETC/USDT 1h
2025-07-28 12:23:56,765 - advanced_data_fetcher - INFO - Fetching 4h data for ETC/USDT
2025-07-28 12:24:02,750 - advanced_data_fetcher - INFO - Cached data for ETC/USDT 4h
2025-07-28 12:24:02,750 - advanced_data_fetcher - INFO - Fetching 1d data for ETC/USDT
2025-07-28 12:24:04,800 - advanced_data_fetcher - INFO - Cached data for ETC/USDT 1d
2025-07-28 12:24:04,800 - advanced_data_fetcher - INFO - Fetching 1h data for EURI/USDT
2025-07-28 12:24:07,215 - advanced_data_fetcher - INFO - Cached data for EUR/USDT 1h
2025-07-28 12:24:07,215 - advanced_data_fetcher - INFO - Fetching 4h data for EUR/USDT
2025-07-28 12:24:09,545 - advanced_data_fetcher - WARNING - Low quality data for EURI/USDT 1h: {'date_range_days': 333, 'min_required_days': 1460, 'actual_samples': 8016, 'expected_samples': 35040, 'completeness_ratio': 0.22876712328767124, 'gap_ratio': 0.0, 'avg_volume': np.float64(216699.00343063872), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:24:10,581 - advanced_data_fetcher - INFO - Cached data for EURI/USDT 1h
2025-07-28 12:24:10,581 - advanced_data_fetcher - INFO - Fetching 4h data for EURI/USDT
2025-07-28 12:24:12,331 - advanced_data_fetcher - WARNING - Low quality data for EURI/USDT 4h: {'date_range_days': 334, 'min_required_days': 1460, 'actual_samples': 2005, 'expected_samples': 8760, 'completeness_ratio': 0.2288812785388128, 'gap_ratio': 0.0, 'avg_volume': np.float64(866363.7064837905), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:24:13,450 - advanced_data_fetcher - INFO - Cached data for EUR/USDT 4h
2025-07-28 12:24:13,450 - advanced_data_fetcher - INFO - Fetching 1d data for EUR/USDT
2025-07-28 12:24:13,450 - advanced_data_fetcher - INFO - Cached data for EURI/USDT 4h
2025-07-28 12:24:13,450 - advanced_data_fetcher - INFO - Fetching 1d data for EURI/USDT
2025-07-28 12:24:13,915 - advanced_data_fetcher - WARNING - Low quality data for EURI/USDT 1d: {'date_range_days': 334, 'min_required_days': 1460, 'actual_samples': 335, 'expected_samples': 1460, 'completeness_ratio': 0.22945205479452055, 'gap_ratio': 0.0, 'avg_volume': np.float64(5185251.437313433), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:24:14,015 - advanced_data_fetcher - INFO - Cached data for EURI/USDT 1d
2025-07-28 12:24:14,015 - advanced_data_fetcher - INFO - Fetching 1h data for FARM/USDT
2025-07-28 12:24:14,650 - advanced_data_fetcher - INFO - Cached data for EUR/USDT 1d
2025-07-28 12:24:14,650 - advanced_data_fetcher - INFO - Fetching 1h data for FDUSD/USDT
2025-07-28 12:24:23,816 - advanced_data_fetcher - WARNING - Low quality data for FDUSD/USDT 1h: {'date_range_days': 733, 'min_required_days': 1460, 'actual_samples': 17590, 'expected_samples': 35040, 'completeness_ratio': 0.5019977168949772, 'gap_ratio': 0.00022735023303398886, 'avg_volume': np.float64(23225288.140306994), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:24:26,050 - advanced_data_fetcher - INFO - Cached data for FDUSD/USDT 1h
2025-07-28 12:24:26,050 - advanced_data_fetcher - INFO - Fetching 4h data for FDUSD/USDT
2025-07-28 12:24:28,416 - advanced_data_fetcher - WARNING - Low quality data for FDUSD/USDT 4h: {'date_range_days': 733, 'min_required_days': 1460, 'actual_samples': 4399, 'expected_samples': 8760, 'completeness_ratio': 0.5021689497716895, 'gap_ratio': 0.0, 'avg_volume': np.float64(92869474.8481473), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:24:29,065 - advanced_data_fetcher - INFO - Cached data for FDUSD/USDT 4h
2025-07-28 12:24:29,065 - advanced_data_fetcher - INFO - Fetching 1d data for FDUSD/USDT
2025-07-28 12:24:29,570 - advanced_data_fetcher - WARNING - Low quality data for FDUSD/USDT 1d: {'date_range_days': 733, 'min_required_days': 1460, 'actual_samples': 734, 'expected_samples': 1460, 'completeness_ratio': 0.5027397260273972, 'gap_ratio': 0.0, 'avg_volume': np.float64(556584224.3814714), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:24:29,710 - advanced_data_fetcher - INFO - Cached data for FDUSD/USDT 1d
2025-07-28 12:24:29,710 - advanced_data_fetcher - INFO - Fetching 1h data for FET/USDT
2025-07-28 12:24:36,274 - advanced_data_fetcher - INFO - Cached data for FARM/USDT 1h
2025-07-28 12:24:36,274 - advanced_data_fetcher - INFO - Fetching 4h data for FARM/USDT
2025-07-28 12:24:42,165 - advanced_data_fetcher - INFO - Cached data for FARM/USDT 4h
2025-07-28 12:24:42,165 - advanced_data_fetcher - INFO - Fetching 1d data for FARM/USDT
2025-07-28 12:24:43,400 - advanced_data_fetcher - INFO - Cached data for FARM/USDT 1d
2025-07-28 12:24:43,400 - advanced_data_fetcher - INFO - Fetching 1h data for FIDA/USDT
2025-07-28 12:24:53,481 - advanced_data_fetcher - INFO - Cached data for FET/USDT 1h
2025-07-28 12:24:53,481 - advanced_data_fetcher - INFO - Fetching 4h data for FET/USDT
2025-07-28 12:24:58,950 - advanced_data_fetcher - INFO - Cached data for FET/USDT 4h
2025-07-28 12:24:58,950 - advanced_data_fetcher - INFO - Fetching 1d data for FET/USDT
2025-07-28 12:25:00,100 - advanced_data_fetcher - INFO - Cached data for FET/USDT 1d
2025-07-28 12:25:00,100 - advanced_data_fetcher - INFO - Fetching 1h data for FIL/USDT
2025-07-28 12:25:06,350 - advanced_data_fetcher - INFO - Cached data for FIDA/USDT 1h
2025-07-28 12:25:06,350 - advanced_data_fetcher - INFO - Fetching 4h data for FIDA/USDT
2025-07-28 12:25:12,165 - advanced_data_fetcher - INFO - Cached data for FIDA/USDT 4h
2025-07-28 12:25:12,165 - advanced_data_fetcher - INFO - Fetching 1d data for FIDA/USDT
2025-07-28 12:25:13,365 - advanced_data_fetcher - INFO - Cached data for FIDA/USDT 1d
2025-07-28 12:25:13,365 - advanced_data_fetcher - INFO - Fetching 1h data for FIO/USDT
2025-07-28 12:25:23,141 - advanced_data_fetcher - INFO - Cached data for FIL/USDT 1h
2025-07-28 12:25:23,141 - advanced_data_fetcher - INFO - Fetching 4h data for FIL/USDT
2025-07-28 12:25:28,586 - advanced_data_fetcher - INFO - Cached data for FIL/USDT 4h
2025-07-28 12:25:28,600 - advanced_data_fetcher - INFO - Fetching 1d data for FIL/USDT
2025-07-28 12:25:29,781 - advanced_data_fetcher - INFO - Cached data for FIL/USDT 1d
2025-07-28 12:25:29,781 - advanced_data_fetcher - INFO - Fetching 1h data for FIS/USDT
2025-07-28 12:25:36,415 - advanced_data_fetcher - INFO - Cached data for FIO/USDT 1h
2025-07-28 12:25:36,415 - advanced_data_fetcher - INFO - Fetching 4h data for FIO/USDT
2025-07-28 12:25:41,965 - advanced_data_fetcher - INFO - Cached data for FIO/USDT 4h
2025-07-28 12:25:41,965 - advanced_data_fetcher - INFO - Fetching 1d data for FIO/USDT
2025-07-28 12:25:43,215 - advanced_data_fetcher - INFO - Cached data for FIO/USDT 1d
2025-07-28 12:25:43,215 - advanced_data_fetcher - INFO - Fetching 1h data for FLM/USDT
2025-07-28 12:25:52,354 - advanced_data_fetcher - INFO - Cached data for FIS/USDT 1h
2025-07-28 12:25:52,354 - advanced_data_fetcher - INFO - Fetching 4h data for FIS/USDT
2025-07-28 12:25:58,050 - advanced_data_fetcher - INFO - Cached data for FIS/USDT 4h
2025-07-28 12:25:58,050 - advanced_data_fetcher - INFO - Fetching 1d data for FIS/USDT
2025-07-28 12:25:59,465 - advanced_data_fetcher - INFO - Cached data for FIS/USDT 1d
2025-07-28 12:25:59,481 - advanced_data_fetcher - INFO - Fetching 1h data for FLOKI/USDT
2025-07-28 12:26:06,521 - advanced_data_fetcher - INFO - Cached data for FLM/USDT 1h
2025-07-28 12:26:06,523 - advanced_data_fetcher - INFO - Fetching 4h data for FLM/USDT
2025-07-28 12:26:10,150 - advanced_data_fetcher - WARNING - Low quality data for FLOKI/USDT 1h: {'date_range_days': 814, 'min_required_days': 1460, 'actual_samples': 19552, 'expected_samples': 35040, 'completeness_ratio': 0.5579908675799087, 'gap_ratio': 0.0, 'avg_volume': np.float64(15919837331.80043), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:26:12,975 - advanced_data_fetcher - INFO - Cached data for FLM/USDT 4h
2025-07-28 12:26:12,975 - advanced_data_fetcher - INFO - Fetching 1d data for FLM/USDT
2025-07-28 12:26:14,050 - advanced_data_fetcher - INFO - Cached data for FLOKI/USDT 1h
2025-07-28 12:26:14,050 - advanced_data_fetcher - INFO - Fetching 4h data for FLOKI/USDT
2025-07-28 12:26:14,344 - advanced_data_fetcher - INFO - Cached data for FLM/USDT 1d
2025-07-28 12:26:14,345 - advanced_data_fetcher - INFO - Fetching 1h data for FLOW/USDT
2025-07-28 12:26:16,850 - advanced_data_fetcher - WARNING - Low quality data for FLOKI/USDT 4h: {'date_range_days': 814, 'min_required_days': 1460, 'actual_samples': 4889, 'expected_samples': 8760, 'completeness_ratio': 0.5581050228310502, 'gap_ratio': 0.0, 'avg_volume': np.float64(63666324837.22724), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:26:17,556 - advanced_data_fetcher - INFO - Cached data for FLOKI/USDT 4h
2025-07-28 12:26:17,556 - advanced_data_fetcher - INFO - Fetching 1d data for FLOKI/USDT
2025-07-28 12:26:18,067 - advanced_data_fetcher - WARNING - Low quality data for FLOKI/USDT 1d: {'date_range_days': 815, 'min_required_days': 1460, 'actual_samples': 816, 'expected_samples': 1460, 'completeness_ratio': 0.5589041095890411, 'gap_ratio': 0.0, 'avg_volume': np.float64(381451793055.5637), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:26:18,222 - advanced_data_fetcher - INFO - Cached data for FLOKI/USDT 1d
2025-07-28 12:26:18,222 - advanced_data_fetcher - INFO - Fetching 1h data for FLUX/USDT
2025-07-28 12:26:39,050 - advanced_data_fetcher - INFO - Cached data for FLOW/USDT 1h
2025-07-28 12:26:39,050 - advanced_data_fetcher - INFO - Fetching 4h data for FLOW/USDT
2025-07-28 12:26:41,300 - advanced_data_fetcher - INFO - Cached data for FLUX/USDT 1h
2025-07-28 12:26:41,300 - advanced_data_fetcher - INFO - Fetching 4h data for FLUX/USDT
2025-07-28 12:26:45,400 - advanced_data_fetcher - INFO - Cached data for FLOW/USDT 4h
2025-07-28 12:26:45,400 - advanced_data_fetcher - INFO - Fetching 1d data for FLOW/USDT
2025-07-28 12:26:46,431 - advanced_data_fetcher - INFO - Cached data for FLUX/USDT 4h
2025-07-28 12:26:46,431 - advanced_data_fetcher - INFO - Fetching 1d data for FLUX/USDT
2025-07-28 12:26:46,631 - advanced_data_fetcher - INFO - Cached data for FLOW/USDT 1d
2025-07-28 12:26:46,631 - advanced_data_fetcher - INFO - Fetching 1h data for FORM/USDT
2025-07-28 12:26:47,670 - advanced_data_fetcher - INFO - Cached data for FLUX/USDT 1d
2025-07-28 12:26:47,670 - advanced_data_fetcher - INFO - Fetching 1h data for FORTH/USDT
2025-07-28 12:26:48,578 - advanced_data_fetcher - WARNING - Low quality data for FORM/USDT 1h: {'date_range_days': 131, 'min_required_days': 1460, 'actual_samples': 3146, 'expected_samples': 35040, 'completeness_ratio': 0.08978310502283104, 'gap_ratio': 0.0, 'avg_volume': np.float64(140077.0591226955), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:26:49,016 - advanced_data_fetcher - INFO - Cached data for FORM/USDT 1h
2025-07-28 12:26:49,016 - advanced_data_fetcher - INFO - Fetching 4h data for FORM/USDT
2025-07-28 12:26:49,515 - advanced_data_fetcher - WARNING - Low quality data for FORM/USDT 4h: {'date_range_days': 131, 'min_required_days': 1460, 'actual_samples': 787, 'expected_samples': 8760, 'completeness_ratio': 0.08984018264840182, 'gap_ratio': 0.0, 'avg_volume': np.float64(559952.2592121982), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:26:49,695 - advanced_data_fetcher - INFO - Cached data for FORM/USDT 4h
2025-07-28 12:26:49,695 - advanced_data_fetcher - INFO - Fetching 1d data for FORM/USDT
2025-07-28 12:26:50,150 - advanced_data_fetcher - WARNING - Low quality data for FORM/USDT 1d: {'date_range_days': 131, 'min_required_days': 1460, 'actual_samples': 132, 'expected_samples': 1460, 'completeness_ratio': 0.09041095890410959, 'gap_ratio': 0.0, 'avg_volume': np.float64(3338503.242424242), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:26:50,229 - advanced_data_fetcher - INFO - Cached data for FORM/USDT 1d
2025-07-28 12:26:50,229 - advanced_data_fetcher - INFO - Fetching 1h data for FTT/USDT
2025-07-28 12:27:11,436 - advanced_data_fetcher - INFO - Cached data for FTT/USDT 1h
2025-07-28 12:27:11,436 - advanced_data_fetcher - INFO - Fetching 4h data for FTT/USDT
2025-07-28 12:27:12,770 - advanced_data_fetcher - INFO - Cached data for FORTH/USDT 1h
2025-07-28 12:27:12,770 - advanced_data_fetcher - INFO - Fetching 4h data for FORTH/USDT
2025-07-28 12:27:16,931 - advanced_data_fetcher - INFO - Cached data for FTT/USDT 4h
2025-07-28 12:27:16,931 - advanced_data_fetcher - INFO - Fetching 1d data for FTT/USDT
2025-07-28 12:27:18,460 - advanced_data_fetcher - INFO - Cached data for FTT/USDT 1d
2025-07-28 12:27:18,465 - advanced_data_fetcher - INFO - Fetching 1h data for FUN/USDT
2025-07-28 12:27:18,650 - advanced_data_fetcher - INFO - Cached data for FORTH/USDT 4h
2025-07-28 12:27:18,650 - advanced_data_fetcher - INFO - Fetching 1d data for FORTH/USDT
2025-07-28 12:27:19,850 - advanced_data_fetcher - INFO - Cached data for FORTH/USDT 1d
2025-07-28 12:27:19,865 - advanced_data_fetcher - INFO - Fetching 1h data for FXS/USDT
2025-07-28 12:27:41,916 - advanced_data_fetcher - INFO - Cached data for FXS/USDT 1h
2025-07-28 12:27:41,945 - advanced_data_fetcher - INFO - Fetching 4h data for FXS/USDT
2025-07-28 12:27:44,572 - advanced_data_fetcher - INFO - Cached data for FUN/USDT 1h
2025-07-28 12:27:44,572 - advanced_data_fetcher - INFO - Fetching 4h data for FUN/USDT
2025-07-28 12:27:47,765 - advanced_data_fetcher - INFO - Cached data for FXS/USDT 4h
2025-07-28 12:27:47,781 - advanced_data_fetcher - INFO - Fetching 1d data for FXS/USDT
2025-07-28 12:27:48,934 - advanced_data_fetcher - INFO - Cached data for FXS/USDT 1d
2025-07-28 12:27:48,934 - advanced_data_fetcher - INFO - Fetching 1h data for G/USDT
2025-07-28 12:27:50,800 - advanced_data_fetcher - INFO - Cached data for FUN/USDT 4h
2025-07-28 12:27:50,800 - advanced_data_fetcher - INFO - Fetching 1d data for FUN/USDT
2025-07-28 12:27:52,000 - advanced_data_fetcher - INFO - Cached data for FUN/USDT 1d
2025-07-28 12:27:52,000 - advanced_data_fetcher - INFO - Fetching 1h data for GALA/USDT
2025-07-28 12:27:53,566 - advanced_data_fetcher - WARNING - Low quality data for G/USDT 1h: {'date_range_days': 374, 'min_required_days': 1460, 'actual_samples': 8978, 'expected_samples': 35040, 'completeness_ratio': 0.2562214611872146, 'gap_ratio': 0.0, 'avg_volume': np.float64(4818660.164401871), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:27:54,800 - advanced_data_fetcher - INFO - Cached data for G/USDT 1h
2025-07-28 12:27:54,800 - advanced_data_fetcher - INFO - Fetching 4h data for G/USDT
2025-07-28 12:27:56,216 - advanced_data_fetcher - WARNING - Low quality data for G/USDT 4h: {'date_range_days': 374, 'min_required_days': 1460, 'actual_samples': 2245, 'expected_samples': 8760, 'completeness_ratio': 0.2562785388127854, 'gap_ratio': 0.0, 'avg_volume': np.float64(19270347.864587974), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:27:56,559 - advanced_data_fetcher - INFO - Cached data for G/USDT 4h
2025-07-28 12:27:56,560 - advanced_data_fetcher - INFO - Fetching 1d data for G/USDT
2025-07-28 12:27:57,024 - advanced_data_fetcher - WARNING - Low quality data for G/USDT 1d: {'date_range_days': 374, 'min_required_days': 1460, 'actual_samples': 375, 'expected_samples': 1460, 'completeness_ratio': 0.2568493150684932, 'gap_ratio': 0.0, 'avg_volume': np.float64(115365149.216), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:27:57,125 - advanced_data_fetcher - INFO - Cached data for G/USDT 1d
2025-07-28 12:27:57,141 - advanced_data_fetcher - INFO - Fetching 1h data for GAS/USDT
2025-07-28 12:28:07,385 - advanced_data_fetcher - WARNING - Low quality data for GAS/USDT 1h: {'date_range_days': 864, 'min_required_days': 1460, 'actual_samples': 20737, 'expected_samples': 35040, 'completeness_ratio': 0.5918093607305936, 'gap_ratio': 4.8220657729771434e-05, 'avg_volume': np.float64(52473.23128224913), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:28:11,125 - advanced_data_fetcher - INFO - Cached data for GAS/USDT 1h
2025-07-28 12:28:11,125 - advanced_data_fetcher - INFO - Fetching 4h data for GAS/USDT
2025-07-28 12:28:14,625 - advanced_data_fetcher - WARNING - Low quality data for GAS/USDT 4h: {'date_range_days': 864, 'min_required_days': 1460, 'actual_samples': 5185, 'expected_samples': 8760, 'completeness_ratio': 0.5918949771689498, 'gap_ratio': 0.0, 'avg_volume': np.float64(209862.5645323047), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:28:14,945 - advanced_data_fetcher - INFO - Cached data for GALA/USDT 1h
2025-07-28 12:28:14,949 - advanced_data_fetcher - INFO - Fetching 4h data for GALA/USDT
2025-07-28 12:28:15,556 - advanced_data_fetcher - INFO - Cached data for GAS/USDT 4h
2025-07-28 12:28:15,556 - advanced_data_fetcher - INFO - Fetching 1d data for GAS/USDT
2025-07-28 12:28:16,060 - advanced_data_fetcher - WARNING - Low quality data for GAS/USDT 1d: {'date_range_days': 864, 'min_required_days': 1460, 'actual_samples': 865, 'expected_samples': 1460, 'completeness_ratio': 0.5924657534246576, 'gap_ratio': 0.0, 'avg_volume': np.float64(1257962.308786127), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:28:16,200 - advanced_data_fetcher - INFO - Cached data for GAS/USDT 1d
2025-07-28 12:28:16,200 - advanced_data_fetcher - INFO - Fetching 1h data for GHST/USDT
2025-07-28 12:28:20,431 - advanced_data_fetcher - INFO - Cached data for GALA/USDT 4h
2025-07-28 12:28:20,431 - advanced_data_fetcher - INFO - Fetching 1d data for GALA/USDT
2025-07-28 12:28:21,665 - advanced_data_fetcher - INFO - Cached data for GALA/USDT 1d
2025-07-28 12:28:21,665 - advanced_data_fetcher - INFO - Fetching 1h data for GLM/USDT
2025-07-28 12:28:31,781 - advanced_data_fetcher - WARNING - Low quality data for GLM/USDT 1h: {'date_range_days': 864, 'min_required_days': 1460, 'actual_samples': 20737, 'expected_samples': 35040, 'completeness_ratio': 0.5918093607305936, 'gap_ratio': 4.8220657729771434e-05, 'avg_volume': np.float64(438555.02882287704), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:28:35,715 - advanced_data_fetcher - INFO - Cached data for GLM/USDT 1h
2025-07-28 12:28:35,715 - advanced_data_fetcher - INFO - Fetching 4h data for GLM/USDT
2025-07-28 12:28:39,015 - advanced_data_fetcher - INFO - Cached data for GHST/USDT 1h
2025-07-28 12:28:39,015 - advanced_data_fetcher - INFO - Fetching 4h data for GHST/USDT
2025-07-28 12:28:39,140 - advanced_data_fetcher - WARNING - Low quality data for GLM/USDT 4h: {'date_range_days': 864, 'min_required_days': 1460, 'actual_samples': 5185, 'expected_samples': 8760, 'completeness_ratio': 0.5918949771689498, 'gap_ratio': 0.0, 'avg_volume': np.float64(1753966.3708196722), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:28:39,800 - advanced_data_fetcher - INFO - Cached data for GLM/USDT 4h
2025-07-28 12:28:39,800 - advanced_data_fetcher - INFO - Fetching 1d data for GLM/USDT
2025-07-28 12:28:40,281 - advanced_data_fetcher - WARNING - Low quality data for GLM/USDT 1d: {'date_range_days': 864, 'min_required_days': 1460, 'actual_samples': 865, 'expected_samples': 1460, 'completeness_ratio': 0.5924657534246576, 'gap_ratio': 0.0, 'avg_volume': np.float64(10513659.690982658), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:28:40,446 - advanced_data_fetcher - INFO - Cached data for GLM/USDT 1d
2025-07-28 12:28:40,446 - advanced_data_fetcher - INFO - Fetching 1h data for GLMR/USDT
2025-07-28 12:28:45,465 - advanced_data_fetcher - INFO - Cached data for GHST/USDT 4h
2025-07-28 12:28:45,465 - advanced_data_fetcher - INFO - Fetching 1d data for GHST/USDT
2025-07-28 12:28:46,640 - advanced_data_fetcher - INFO - Cached data for GHST/USDT 1d
2025-07-28 12:28:46,640 - advanced_data_fetcher - INFO - Fetching 1h data for GMT/USDT
2025-07-28 12:29:00,441 - advanced_data_fetcher - INFO - Cached data for GLMR/USDT 1h
2025-07-28 12:29:00,445 - advanced_data_fetcher - INFO - Fetching 4h data for GLMR/USDT
2025-07-28 12:29:06,587 - advanced_data_fetcher - INFO - Cached data for GMT/USDT 1h
2025-07-28 12:29:06,587 - advanced_data_fetcher - INFO - Fetching 4h data for GMT/USDT
2025-07-28 12:29:07,065 - advanced_data_fetcher - INFO - Cached data for GLMR/USDT 4h
2025-07-28 12:29:07,065 - advanced_data_fetcher - INFO - Fetching 1d data for GLMR/USDT
2025-07-28 12:29:08,200 - advanced_data_fetcher - INFO - Cached data for GLMR/USDT 1d
2025-07-28 12:29:08,200 - advanced_data_fetcher - INFO - Fetching 1h data for GMX/USDT
2025-07-28 12:29:11,681 - advanced_data_fetcher - INFO - Cached data for GMT/USDT 4h
2025-07-28 12:29:11,681 - advanced_data_fetcher - INFO - Fetching 1d data for GMT/USDT
2025-07-28 12:29:12,850 - advanced_data_fetcher - INFO - Cached data for GMT/USDT 1d
2025-07-28 12:29:12,850 - advanced_data_fetcher - INFO - Fetching 1h data for GNO/USDT
2025-07-28 12:29:20,716 - advanced_data_fetcher - WARNING - Low quality data for GMX/USDT 1h: {'date_range_days': 1026, 'min_required_days': 1460, 'actual_samples': 24647, 'expected_samples': 35040, 'completeness_ratio': 0.7033961187214612, 'gap_ratio': 4.057124310288867e-05, 'avg_volume': np.float64(6120.8117276341945), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:29:24,040 - advanced_data_fetcher - INFO - Cached data for GMX/USDT 1h
2025-07-28 12:29:24,040 - advanced_data_fetcher - INFO - Fetching 4h data for GMX/USDT
2025-07-28 12:29:27,531 - advanced_data_fetcher - WARNING - Low quality data for GMX/USDT 4h: {'date_range_days': 1027, 'min_required_days': 1460, 'actual_samples': 6163, 'expected_samples': 8760, 'completeness_ratio': 0.7035388127853881, 'gap_ratio': 0.0, 'avg_volume': np.float64(24478.28113759533), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:29:28,330 - advanced_data_fetcher - INFO - Cached data for GMX/USDT 4h
2025-07-28 12:29:28,331 - advanced_data_fetcher - INFO - Fetching 1d data for GMX/USDT
2025-07-28 12:29:29,250 - advanced_data_fetcher - WARNING - Low quality data for GMX/USDT 1d: {'date_range_days': 1027, 'min_required_days': 1460, 'actual_samples': 1028, 'expected_samples': 1460, 'completeness_ratio': 0.7041095890410959, 'gap_ratio': 0.0, 'avg_volume': np.float64(146750.6290379377), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:29:29,415 - advanced_data_fetcher - INFO - Cached data for GMX/USDT 1d
2025-07-28 12:29:29,415 - advanced_data_fetcher - INFO - Fetching 1h data for GNS/USDT
2025-07-28 12:29:31,200 - advanced_data_fetcher - WARNING - Low quality data for GNO/USDT 1h: {'date_range_days': 1428, 'min_required_days': 1460, 'actual_samples': 34273, 'expected_samples': 35040, 'completeness_ratio': 0.9781107305936073, 'gap_ratio': 8.7524798692963e-05, 'avg_volume': np.float64(134.42944950835934), 'quality_score': np.float64(0.5), 'is_high_quality': np.False_}
2025-07-28 12:29:35,583 - advanced_data_fetcher - INFO - Cached data for GNO/USDT 1h
2025-07-28 12:29:35,583 - advanced_data_fetcher - INFO - Fetching 4h data for GNO/USDT
2025-07-28 12:29:40,799 - advanced_data_fetcher - WARNING - Low quality data for GNO/USDT 4h: {'date_range_days': 1428, 'min_required_days': 1460, 'actual_samples': 8570, 'expected_samples': 8760, 'completeness_ratio': 0.978310502283105, 'gap_ratio': 0.0, 'avg_volume': np.float64(537.6079956826138), 'quality_score': np.float64(0.5), 'is_high_quality': np.False_}
2025-07-28 12:29:41,865 - advanced_data_fetcher - WARNING - Low quality data for GNS/USDT 1h: {'date_range_days': 891, 'min_required_days': 1460, 'actual_samples': 21406, 'expected_samples': 35040, 'completeness_ratio': 0.6109018264840183, 'gap_ratio': 4.671369178306161e-05, 'avg_volume': np.float64(18820.476574792112), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:29:42,081 - advanced_data_fetcher - INFO - Cached data for GNO/USDT 4h
2025-07-28 12:29:42,081 - advanced_data_fetcher - INFO - Fetching 1d data for GNO/USDT
2025-07-28 12:29:44,099 - advanced_data_fetcher - INFO - Cached data for GNO/USDT 1d
2025-07-28 12:29:44,099 - advanced_data_fetcher - INFO - Fetching 1h data for GPS/USDT
2025-07-28 12:29:45,216 - advanced_data_fetcher - INFO - Cached data for GNS/USDT 1h
2025-07-28 12:29:45,216 - advanced_data_fetcher - INFO - Fetching 4h data for GNS/USDT
2025-07-28 12:29:46,250 - advanced_data_fetcher - WARNING - Low quality data for GPS/USDT 1h: {'date_range_days': 145, 'min_required_days': 1460, 'actual_samples': 3501, 'expected_samples': 35040, 'completeness_ratio': 0.09991438356164384, 'gap_ratio': 0.0, 'avg_volume': np.float64(6463389.561411025), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:29:46,733 - advanced_data_fetcher - INFO - Cached data for GPS/USDT 1h
2025-07-28 12:29:46,733 - advanced_data_fetcher - INFO - Fetching 4h data for GPS/USDT
2025-07-28 12:29:47,282 - advanced_data_fetcher - WARNING - Low quality data for GPS/USDT 4h: {'date_range_days': 145, 'min_required_days': 1460, 'actual_samples': 876, 'expected_samples': 8760, 'completeness_ratio': 0.1, 'gap_ratio': 0.0, 'avg_volume': np.float64(25831423.349885844), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:29:47,447 - advanced_data_fetcher - INFO - Cached data for GPS/USDT 4h
2025-07-28 12:29:47,450 - advanced_data_fetcher - INFO - Fetching 1d data for GPS/USDT
2025-07-28 12:29:47,871 - advanced_data_fetcher - WARNING - Low quality data for GPS/USDT 1d: {'date_range_days': 146, 'min_required_days': 1460, 'actual_samples': 147, 'expected_samples': 1460, 'completeness_ratio': 0.10068493150684932, 'gap_ratio': 0.0, 'avg_volume': np.float64(153934196.28911564), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:29:47,950 - advanced_data_fetcher - INFO - Cached data for GPS/USDT 1d
2025-07-28 12:29:47,950 - advanced_data_fetcher - INFO - Fetching 1h data for GRT/USDT
2025-07-28 12:29:48,215 - advanced_data_fetcher - WARNING - Low quality data for GNS/USDT 4h: {'date_range_days': 892, 'min_required_days': 1460, 'actual_samples': 5353, 'expected_samples': 8760, 'completeness_ratio': 0.6110730593607306, 'gap_ratio': 0.0, 'avg_volume': np.float64(75260.8110517467), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:29:49,000 - advanced_data_fetcher - INFO - Cached data for GNS/USDT 4h
2025-07-28 12:29:49,000 - advanced_data_fetcher - INFO - Fetching 1d data for GNS/USDT
2025-07-28 12:29:49,500 - advanced_data_fetcher - WARNING - Low quality data for GNS/USDT 1d: {'date_range_days': 892, 'min_required_days': 1460, 'actual_samples': 893, 'expected_samples': 1460, 'completeness_ratio': 0.6116438356164383, 'gap_ratio': 0.0, 'avg_volume': np.float64(451143.4731914893), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:29:49,675 - advanced_data_fetcher - INFO - Cached data for GNS/USDT 1d
2025-07-28 12:29:49,675 - advanced_data_fetcher - INFO - Fetching 1h data for GTC/USDT
2025-07-28 12:30:14,425 - advanced_data_fetcher - INFO - Cached data for GRT/USDT 1h
2025-07-28 12:30:14,425 - advanced_data_fetcher - INFO - Fetching 4h data for GRT/USDT
2025-07-28 12:30:15,565 - advanced_data_fetcher - INFO - Cached data for GTC/USDT 1h
2025-07-28 12:30:15,565 - advanced_data_fetcher - INFO - Fetching 4h data for GTC/USDT
2025-07-28 12:30:20,181 - advanced_data_fetcher - INFO - Cached data for GRT/USDT 4h
2025-07-28 12:30:20,181 - advanced_data_fetcher - INFO - Fetching 1d data for GRT/USDT
2025-07-28 12:30:21,381 - advanced_data_fetcher - INFO - Cached data for GTC/USDT 4h
2025-07-28 12:30:21,381 - advanced_data_fetcher - INFO - Fetching 1d data for GTC/USDT
2025-07-28 12:30:21,550 - advanced_data_fetcher - INFO - Cached data for GRT/USDT 1d
2025-07-28 12:30:21,550 - advanced_data_fetcher - INFO - Fetching 1h data for GUN/USDT
2025-07-28 12:30:22,681 - advanced_data_fetcher - INFO - Cached data for GTC/USDT 1d
2025-07-28 12:30:22,681 - advanced_data_fetcher - INFO - Fetching 1h data for HAEDAL/USDT
2025-07-28 12:30:23,062 - advanced_data_fetcher - WARNING - Low quality data for GUN/USDT 1h: {'date_range_days': 118, 'min_required_days': 1460, 'actual_samples': 2853, 'expected_samples': 35040, 'completeness_ratio': 0.08142123287671232, 'gap_ratio': 0.0, 'avg_volume': np.float64(12881376.231685944), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:30:23,597 - advanced_data_fetcher - INFO - Cached data for GUN/USDT 1h
2025-07-28 12:30:23,597 - advanced_data_fetcher - INFO - Fetching 4h data for GUN/USDT
2025-07-28 12:30:23,766 - advanced_data_fetcher - WARNING - Low quality data for HAEDAL/USDT 1h: {'date_range_days': 67, 'min_required_days': 1460, 'actual_samples': 1627, 'expected_samples': 35040, 'completeness_ratio': 0.04643264840182648, 'gap_ratio': 0.0, 'avg_volume': np.float64(2337272.9926859247), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:30:24,016 - advanced_data_fetcher - INFO - Cached data for HAEDAL/USDT 1h
2025-07-28 12:30:24,016 - advanced_data_fetcher - INFO - Fetching 4h data for HAEDAL/USDT
2025-07-28 12:30:24,088 - advanced_data_fetcher - WARNING - Low quality data for GUN/USDT 4h: {'date_range_days': 118, 'min_required_days': 1460, 'actual_samples': 714, 'expected_samples': 8760, 'completeness_ratio': 0.08150684931506849, 'gap_ratio': 0.0, 'avg_volume': np.float64(51471381.49719888), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:30:24,275 - advanced_data_fetcher - INFO - Cached data for GUN/USDT 4h
2025-07-28 12:30:24,275 - advanced_data_fetcher - INFO - Fetching 1d data for GUN/USDT
2025-07-28 12:30:24,481 - advanced_data_fetcher - WARNING - Low quality data for HAEDAL/USDT 4h: {'date_range_days': 67, 'min_required_days': 1460, 'actual_samples': 408, 'expected_samples': 8760, 'completeness_ratio': 0.04657534246575343, 'gap_ratio': 0.0, 'avg_volume': np.float64(9320448.919362744), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:30:24,616 - advanced_data_fetcher - INFO - Cached data for HAEDAL/USDT 4h
2025-07-28 12:30:24,616 - advanced_data_fetcher - INFO - Fetching 1d data for HAEDAL/USDT
2025-07-28 12:30:24,700 - advanced_data_fetcher - WARNING - Low quality data for GUN/USDT 1d: {'date_range_days': 119, 'min_required_days': 1460, 'actual_samples': 120, 'expected_samples': 1460, 'completeness_ratio': 0.0821917808219178, 'gap_ratio': 0.0, 'avg_volume': np.float64(306254719.90833336), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:30:24,800 - advanced_data_fetcher - INFO - Cached data for GUN/USDT 1d
2025-07-28 12:30:24,800 - advanced_data_fetcher - INFO - Fetching 1h data for HBAR/USDT
2025-07-28 12:30:25,091 - advanced_data_fetcher - WARNING - Low quality data for HAEDAL/USDT 1d: {'date_range_days': 68, 'min_required_days': 1460, 'actual_samples': 69, 'expected_samples': 1460, 'completeness_ratio': 0.04726027397260274, 'gap_ratio': 0.0, 'avg_volume': np.float64(55112219.69710145), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:30:25,140 - advanced_data_fetcher - INFO - Cached data for HAEDAL/USDT 1d
2025-07-28 12:30:25,140 - advanced_data_fetcher - INFO - Fetching 1h data for HEI/USDT
2025-07-28 12:30:27,100 - advanced_data_fetcher - WARNING - Low quality data for HEI/USDT 1h: {'date_range_days': 165, 'min_required_days': 1460, 'actual_samples': 3962, 'expected_samples': 35040, 'completeness_ratio': 0.11307077625570776, 'gap_ratio': 0.0, 'avg_volume': np.float64(354500.41648157494), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:30:27,675 - advanced_data_fetcher - INFO - Cached data for HEI/USDT 1h
2025-07-28 12:30:27,675 - advanced_data_fetcher - INFO - Fetching 4h data for HEI/USDT
2025-07-28 12:30:28,185 - advanced_data_fetcher - WARNING - Low quality data for HEI/USDT 4h: {'date_range_days': 165, 'min_required_days': 1460, 'actual_samples': 991, 'expected_samples': 8760, 'completeness_ratio': 0.11312785388127854, 'gap_ratio': 0.0, 'avg_volume': np.float64(1417286.226135217), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:30:28,390 - advanced_data_fetcher - INFO - Cached data for HEI/USDT 4h
2025-07-28 12:30:28,400 - advanced_data_fetcher - INFO - Fetching 1d data for HEI/USDT
2025-07-28 12:30:28,843 - advanced_data_fetcher - WARNING - Low quality data for HEI/USDT 1d: {'date_range_days': 165, 'min_required_days': 1460, 'actual_samples': 166, 'expected_samples': 1460, 'completeness_ratio': 0.1136986301369863, 'gap_ratio': 0.0, 'avg_volume': np.float64(8461028.012650602), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:30:28,900 - advanced_data_fetcher - INFO - Cached data for HEI/USDT 1d
2025-07-28 12:30:28,900 - advanced_data_fetcher - INFO - Fetching 1h data for HFT/USDT
2025-07-28 12:30:40,816 - advanced_data_fetcher - WARNING - Low quality data for HFT/USDT 1h: {'date_range_days': 993, 'min_required_days': 1460, 'actual_samples': 23852, 'expected_samples': 35040, 'completeness_ratio': 0.6807077625570777, 'gap_ratio': 4.192344778434578e-05, 'avg_volume': np.float64(815598.1476479959), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:30:44,874 - advanced_data_fetcher - INFO - Cached data for HFT/USDT 1h
2025-07-28 12:30:44,874 - advanced_data_fetcher - INFO - Fetching 4h data for HFT/USDT
2025-07-28 12:30:48,184 - advanced_data_fetcher - WARNING - Low quality data for HFT/USDT 4h: {'date_range_days': 993, 'min_required_days': 1460, 'actual_samples': 5964, 'expected_samples': 8760, 'completeness_ratio': 0.6808219178082192, 'gap_ratio': 0.0, 'avg_volume': np.float64(3261845.576408451), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:30:49,191 - advanced_data_fetcher - INFO - Cached data for HBAR/USDT 1h
2025-07-28 12:30:49,191 - advanced_data_fetcher - INFO - Fetching 4h data for HBAR/USDT
2025-07-28 12:30:49,486 - advanced_data_fetcher - INFO - Cached data for HFT/USDT 4h
2025-07-28 12:30:49,486 - advanced_data_fetcher - INFO - Fetching 1d data for HFT/USDT
2025-07-28 12:30:50,031 - advanced_data_fetcher - WARNING - Low quality data for HFT/USDT 1d: {'date_range_days': 994, 'min_required_days': 1460, 'actual_samples': 995, 'expected_samples': 1460, 'completeness_ratio': 0.6815068493150684, 'gap_ratio': 0.0, 'avg_volume': np.float64(19551404.037889447), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:30:50,225 - advanced_data_fetcher - INFO - Cached data for HFT/USDT 1d
2025-07-28 12:30:50,225 - advanced_data_fetcher - INFO - Fetching 1h data for HIFI/USDT
2025-07-28 12:30:54,816 - advanced_data_fetcher - INFO - Cached data for HBAR/USDT 4h
2025-07-28 12:30:54,816 - advanced_data_fetcher - INFO - Fetching 1d data for HBAR/USDT
2025-07-28 12:30:56,081 - advanced_data_fetcher - INFO - Cached data for HBAR/USDT 1d
2025-07-28 12:30:56,081 - advanced_data_fetcher - INFO - Fetching 1h data for HIGH/USDT
2025-07-28 12:31:02,575 - advanced_data_fetcher - WARNING - Low quality data for HIFI/USDT 1h: {'date_range_days': 928, 'min_required_days': 1460, 'actual_samples': 22273, 'expected_samples': 35040, 'completeness_ratio': 0.6356449771689497, 'gap_ratio': 4.4895393732603036e-05, 'avg_volume': np.float64(478447.80404974637), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:31:05,486 - advanced_data_fetcher - INFO - Cached data for HIFI/USDT 1h
2025-07-28 12:31:05,486 - advanced_data_fetcher - INFO - Fetching 4h data for HIFI/USDT
2025-07-28 12:31:08,416 - advanced_data_fetcher - WARNING - Low quality data for HIFI/USDT 4h: {'date_range_days': 928, 'min_required_days': 1460, 'actual_samples': 5569, 'expected_samples': 8760, 'completeness_ratio': 0.6357305936073059, 'gap_ratio': 0.0, 'avg_volume': np.float64(1913534.9981325194), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:31:09,112 - advanced_data_fetcher - INFO - Cached data for HIFI/USDT 4h
2025-07-28 12:31:09,112 - advanced_data_fetcher - INFO - Fetching 1d data for HIFI/USDT
2025-07-28 12:31:09,600 - advanced_data_fetcher - WARNING - Low quality data for HIFI/USDT 1d: {'date_range_days': 928, 'min_required_days': 1460, 'actual_samples': 929, 'expected_samples': 1460, 'completeness_ratio': 0.6363013698630137, 'gap_ratio': 0.0, 'avg_volume': np.float64(11470913.810118407), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:31:09,803 - advanced_data_fetcher - INFO - Cached data for HIFI/USDT 1d
2025-07-28 12:31:09,803 - advanced_data_fetcher - INFO - Fetching 1h data for HIVE/USDT
2025-07-28 12:31:16,537 - advanced_data_fetcher - INFO - Cached data for HIGH/USDT 1h
2025-07-28 12:31:16,538 - advanced_data_fetcher - INFO - Fetching 4h data for HIGH/USDT
2025-07-28 12:31:21,397 - advanced_data_fetcher - INFO - Cached data for HIGH/USDT 4h
2025-07-28 12:31:21,397 - advanced_data_fetcher - INFO - Fetching 1d data for HIGH/USDT
2025-07-28 12:31:22,580 - advanced_data_fetcher - INFO - Cached data for HIGH/USDT 1d
2025-07-28 12:31:22,581 - advanced_data_fetcher - INFO - Fetching 1h data for HMSTR/USDT
2025-07-28 12:31:26,465 - advanced_data_fetcher - WARNING - Low quality data for HMSTR/USDT 1h: {'date_range_days': 304, 'min_required_days': 1460, 'actual_samples': 7318, 'expected_samples': 35040, 'completeness_ratio': 0.20884703196347032, 'gap_ratio': 0.0, 'avg_volume': np.float64(169285551.53908172), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:31:27,400 - advanced_data_fetcher - INFO - Cached data for HMSTR/USDT 1h
2025-07-28 12:31:27,400 - advanced_data_fetcher - INFO - Fetching 4h data for HMSTR/USDT
2025-07-28 12:31:28,538 - advanced_data_fetcher - WARNING - Low quality data for HMSTR/USDT 4h: {'date_range_days': 304, 'min_required_days': 1460, 'actual_samples': 1830, 'expected_samples': 8760, 'completeness_ratio': 0.2089041095890411, 'gap_ratio': 0.0, 'avg_volume': np.float64(676957194.6245902), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:31:29,717 - advanced_data_fetcher - INFO - Cached data for HMSTR/USDT 4h
2025-07-28 12:31:29,717 - advanced_data_fetcher - INFO - Fetching 1d data for HMSTR/USDT
2025-07-28 12:31:30,241 - advanced_data_fetcher - WARNING - Low quality data for HMSTR/USDT 1d: {'date_range_days': 305, 'min_required_days': 1460, 'actual_samples': 306, 'expected_samples': 1460, 'completeness_ratio': 0.2095890410958904, 'gap_ratio': 0.0, 'avg_volume': np.float64(4048469887.915033), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:31:30,447 - advanced_data_fetcher - INFO - Cached data for HMSTR/USDT 1d
2025-07-28 12:31:30,450 - advanced_data_fetcher - INFO - Fetching 1h data for HOME/USDT
2025-07-28 12:31:31,416 - advanced_data_fetcher - WARNING - Low quality data for HOME/USDT 1h: {'date_range_days': 45, 'min_required_days': 1460, 'actual_samples': 1099, 'expected_samples': 35040, 'completeness_ratio': 0.03136415525114155, 'gap_ratio': 0.0, 'avg_volume': np.float64(10764797.056414923), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:31:31,782 - advanced_data_fetcher - INFO - Cached data for HOME/USDT 1h
2025-07-28 12:31:31,782 - advanced_data_fetcher - INFO - Fetching 4h data for HOME/USDT
2025-07-28 12:31:32,350 - advanced_data_fetcher - WARNING - Low quality data for HOME/USDT 4h: {'date_range_days': 45, 'min_required_days': 1460, 'actual_samples': 276, 'expected_samples': 8760, 'completeness_ratio': 0.031506849315068496, 'gap_ratio': 0.0, 'avg_volume': np.float64(42864174.85869565), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:31:32,540 - advanced_data_fetcher - INFO - Cached data for HOME/USDT 4h
2025-07-28 12:31:32,545 - advanced_data_fetcher - INFO - Fetching 1d data for HOME/USDT
2025-07-28 12:31:33,100 - advanced_data_fetcher - WARNING - Low quality data for HOME/USDT 1d: {'date_range_days': 46, 'min_required_days': 1460, 'actual_samples': 47, 'expected_samples': 1460, 'completeness_ratio': 0.03219178082191781, 'gap_ratio': 0.0, 'avg_volume': np.float64(251713026.82978722), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:31:33,315 - advanced_data_fetcher - INFO - Cached data for HOME/USDT 1d
2025-07-28 12:31:33,315 - advanced_data_fetcher - INFO - Fetching 1h data for HOOK/USDT
2025-07-28 12:31:33,393 - advanced_data_fetcher - INFO - Cached data for HIVE/USDT 1h
2025-07-28 12:31:33,394 - advanced_data_fetcher - INFO - Fetching 4h data for HIVE/USDT
2025-07-28 12:31:38,968 - advanced_data_fetcher - INFO - Cached data for HIVE/USDT 4h
2025-07-28 12:31:38,968 - advanced_data_fetcher - INFO - Fetching 1d data for HIVE/USDT
2025-07-28 12:31:40,200 - advanced_data_fetcher - INFO - Cached data for HIVE/USDT 1d
2025-07-28 12:31:40,200 - advanced_data_fetcher - INFO - Fetching 1h data for HOT/USDT
2025-07-28 12:31:45,750 - advanced_data_fetcher - WARNING - Low quality data for HOOK/USDT 1h: {'date_range_days': 969, 'min_required_days': 1460, 'actual_samples': 23275, 'expected_samples': 35040, 'completeness_ratio': 0.6642408675799086, 'gap_ratio': 4.296270836913559e-05, 'avg_volume': np.float64(412049.88406015036), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:31:48,615 - advanced_data_fetcher - INFO - Cached data for HOOK/USDT 1h
2025-07-28 12:31:48,615 - advanced_data_fetcher - INFO - Fetching 4h data for HOOK/USDT
2025-07-28 12:31:51,534 - advanced_data_fetcher - WARNING - Low quality data for HOOK/USDT 4h: {'date_range_days': 969, 'min_required_days': 1460, 'actual_samples': 5820, 'expected_samples': 8760, 'completeness_ratio': 0.6643835616438356, 'gap_ratio': 0.0, 'avg_volume': np.float64(1647845.5414948454), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:31:52,265 - advanced_data_fetcher - INFO - Cached data for HOOK/USDT 4h
2025-07-28 12:31:52,265 - advanced_data_fetcher - INFO - Fetching 1d data for HOOK/USDT
2025-07-28 12:31:52,765 - advanced_data_fetcher - WARNING - Low quality data for HOOK/USDT 1d: {'date_range_days': 970, 'min_required_days': 1460, 'actual_samples': 971, 'expected_samples': 1460, 'completeness_ratio': 0.665068493150685, 'gap_ratio': 0.0, 'avg_volume': np.float64(9876891.06014418), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:31:52,950 - advanced_data_fetcher - INFO - Cached data for HOOK/USDT 1d
2025-07-28 12:31:52,950 - advanced_data_fetcher - INFO - Fetching 1h data for HUMA/USDT
2025-07-28 12:31:53,915 - advanced_data_fetcher - WARNING - Low quality data for HUMA/USDT 1h: {'date_range_days': 62, 'min_required_days': 1460, 'actual_samples': 1509, 'expected_samples': 35040, 'completeness_ratio': 0.043065068493150685, 'gap_ratio': 0.0, 'avg_volume': np.float64(20921529.853545394), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:31:54,150 - advanced_data_fetcher - INFO - Cached data for HUMA/USDT 1h
2025-07-28 12:31:54,150 - advanced_data_fetcher - INFO - Fetching 4h data for HUMA/USDT
2025-07-28 12:31:54,621 - advanced_data_fetcher - WARNING - Low quality data for HUMA/USDT 4h: {'date_range_days': 62, 'min_required_days': 1460, 'actual_samples': 378, 'expected_samples': 8760, 'completeness_ratio': 0.04315068493150685, 'gap_ratio': 0.0, 'avg_volume': np.float64(83520082.1005291), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:31:54,715 - advanced_data_fetcher - INFO - Cached data for HUMA/USDT 4h
2025-07-28 12:31:54,715 - advanced_data_fetcher - INFO - Fetching 1d data for HUMA/USDT
2025-07-28 12:31:55,150 - advanced_data_fetcher - WARNING - Low quality data for HUMA/USDT 1d: {'date_range_days': 63, 'min_required_days': 1460, 'actual_samples': 64, 'expected_samples': 1460, 'completeness_ratio': 0.043835616438356165, 'gap_ratio': 0.0, 'avg_volume': np.float64(493290484.90625), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:31:55,216 - advanced_data_fetcher - INFO - Cached data for HUMA/USDT 1d
2025-07-28 12:31:55,216 - advanced_data_fetcher - INFO - Fetching 1h data for HYPER/USDT
2025-07-28 12:31:56,616 - advanced_data_fetcher - WARNING - Low quality data for HYPER/USDT 1h: {'date_range_days': 96, 'min_required_days': 1460, 'actual_samples': 2325, 'expected_samples': 35040, 'completeness_ratio': 0.0663527397260274, 'gap_ratio': 0.0, 'avg_volume': np.float64(2255414.3821505373), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:31:56,967 - advanced_data_fetcher - INFO - Cached data for HYPER/USDT 1h
2025-07-28 12:31:56,967 - advanced_data_fetcher - INFO - Fetching 4h data for HYPER/USDT
2025-07-28 12:31:57,450 - advanced_data_fetcher - WARNING - Low quality data for HYPER/USDT 4h: {'date_range_days': 96, 'min_required_days': 1460, 'actual_samples': 582, 'expected_samples': 8760, 'completeness_ratio': 0.06643835616438357, 'gap_ratio': 0.0, 'avg_volume': np.float64(9010031.681271479), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:31:57,615 - advanced_data_fetcher - INFO - Cached data for HYPER/USDT 4h
2025-07-28 12:31:57,615 - advanced_data_fetcher - INFO - Fetching 1d data for HYPER/USDT
2025-07-28 12:31:58,045 - advanced_data_fetcher - WARNING - Low quality data for HYPER/USDT 1d: {'date_range_days': 97, 'min_required_days': 1460, 'actual_samples': 98, 'expected_samples': 1460, 'completeness_ratio': 0.06712328767123288, 'gap_ratio': 0.0, 'avg_volume': np.float64(53508573.08367347), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:31:58,100 - advanced_data_fetcher - INFO - Cached data for HYPER/USDT 1d
2025-07-28 12:31:58,100 - advanced_data_fetcher - INFO - Fetching 1h data for ICP/USDT
2025-07-28 12:32:03,272 - advanced_data_fetcher - INFO - Cached data for HOT/USDT 1h
2025-07-28 12:32:03,272 - advanced_data_fetcher - INFO - Fetching 4h data for HOT/USDT
2025-07-28 12:32:08,950 - advanced_data_fetcher - INFO - Cached data for HOT/USDT 4h
2025-07-28 12:32:08,950 - advanced_data_fetcher - INFO - Fetching 1d data for HOT/USDT
2025-07-28 12:32:10,147 - advanced_data_fetcher - INFO - Cached data for HOT/USDT 1d
2025-07-28 12:32:10,147 - advanced_data_fetcher - INFO - Fetching 1h data for ICX/USDT
2025-07-28 12:32:21,065 - advanced_data_fetcher - INFO - Cached data for ICP/USDT 1h
2025-07-28 12:32:21,065 - advanced_data_fetcher - INFO - Fetching 4h data for ICP/USDT
2025-07-28 12:32:26,542 - advanced_data_fetcher - INFO - Cached data for ICP/USDT 4h
2025-07-28 12:32:26,542 - advanced_data_fetcher - INFO - Fetching 1d data for ICP/USDT
2025-07-28 12:32:28,546 - advanced_data_fetcher - INFO - Cached data for ICP/USDT 1d
2025-07-28 12:32:28,547 - advanced_data_fetcher - INFO - Fetching 1h data for ID/USDT
2025-07-28 12:32:33,181 - advanced_data_fetcher - INFO - Cached data for ICX/USDT 1h
2025-07-28 12:32:33,181 - advanced_data_fetcher - INFO - Fetching 4h data for ICX/USDT
2025-07-28 12:32:38,915 - advanced_data_fetcher - INFO - Cached data for ICX/USDT 4h
2025-07-28 12:32:38,915 - advanced_data_fetcher - INFO - Fetching 1d data for ICX/USDT
2025-07-28 12:32:39,465 - advanced_data_fetcher - WARNING - Low quality data for ID/USDT 1h: {'date_range_days': 858, 'min_required_days': 1460, 'actual_samples': 20613, 'expected_samples': 35040, 'completeness_ratio': 0.5882705479452055, 'gap_ratio': 4.851072086931212e-05, 'avg_volume': np.float64(1232028.2511521855), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:32:40,781 - advanced_data_fetcher - INFO - Cached data for ICX/USDT 1d
2025-07-28 12:32:40,816 - advanced_data_fetcher - INFO - Fetching 1h data for IDEX/USDT
2025-07-28 12:32:42,364 - advanced_data_fetcher - INFO - Cached data for ID/USDT 1h
2025-07-28 12:32:42,365 - advanced_data_fetcher - INFO - Fetching 4h data for ID/USDT
2025-07-28 12:32:45,170 - advanced_data_fetcher - WARNING - Low quality data for ID/USDT 4h: {'date_range_days': 858, 'min_required_days': 1460, 'actual_samples': 5154, 'expected_samples': 8760, 'completeness_ratio': 0.5883561643835616, 'gap_ratio': 0.0, 'avg_volume': np.float64(4927395.87524253), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:32:45,804 - advanced_data_fetcher - INFO - Cached data for ID/USDT 4h
2025-07-28 12:32:45,804 - advanced_data_fetcher - INFO - Fetching 1d data for ID/USDT
2025-07-28 12:32:46,347 - advanced_data_fetcher - WARNING - Low quality data for ID/USDT 1d: {'date_range_days': 859, 'min_required_days': 1460, 'actual_samples': 860, 'expected_samples': 1460, 'completeness_ratio': 0.589041095890411, 'gap_ratio': 0.0, 'avg_volume': np.float64(29529998.07093023), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:32:46,478 - advanced_data_fetcher - INFO - Cached data for ID/USDT 1d
2025-07-28 12:32:46,478 - advanced_data_fetcher - INFO - Fetching 1h data for ILV/USDT
2025-07-28 12:33:03,100 - advanced_data_fetcher - INFO - Cached data for IDEX/USDT 1h
2025-07-28 12:33:03,100 - advanced_data_fetcher - INFO - Fetching 4h data for IDEX/USDT
2025-07-28 12:33:08,431 - advanced_data_fetcher - INFO - Cached data for ILV/USDT 1h
2025-07-28 12:33:08,447 - advanced_data_fetcher - INFO - Fetching 4h data for ILV/USDT
2025-07-28 12:33:09,465 - advanced_data_fetcher - INFO - Cached data for IDEX/USDT 4h
2025-07-28 12:33:09,465 - advanced_data_fetcher - INFO - Fetching 1d data for IDEX/USDT
2025-07-28 12:33:10,630 - advanced_data_fetcher - INFO - Cached data for IDEX/USDT 1d
2025-07-28 12:33:10,630 - advanced_data_fetcher - INFO - Fetching 1h data for IMX/USDT
2025-07-28 12:33:13,950 - advanced_data_fetcher - INFO - Cached data for ILV/USDT 4h
2025-07-28 12:33:13,950 - advanced_data_fetcher - INFO - Fetching 1d data for ILV/USDT
2025-07-28 12:33:15,127 - advanced_data_fetcher - INFO - Cached data for ILV/USDT 1d
2025-07-28 12:33:15,127 - advanced_data_fetcher - INFO - Fetching 1h data for INIT/USDT
2025-07-28 12:33:16,542 - advanced_data_fetcher - WARNING - Low quality data for INIT/USDT 1h: {'date_range_days': 94, 'min_required_days': 1460, 'actual_samples': 2279, 'expected_samples': 35040, 'completeness_ratio': 0.06503995433789954, 'gap_ratio': 0.0, 'avg_volume': np.float64(1612582.7510750329), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:33:16,823 - advanced_data_fetcher - INFO - Cached data for INIT/USDT 1h
2025-07-28 12:33:16,839 - advanced_data_fetcher - INFO - Fetching 4h data for INIT/USDT
2025-07-28 12:33:17,344 - advanced_data_fetcher - WARNING - Low quality data for INIT/USDT 4h: {'date_range_days': 95, 'min_required_days': 1460, 'actual_samples': 571, 'expected_samples': 8760, 'completeness_ratio': 0.06518264840182648, 'gap_ratio': 0.0, 'avg_volume': np.float64(6436210.314711033), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:33:17,450 - advanced_data_fetcher - INFO - Cached data for INIT/USDT 4h
2025-07-28 12:33:17,450 - advanced_data_fetcher - INFO - Fetching 1d data for INIT/USDT
2025-07-28 12:33:17,887 - advanced_data_fetcher - WARNING - Low quality data for INIT/USDT 1d: {'date_range_days': 95, 'min_required_days': 1460, 'actual_samples': 96, 'expected_samples': 1460, 'completeness_ratio': 0.06575342465753424, 'gap_ratio': 0.0, 'avg_volume': np.float64(38282042.60104167), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:33:17,965 - advanced_data_fetcher - INFO - Cached data for INIT/USDT 1d
2025-07-28 12:33:17,965 - advanced_data_fetcher - INFO - Fetching 1h data for INJ/USDT
2025-07-28 12:33:30,275 - advanced_data_fetcher - INFO - Cached data for IMX/USDT 1h
2025-07-28 12:33:30,275 - advanced_data_fetcher - INFO - Fetching 4h data for IMX/USDT
2025-07-28 12:33:35,200 - advanced_data_fetcher - INFO - Cached data for IMX/USDT 4h
2025-07-28 12:33:35,200 - advanced_data_fetcher - INFO - Fetching 1d data for IMX/USDT
2025-07-28 12:33:36,865 - advanced_data_fetcher - INFO - Cached data for IMX/USDT 1d
2025-07-28 12:33:36,865 - advanced_data_fetcher - INFO - Fetching 1h data for IO/USDT
2025-07-28 12:33:41,183 - advanced_data_fetcher - INFO - Cached data for INJ/USDT 1h
2025-07-28 12:33:41,183 - advanced_data_fetcher - INFO - Fetching 4h data for INJ/USDT
2025-07-28 12:33:42,165 - advanced_data_fetcher - WARNING - Low quality data for IO/USDT 1h: {'date_range_days': 411, 'min_required_days': 1460, 'actual_samples': 9886, 'expected_samples': 35040, 'completeness_ratio': 0.28213470319634704, 'gap_ratio': 0.0, 'avg_volume': np.float64(519210.2043010318), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:33:43,466 - advanced_data_fetcher - INFO - Cached data for IO/USDT 1h
2025-07-28 12:33:43,466 - advanced_data_fetcher - INFO - Fetching 4h data for IO/USDT
2025-07-28 12:33:44,950 - advanced_data_fetcher - WARNING - Low quality data for IO/USDT 4h: {'date_range_days': 411, 'min_required_days': 1460, 'actual_samples': 2472, 'expected_samples': 8760, 'completeness_ratio': 0.2821917808219178, 'gap_ratio': 0.0, 'avg_volume': np.float64(2076420.7442233006), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:33:45,283 - advanced_data_fetcher - INFO - Cached data for IO/USDT 4h
2025-07-28 12:33:45,283 - advanced_data_fetcher - INFO - Fetching 1d data for IO/USDT
2025-07-28 12:33:45,741 - advanced_data_fetcher - WARNING - Low quality data for IO/USDT 1d: {'date_range_days': 412, 'min_required_days': 1460, 'actual_samples': 413, 'expected_samples': 1460, 'completeness_ratio': 0.2828767123287671, 'gap_ratio': 0.0, 'avg_volume': np.float64(12428358.546537532), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:33:45,850 - advanced_data_fetcher - INFO - Cached data for IO/USDT 1d
2025-07-28 12:33:45,850 - advanced_data_fetcher - INFO - Fetching 1h data for IOST/USDT
2025-07-28 12:33:46,935 - advanced_data_fetcher - INFO - Cached data for INJ/USDT 4h
2025-07-28 12:33:46,935 - advanced_data_fetcher - INFO - Fetching 1d data for INJ/USDT
2025-07-28 12:33:48,116 - advanced_data_fetcher - INFO - Cached data for INJ/USDT 1d
2025-07-28 12:33:48,116 - advanced_data_fetcher - INFO - Fetching 1h data for IOTA/USDT
2025-07-28 12:34:11,025 - advanced_data_fetcher - INFO - Cached data for IOST/USDT 1h
2025-07-28 12:34:11,041 - advanced_data_fetcher - INFO - Fetching 4h data for IOST/USDT
2025-07-28 12:34:13,915 - advanced_data_fetcher - INFO - Cached data for IOTA/USDT 1h
2025-07-28 12:34:13,915 - advanced_data_fetcher - INFO - Fetching 4h data for IOTA/USDT
2025-07-28 12:34:17,031 - advanced_data_fetcher - INFO - Cached data for IOST/USDT 4h
2025-07-28 12:34:17,031 - advanced_data_fetcher - INFO - Fetching 1d data for IOST/USDT
2025-07-28 12:34:18,240 - advanced_data_fetcher - INFO - Cached data for IOST/USDT 1d
2025-07-28 12:34:18,240 - advanced_data_fetcher - INFO - Fetching 1h data for IOTX/USDT
2025-07-28 12:34:19,616 - advanced_data_fetcher - INFO - Cached data for IOTA/USDT 4h
2025-07-28 12:34:19,616 - advanced_data_fetcher - INFO - Fetching 1d data for IOTA/USDT
2025-07-28 12:34:20,781 - advanced_data_fetcher - INFO - Cached data for IOTA/USDT 1d
2025-07-28 12:34:20,781 - advanced_data_fetcher - INFO - Fetching 1h data for IQ/USDT
2025-07-28 12:34:28,969 - advanced_data_fetcher - WARNING - Low quality data for IQ/USDT 1h: {'date_range_days': 675, 'min_required_days': 1460, 'actual_samples': 16202, 'expected_samples': 35040, 'completeness_ratio': 0.46238584474885847, 'gap_ratio': 0.0, 'avg_volume': np.float64(17427684.97037403), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:34:31,075 - advanced_data_fetcher - INFO - Cached data for IQ/USDT 1h
2025-07-28 12:34:31,076 - advanced_data_fetcher - INFO - Fetching 4h data for IQ/USDT
2025-07-28 12:34:33,500 - advanced_data_fetcher - WARNING - Low quality data for IQ/USDT 4h: {'date_range_days': 675, 'min_required_days': 1460, 'actual_samples': 4051, 'expected_samples': 8760, 'completeness_ratio': 0.46244292237442924, 'gap_ratio': 0.0, 'avg_volume': np.float64(69702135.74179216), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:34:34,060 - advanced_data_fetcher - INFO - Cached data for IQ/USDT 4h
2025-07-28 12:34:34,060 - advanced_data_fetcher - INFO - Fetching 1d data for IQ/USDT
2025-07-28 12:34:34,552 - advanced_data_fetcher - WARNING - Low quality data for IQ/USDT 1d: {'date_range_days': 675, 'min_required_days': 1460, 'actual_samples': 676, 'expected_samples': 1460, 'completeness_ratio': 0.46301369863013697, 'gap_ratio': 0.0, 'avg_volume': np.float64(417697266.10946745), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:34:34,675 - advanced_data_fetcher - INFO - Cached data for IQ/USDT 1d
2025-07-28 12:34:34,675 - advanced_data_fetcher - INFO - Fetching 1h data for JASMY/USDT
2025-07-28 12:34:41,615 - advanced_data_fetcher - INFO - Cached data for IOTX/USDT 1h
2025-07-28 12:34:41,615 - advanced_data_fetcher - INFO - Fetching 4h data for IOTX/USDT
2025-07-28 12:34:47,181 - advanced_data_fetcher - INFO - Cached data for IOTX/USDT 4h
2025-07-28 12:34:47,181 - advanced_data_fetcher - INFO - Fetching 1d data for IOTX/USDT
2025-07-28 12:34:48,415 - advanced_data_fetcher - INFO - Cached data for IOTX/USDT 1d
2025-07-28 12:34:48,415 - advanced_data_fetcher - INFO - Fetching 1h data for JOE/USDT
2025-07-28 12:34:56,900 - advanced_data_fetcher - INFO - Cached data for JASMY/USDT 1h
2025-07-28 12:34:56,900 - advanced_data_fetcher - INFO - Fetching 4h data for JASMY/USDT
2025-07-28 12:35:03,081 - advanced_data_fetcher - INFO - Cached data for JASMY/USDT 4h
2025-07-28 12:35:03,081 - advanced_data_fetcher - INFO - Fetching 1d data for JASMY/USDT
2025-07-28 12:35:04,250 - advanced_data_fetcher - INFO - Cached data for JASMY/USDT 1d
2025-07-28 12:35:04,250 - advanced_data_fetcher - INFO - Fetching 1h data for JST/USDT
2025-07-28 12:35:09,815 - advanced_data_fetcher - INFO - Cached data for JOE/USDT 1h
2025-07-28 12:35:09,815 - advanced_data_fetcher - INFO - Fetching 4h data for JOE/USDT
2025-07-28 12:35:14,686 - advanced_data_fetcher - INFO - Cached data for JOE/USDT 4h
2025-07-28 12:35:14,686 - advanced_data_fetcher - INFO - Fetching 1d data for JOE/USDT
2025-07-28 12:35:15,835 - advanced_data_fetcher - INFO - Cached data for JOE/USDT 1d
2025-07-28 12:35:15,835 - advanced_data_fetcher - INFO - Fetching 1h data for JTO/USDT
2025-07-28 12:35:23,916 - advanced_data_fetcher - WARNING - Low quality data for JTO/USDT 1h: {'date_range_days': 598, 'min_required_days': 1460, 'actual_samples': 14370, 'expected_samples': 35040, 'completeness_ratio': 0.4101027397260274, 'gap_ratio': 0.0, 'avg_volume': np.float64(395536.8289074461), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:35:28,918 - advanced_data_fetcher - INFO - Cached data for JTO/USDT 1h
2025-07-28 12:35:28,918 - advanced_data_fetcher - INFO - Fetching 4h data for JTO/USDT
2025-07-28 12:35:29,675 - advanced_data_fetcher - INFO - Cached data for JST/USDT 1h
2025-07-28 12:35:29,675 - advanced_data_fetcher - INFO - Fetching 4h data for JST/USDT
2025-07-28 12:35:31,010 - advanced_data_fetcher - WARNING - Low quality data for JTO/USDT 4h: {'date_range_days': 598, 'min_required_days': 1460, 'actual_samples': 3593, 'expected_samples': 8760, 'completeness_ratio': 0.4101598173515982, 'gap_ratio': 0.0, 'avg_volume': np.float64(1581927.146813248), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:35:31,500 - advanced_data_fetcher - INFO - Cached data for JTO/USDT 4h
2025-07-28 12:35:31,514 - advanced_data_fetcher - INFO - Fetching 1d data for JTO/USDT
2025-07-28 12:35:31,989 - advanced_data_fetcher - WARNING - Low quality data for JTO/USDT 1d: {'date_range_days': 599, 'min_required_days': 1460, 'actual_samples': 600, 'expected_samples': 1460, 'completeness_ratio': 0.410958904109589, 'gap_ratio': 0.0, 'avg_volume': np.float64(9473107.064166667), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:35:32,119 - advanced_data_fetcher - INFO - Cached data for JTO/USDT 1d
2025-07-28 12:35:32,119 - advanced_data_fetcher - INFO - Fetching 1h data for JUV/USDT
2025-07-28 12:35:35,448 - advanced_data_fetcher - INFO - Cached data for JST/USDT 4h
2025-07-28 12:35:35,449 - advanced_data_fetcher - INFO - Fetching 1d data for JST/USDT
2025-07-28 12:35:36,765 - advanced_data_fetcher - INFO - Cached data for JST/USDT 1d
2025-07-28 12:35:36,765 - advanced_data_fetcher - INFO - Fetching 1h data for KAIA/USDT
2025-07-28 12:35:40,131 - advanced_data_fetcher - WARNING - Low quality data for KAIA/USDT 1h: {'date_range_days': 270, 'min_required_days': 1460, 'actual_samples': 6482, 'expected_samples': 35040, 'completeness_ratio': 0.18498858447488584, 'gap_ratio': 0.0, 'avg_volume': np.float64(2353597.7797593335), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:35:40,956 - advanced_data_fetcher - INFO - Cached data for KAIA/USDT 1h
2025-07-28 12:35:40,958 - advanced_data_fetcher - INFO - Fetching 4h data for KAIA/USDT
2025-07-28 12:35:41,925 - advanced_data_fetcher - WARNING - Low quality data for KAIA/USDT 4h: {'date_range_days': 270, 'min_required_days': 1460, 'actual_samples': 1621, 'expected_samples': 8760, 'completeness_ratio': 0.18504566210045661, 'gap_ratio': 0.0, 'avg_volume': np.float64(9411487.23528686), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:35:42,200 - advanced_data_fetcher - INFO - Cached data for KAIA/USDT 4h
2025-07-28 12:35:42,200 - advanced_data_fetcher - INFO - Fetching 1d data for KAIA/USDT
2025-07-28 12:35:42,650 - advanced_data_fetcher - WARNING - Low quality data for KAIA/USDT 1d: {'date_range_days': 270, 'min_required_days': 1460, 'actual_samples': 271, 'expected_samples': 1460, 'completeness_ratio': 0.18561643835616437, 'gap_ratio': 0.0, 'avg_volume': np.float64(56295279.73579336), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:35:42,765 - advanced_data_fetcher - INFO - Cached data for KAIA/USDT 1d
2025-07-28 12:35:42,765 - advanced_data_fetcher - INFO - Fetching 1h data for KAITO/USDT
2025-07-28 12:35:44,750 - advanced_data_fetcher - WARNING - Low quality data for KAITO/USDT 1h: {'date_range_days': 157, 'min_required_days': 1460, 'actual_samples': 3789, 'expected_samples': 35040, 'completeness_ratio': 0.10813356164383561, 'gap_ratio': 0.0, 'avg_volume': np.float64(942903.8349168646), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:35:45,265 - advanced_data_fetcher - INFO - Cached data for KAITO/USDT 1h
2025-07-28 12:35:45,265 - advanced_data_fetcher - INFO - Fetching 4h data for KAITO/USDT
2025-07-28 12:35:45,775 - advanced_data_fetcher - WARNING - Low quality data for KAITO/USDT 4h: {'date_range_days': 157, 'min_required_days': 1460, 'actual_samples': 948, 'expected_samples': 8760, 'completeness_ratio': 0.10821917808219178, 'gap_ratio': 0.0, 'avg_volume': np.float64(3768631.466772152), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:35:45,965 - advanced_data_fetcher - INFO - Cached data for KAITO/USDT 4h
2025-07-28 12:35:45,965 - advanced_data_fetcher - INFO - Fetching 1d data for KAITO/USDT
2025-07-28 12:35:46,401 - advanced_data_fetcher - WARNING - Low quality data for KAITO/USDT 1d: {'date_range_days': 158, 'min_required_days': 1460, 'actual_samples': 159, 'expected_samples': 1460, 'completeness_ratio': 0.10890410958904109, 'gap_ratio': 0.0, 'avg_volume': np.float64(22469576.29245283), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:35:46,533 - advanced_data_fetcher - INFO - Cached data for KAITO/USDT 1d
2025-07-28 12:35:46,533 - advanced_data_fetcher - INFO - Fetching 1h data for KAVA/USDT
2025-07-28 12:35:55,685 - advanced_data_fetcher - INFO - Cached data for JUV/USDT 1h
2025-07-28 12:35:55,687 - advanced_data_fetcher - INFO - Fetching 4h data for JUV/USDT
2025-07-28 12:36:01,265 - advanced_data_fetcher - INFO - Cached data for JUV/USDT 4h
2025-07-28 12:36:01,265 - advanced_data_fetcher - INFO - Fetching 1d data for JUV/USDT
2025-07-28 12:36:02,431 - advanced_data_fetcher - INFO - Cached data for JUV/USDT 1d
2025-07-28 12:36:02,431 - advanced_data_fetcher - INFO - Fetching 1h data for KDA/USDT
2025-07-28 12:36:09,766 - advanced_data_fetcher - INFO - Cached data for KAVA/USDT 1h
2025-07-28 12:36:09,766 - advanced_data_fetcher - INFO - Fetching 4h data for KAVA/USDT
2025-07-28 12:36:15,331 - advanced_data_fetcher - INFO - Cached data for KAVA/USDT 4h
2025-07-28 12:36:15,331 - advanced_data_fetcher - INFO - Fetching 1d data for KAVA/USDT
2025-07-28 12:36:16,525 - advanced_data_fetcher - INFO - Cached data for KAVA/USDT 1d
2025-07-28 12:36:16,526 - advanced_data_fetcher - INFO - Fetching 1h data for KERNEL/USDT
2025-07-28 12:36:18,100 - advanced_data_fetcher - WARNING - Low quality data for KERNEL/USDT 1h: {'date_range_days': 104, 'min_required_days': 1460, 'actual_samples': 2518, 'expected_samples': 35040, 'completeness_ratio': 0.0718607305936073, 'gap_ratio': 0.0, 'avg_volume': np.float64(2189530.879666402), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:36:18,466 - advanced_data_fetcher - INFO - Cached data for KERNEL/USDT 1h
2025-07-28 12:36:18,466 - advanced_data_fetcher - INFO - Fetching 4h data for KERNEL/USDT
2025-07-28 12:36:19,050 - advanced_data_fetcher - WARNING - Low quality data for KERNEL/USDT 4h: {'date_range_days': 104, 'min_required_days': 1460, 'actual_samples': 630, 'expected_samples': 8760, 'completeness_ratio': 0.07191780821917808, 'gap_ratio': 0.0, 'avg_volume': np.float64(8751172.626984127), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:36:19,616 - advanced_data_fetcher - INFO - Cached data for KERNEL/USDT 4h
2025-07-28 12:36:19,616 - advanced_data_fetcher - INFO - Fetching 1d data for KERNEL/USDT
2025-07-28 12:36:20,143 - advanced_data_fetcher - WARNING - Low quality data for KERNEL/USDT 1d: {'date_range_days': 105, 'min_required_days': 1460, 'actual_samples': 106, 'expected_samples': 1460, 'completeness_ratio': 0.07260273972602739, 'gap_ratio': 0.0, 'avg_volume': np.float64(52011692.625471696), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:36:20,309 - advanced_data_fetcher - INFO - Cached data for KERNEL/USDT 1d
2025-07-28 12:36:20,310 - advanced_data_fetcher - INFO - Fetching 1h data for KMNO/USDT
2025-07-28 12:36:21,595 - advanced_data_fetcher - WARNING - Low quality data for KMNO/USDT 1h: {'date_range_days': 82, 'min_required_days': 1460, 'actual_samples': 1987, 'expected_samples': 35040, 'completeness_ratio': 0.05670662100456621, 'gap_ratio': 0.0, 'avg_volume': np.float64(2061615.5359838954), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:36:22,615 - advanced_data_fetcher - INFO - Cached data for KMNO/USDT 1h
2025-07-28 12:36:22,650 - advanced_data_fetcher - INFO - Fetching 4h data for KMNO/USDT
2025-07-28 12:36:23,171 - advanced_data_fetcher - WARNING - Low quality data for KMNO/USDT 4h: {'date_range_days': 82, 'min_required_days': 1460, 'actual_samples': 498, 'expected_samples': 8760, 'completeness_ratio': 0.05684931506849315, 'gap_ratio': 0.0, 'avg_volume': np.float64(8225763.192771085), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:36:23,589 - advanced_data_fetcher - INFO - Cached data for KMNO/USDT 4h
2025-07-28 12:36:23,600 - advanced_data_fetcher - INFO - Fetching 1d data for KMNO/USDT
2025-07-28 12:36:23,625 - advanced_data_fetcher - INFO - Cached data for KDA/USDT 1h
2025-07-28 12:36:23,625 - advanced_data_fetcher - INFO - Fetching 4h data for KDA/USDT
2025-07-28 12:36:24,072 - advanced_data_fetcher - WARNING - Low quality data for KMNO/USDT 1d: {'date_range_days': 83, 'min_required_days': 1460, 'actual_samples': 84, 'expected_samples': 1460, 'completeness_ratio': 0.057534246575342465, 'gap_ratio': 0.0, 'avg_volume': np.float64(48767024.64285714), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:36:24,167 - advanced_data_fetcher - INFO - Cached data for KMNO/USDT 1d
2025-07-28 12:36:24,167 - advanced_data_fetcher - INFO - Fetching 1h data for KNC/USDT
2025-07-28 12:36:28,447 - advanced_data_fetcher - INFO - Cached data for KDA/USDT 4h
2025-07-28 12:36:28,447 - advanced_data_fetcher - INFO - Fetching 1d data for KDA/USDT
2025-07-28 12:36:29,566 - advanced_data_fetcher - INFO - Cached data for KDA/USDT 1d
2025-07-28 12:36:29,566 - advanced_data_fetcher - INFO - Fetching 1h data for KSM/USDT
2025-07-28 12:36:46,379 - advanced_data_fetcher - INFO - Cached data for KNC/USDT 1h
2025-07-28 12:36:46,379 - advanced_data_fetcher - INFO - Fetching 4h data for KNC/USDT
2025-07-28 12:36:53,815 - advanced_data_fetcher - INFO - Cached data for KSM/USDT 1h
2025-07-28 12:36:53,815 - advanced_data_fetcher - INFO - Fetching 4h data for KSM/USDT
2025-07-28 12:36:54,125 - advanced_data_fetcher - INFO - Cached data for KNC/USDT 4h
2025-07-28 12:36:54,125 - advanced_data_fetcher - INFO - Fetching 1d data for KNC/USDT
2025-07-28 12:36:55,340 - advanced_data_fetcher - INFO - Cached data for KNC/USDT 1d
2025-07-28 12:36:55,340 - advanced_data_fetcher - INFO - Fetching 1h data for LA/USDT
2025-07-28 12:36:55,815 - advanced_data_fetcher - WARNING - Low quality data for LA/USDT 1h: {'date_range_days': 18, 'min_required_days': 1460, 'actual_samples': 450, 'expected_samples': 35040, 'completeness_ratio': 0.012842465753424657, 'gap_ratio': 0.0, 'avg_volume': np.float64(3531682.493333333), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:36:55,950 - advanced_data_fetcher - INFO - Cached data for LA/USDT 1h
2025-07-28 12:36:55,950 - advanced_data_fetcher - INFO - Fetching 4h data for LA/USDT
2025-07-28 12:36:56,400 - advanced_data_fetcher - WARNING - Low quality data for LA/USDT 4h: {'date_range_days': 18, 'min_required_days': 1460, 'actual_samples': 113, 'expected_samples': 8760, 'completeness_ratio': 0.012899543378995433, 'gap_ratio': 0.0, 'avg_volume': np.float64(14064222.31858407), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:36:56,500 - advanced_data_fetcher - INFO - Cached data for LA/USDT 4h
2025-07-28 12:36:56,500 - advanced_data_fetcher - INFO - Fetching 1d data for LA/USDT
2025-07-28 12:36:56,936 - advanced_data_fetcher - WARNING - Low quality data for LA/USDT 1d: {'date_range_days': 19, 'min_required_days': 1460, 'actual_samples': 20, 'expected_samples': 1460, 'completeness_ratio': 0.0136986301369863, 'gap_ratio': 0.0, 'avg_volume': np.float64(79462856.1), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:36:57,015 - advanced_data_fetcher - INFO - Cached data for LA/USDT 1d
2025-07-28 12:36:57,015 - advanced_data_fetcher - INFO - Fetching 1h data for LAYER/USDT
2025-07-28 12:36:59,631 - advanced_data_fetcher - WARNING - Low quality data for LAYER/USDT 1h: {'date_range_days': 166, 'min_required_days': 1460, 'actual_samples': 4004, 'expected_samples': 35040, 'completeness_ratio': 0.11426940639269406, 'gap_ratio': 0.0, 'avg_volume': np.float64(901259.3346428571), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:36:59,781 - advanced_data_fetcher - INFO - Cached data for KSM/USDT 4h
2025-07-28 12:36:59,781 - advanced_data_fetcher - INFO - Fetching 1d data for KSM/USDT
2025-07-28 12:37:00,281 - advanced_data_fetcher - INFO - Cached data for LAYER/USDT 1h
2025-07-28 12:37:00,281 - advanced_data_fetcher - INFO - Fetching 4h data for LAYER/USDT
2025-07-28 12:37:01,025 - advanced_data_fetcher - INFO - Cached data for KSM/USDT 1d
2025-07-28 12:37:01,025 - advanced_data_fetcher - INFO - Fetching 1h data for LAZIO/USDT
2025-07-28 12:37:01,210 - advanced_data_fetcher - WARNING - Low quality data for LAYER/USDT 4h: {'date_range_days': 166, 'min_required_days': 1460, 'actual_samples': 1002, 'expected_samples': 8760, 'completeness_ratio': 0.11438356164383562, 'gap_ratio': 0.0, 'avg_volume': np.float64(3601439.4969161674), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:37:01,425 - advanced_data_fetcher - INFO - Cached data for LAYER/USDT 4h
2025-07-28 12:37:01,425 - advanced_data_fetcher - INFO - Fetching 1d data for LAYER/USDT
2025-07-28 12:37:01,880 - advanced_data_fetcher - WARNING - Low quality data for LAYER/USDT 1d: {'date_range_days': 167, 'min_required_days': 1460, 'actual_samples': 168, 'expected_samples': 1460, 'completeness_ratio': 0.11506849315068493, 'gap_ratio': 0.0, 'avg_volume': np.float64(21480014.142321426), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:37:01,965 - advanced_data_fetcher - INFO - Cached data for LAYER/USDT 1d
2025-07-28 12:37:01,965 - advanced_data_fetcher - INFO - Fetching 1h data for LDO/USDT
2025-07-28 12:37:20,791 - advanced_data_fetcher - INFO - Cached data for LDO/USDT 1h
2025-07-28 12:37:20,791 - advanced_data_fetcher - INFO - Fetching 4h data for LDO/USDT
2025-07-28 12:37:24,100 - advanced_data_fetcher - INFO - Cached data for LAZIO/USDT 1h
2025-07-28 12:37:24,100 - advanced_data_fetcher - INFO - Fetching 4h data for LAZIO/USDT
2025-07-28 12:37:25,991 - advanced_data_fetcher - INFO - Cached data for LDO/USDT 4h
2025-07-28 12:37:25,991 - advanced_data_fetcher - INFO - Fetching 1d data for LDO/USDT
2025-07-28 12:37:27,100 - advanced_data_fetcher - INFO - Cached data for LDO/USDT 1d
2025-07-28 12:37:27,100 - advanced_data_fetcher - INFO - Fetching 1h data for LISTA/USDT
2025-07-28 12:37:29,800 - advanced_data_fetcher - INFO - Cached data for LAZIO/USDT 4h
2025-07-28 12:37:29,800 - advanced_data_fetcher - INFO - Fetching 1d data for LAZIO/USDT
2025-07-28 12:37:31,025 - advanced_data_fetcher - INFO - Cached data for LAZIO/USDT 1d
2025-07-28 12:37:31,025 - advanced_data_fetcher - INFO - Fetching 1h data for LOKA/USDT
2025-07-28 12:37:32,624 - advanced_data_fetcher - WARNING - Low quality data for LISTA/USDT 1h: {'date_range_days': 402, 'min_required_days': 1460, 'actual_samples': 9672, 'expected_samples': 35040, 'completeness_ratio': 0.276027397260274, 'gap_ratio': 0.0, 'avg_volume': np.float64(1067897.1195409428), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:37:33,847 - advanced_data_fetcher - INFO - Cached data for LISTA/USDT 1h
2025-07-28 12:37:33,847 - advanced_data_fetcher - INFO - Fetching 4h data for LISTA/USDT
2025-07-28 12:37:35,340 - advanced_data_fetcher - WARNING - Low quality data for LISTA/USDT 4h: {'date_range_days': 403, 'min_required_days': 1460, 'actual_samples': 2419, 'expected_samples': 8760, 'completeness_ratio': 0.27614155251141553, 'gap_ratio': 0.0, 'avg_volume': np.float64(4269822.755436131), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:37:35,715 - advanced_data_fetcher - INFO - Cached data for LISTA/USDT 4h
2025-07-28 12:37:35,715 - advanced_data_fetcher - INFO - Fetching 1d data for LISTA/USDT
2025-07-28 12:37:36,250 - advanced_data_fetcher - WARNING - Low quality data for LISTA/USDT 1d: {'date_range_days': 403, 'min_required_days': 1460, 'actual_samples': 404, 'expected_samples': 1460, 'completeness_ratio': 0.27671232876712326, 'gap_ratio': 0.0, 'avg_volume': np.float64(25566092.191584162), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:37:36,342 - advanced_data_fetcher - INFO - Cached data for LISTA/USDT 1d
2025-07-28 12:37:36,343 - advanced_data_fetcher - INFO - Fetching 1h data for LPT/USDT
2025-07-28 12:37:58,600 - advanced_data_fetcher - INFO - Cached data for LPT/USDT 1h
2025-07-28 12:37:58,600 - advanced_data_fetcher - INFO - Fetching 4h data for LPT/USDT
2025-07-28 12:38:04,166 - advanced_data_fetcher - INFO - Cached data for LPT/USDT 4h
2025-07-28 12:38:04,166 - advanced_data_fetcher - INFO - Fetching 1d data for LPT/USDT
2025-07-28 12:38:05,425 - advanced_data_fetcher - INFO - Cached data for LPT/USDT 1d
2025-07-28 12:38:05,425 - advanced_data_fetcher - INFO - Fetching 1h data for LQTY/USDT
2025-07-28 12:38:16,050 - advanced_data_fetcher - WARNING - Low quality data for LQTY/USDT 1h: {'date_range_days': 880, 'min_required_days': 1460, 'actual_samples': 21142, 'expected_samples': 35040, 'completeness_ratio': 0.6033675799086758, 'gap_ratio': 4.7296977723123494e-05, 'avg_volume': np.float64(219260.08334121652), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:38:18,553 - advanced_data_fetcher - INFO - Cached data for LQTY/USDT 1h
2025-07-28 12:38:18,553 - advanced_data_fetcher - INFO - Fetching 4h data for LQTY/USDT
2025-07-28 12:38:21,450 - advanced_data_fetcher - WARNING - Low quality data for LQTY/USDT 4h: {'date_range_days': 881, 'min_required_days': 1460, 'actual_samples': 5287, 'expected_samples': 8760, 'completeness_ratio': 0.6035388127853881, 'gap_ratio': 0.0, 'avg_volume': np.float64(876791.5040665783), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:38:22,080 - advanced_data_fetcher - INFO - Cached data for LQTY/USDT 4h
2025-07-28 12:38:22,080 - advanced_data_fetcher - INFO - Fetching 1d data for LQTY/USDT
2025-07-28 12:38:22,579 - advanced_data_fetcher - WARNING - Low quality data for LQTY/USDT 1d: {'date_range_days': 881, 'min_required_days': 1460, 'actual_samples': 882, 'expected_samples': 1460, 'completeness_ratio': 0.6041095890410959, 'gap_ratio': 0.0, 'avg_volume': np.float64(5255778.551020408), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:38:22,700 - advanced_data_fetcher - INFO - Cached data for LQTY/USDT 1d
2025-07-28 12:38:22,700 - advanced_data_fetcher - INFO - Fetching 1h data for LRC/USDT
2025-07-28 12:38:44,730 - advanced_data_fetcher - INFO - Cached data for LRC/USDT 1h
2025-07-28 12:38:44,730 - advanced_data_fetcher - INFO - Fetching 4h data for LRC/USDT
2025-07-28 12:38:50,231 - advanced_data_fetcher - INFO - Cached data for LRC/USDT 4h
2025-07-28 12:38:50,231 - advanced_data_fetcher - INFO - Fetching 1d data for LRC/USDT
2025-07-28 12:38:51,381 - advanced_data_fetcher - INFO - Cached data for LRC/USDT 1d
2025-07-28 12:38:51,381 - advanced_data_fetcher - INFO - Fetching 1h data for LSK/USDT
2025-07-28 12:39:13,150 - advanced_data_fetcher - INFO - Cached data for LSK/USDT 1h
2025-07-28 12:39:13,150 - advanced_data_fetcher - INFO - Fetching 4h data for LSK/USDT
2025-07-28 12:39:18,615 - advanced_data_fetcher - INFO - Cached data for LSK/USDT 4h
2025-07-28 12:39:18,615 - advanced_data_fetcher - INFO - Fetching 1d data for LSK/USDT
2025-07-28 12:39:19,790 - advanced_data_fetcher - INFO - Cached data for LSK/USDT 1d
2025-07-28 12:39:19,800 - advanced_data_fetcher - INFO - Fetching 1h data for LUMIA/USDT
2025-07-28 12:39:24,965 - advanced_data_fetcher - WARNING - Low quality data for LUMIA/USDT 1h: {'date_range_days': 283, 'min_required_days': 1460, 'actual_samples': 6793, 'expected_samples': 35040, 'completeness_ratio': 0.19386415525114156, 'gap_ratio': 0.0, 'avg_volume': np.float64(389868.12383482995), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:39:25,765 - advanced_data_fetcher - INFO - Cached data for LUMIA/USDT 1h
2025-07-28 12:39:25,766 - advanced_data_fetcher - INFO - Fetching 4h data for LUMIA/USDT
2025-07-28 12:39:26,725 - advanced_data_fetcher - WARNING - Low quality data for LUMIA/USDT 4h: {'date_range_days': 283, 'min_required_days': 1460, 'actual_samples': 1699, 'expected_samples': 8760, 'completeness_ratio': 0.19394977168949773, 'gap_ratio': 0.0, 'avg_volume': np.float64(1558784.0878222485), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:39:26,965 - advanced_data_fetcher - INFO - Cached data for LUMIA/USDT 4h
2025-07-28 12:39:26,965 - advanced_data_fetcher - INFO - Fetching 1d data for LUMIA/USDT
2025-07-28 12:39:27,435 - advanced_data_fetcher - WARNING - Low quality data for LUMIA/USDT 1d: {'date_range_days': 283, 'min_required_days': 1460, 'actual_samples': 284, 'expected_samples': 1460, 'completeness_ratio': 0.19452054794520549, 'gap_ratio': 0.0, 'avg_volume': np.float64(9325261.145105634), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:39:27,515 - advanced_data_fetcher - INFO - Cached data for LUMIA/USDT 1d
2025-07-28 12:39:27,515 - advanced_data_fetcher - INFO - Fetching 1h data for LUNA/USDT
2025-07-28 12:39:49,565 - advanced_data_fetcher - INFO - Cached data for LUNA/USDT 1h
2025-07-28 12:39:49,565 - advanced_data_fetcher - INFO - Fetching 4h data for LUNA/USDT
2025-07-28 12:39:55,181 - advanced_data_fetcher - INFO - Cached data for LUNA/USDT 4h
2025-07-28 12:39:55,181 - advanced_data_fetcher - INFO - Fetching 1d data for LUNA/USDT
2025-07-28 12:39:56,365 - advanced_data_fetcher - INFO - Cached data for LUNA/USDT 1d
2025-07-28 12:39:56,365 - advanced_data_fetcher - INFO - Fetching 1h data for LUNC/USDT
2025-07-28 12:40:09,331 - advanced_data_fetcher - WARNING - Low quality data for LUNC/USDT 1h: {'date_range_days': 1053, 'min_required_days': 1460, 'actual_samples': 25273, 'expected_samples': 35040, 'completeness_ratio': 0.7212614155251141, 'gap_ratio': 3.9566352773601326e-05, 'avg_volume': np.float64(6233068876.760421), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:40:12,587 - advanced_data_fetcher - INFO - Cached data for LUNC/USDT 1h
2025-07-28 12:40:12,587 - advanced_data_fetcher - INFO - Fetching 4h data for LUNC/USDT
2025-07-28 12:40:16,200 - advanced_data_fetcher - WARNING - Low quality data for LUNC/USDT 4h: {'date_range_days': 1053, 'min_required_days': 1460, 'actual_samples': 6319, 'expected_samples': 8760, 'completeness_ratio': 0.7213470319634703, 'gap_ratio': 0.0, 'avg_volume': np.float64(24929316430.414494), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:40:17,081 - advanced_data_fetcher - INFO - Cached data for LUNC/USDT 4h
2025-07-28 12:40:17,081 - advanced_data_fetcher - INFO - Fetching 1d data for LUNC/USDT
2025-07-28 12:40:18,016 - advanced_data_fetcher - WARNING - Low quality data for LUNC/USDT 1d: {'date_range_days': 1053, 'min_required_days': 1460, 'actual_samples': 1054, 'expected_samples': 1460, 'completeness_ratio': 0.7219178082191781, 'gap_ratio': 0.0, 'avg_volume': np.float64(149457638068.1112), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:40:18,181 - advanced_data_fetcher - INFO - Cached data for LUNC/USDT 1d
2025-07-28 12:40:18,181 - advanced_data_fetcher - INFO - Fetching 1h data for MAGIC/USDT
2025-07-28 12:40:30,131 - advanced_data_fetcher - WARNING - Low quality data for MAGIC/USDT 1h: {'date_range_days': 959, 'min_required_days': 1460, 'actual_samples': 23017, 'expected_samples': 35040, 'completeness_ratio': 0.6568778538812785, 'gap_ratio': 4.344426101312017e-05, 'avg_volume': np.float64(751119.4428726593), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:40:33,015 - advanced_data_fetcher - INFO - Cached data for MAGIC/USDT 1h
2025-07-28 12:40:33,025 - advanced_data_fetcher - INFO - Fetching 4h data for MAGIC/USDT
2025-07-28 12:40:36,382 - advanced_data_fetcher - WARNING - Low quality data for MAGIC/USDT 4h: {'date_range_days': 959, 'min_required_days': 1460, 'actual_samples': 5755, 'expected_samples': 8760, 'completeness_ratio': 0.6569634703196348, 'gap_ratio': 0.0, 'avg_volume': np.float64(3004086.778922676), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:40:37,115 - advanced_data_fetcher - INFO - Cached data for MAGIC/USDT 4h
2025-07-28 12:40:37,115 - advanced_data_fetcher - INFO - Fetching 1d data for MAGIC/USDT
2025-07-28 12:40:37,615 - advanced_data_fetcher - WARNING - Low quality data for MAGIC/USDT 1d: {'date_range_days': 959, 'min_required_days': 1460, 'actual_samples': 960, 'expected_samples': 1460, 'completeness_ratio': 0.6575342465753424, 'gap_ratio': 0.0, 'avg_volume': np.float64(18008874.42947917), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:40:37,766 - advanced_data_fetcher - INFO - Cached data for MAGIC/USDT 1d
2025-07-28 12:40:37,766 - advanced_data_fetcher - INFO - Fetching 1h data for MANA/USDT
2025-07-28 12:40:59,883 - advanced_data_fetcher - INFO - Cached data for MANA/USDT 1h
2025-07-28 12:40:59,883 - advanced_data_fetcher - INFO - Fetching 4h data for MANA/USDT
2025-07-28 12:41:05,339 - advanced_data_fetcher - INFO - Cached data for MANA/USDT 4h
2025-07-28 12:41:05,339 - advanced_data_fetcher - INFO - Fetching 1d data for MANA/USDT
2025-07-28 12:41:06,528 - advanced_data_fetcher - INFO - Cached data for MANA/USDT 1d
2025-07-28 12:41:06,529 - advanced_data_fetcher - INFO - Fetching 1h data for MANTA/USDT
2025-07-28 12:41:13,215 - advanced_data_fetcher - WARNING - Low quality data for MANTA/USDT 1h: {'date_range_days': 556, 'min_required_days': 1460, 'actual_samples': 13368, 'expected_samples': 35040, 'completeness_ratio': 0.3815068493150685, 'gap_ratio': 0.0, 'avg_volume': np.float64(760387.894688809), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:41:14,881 - advanced_data_fetcher - INFO - Cached data for MANTA/USDT 1h
2025-07-28 12:41:14,881 - advanced_data_fetcher - INFO - Fetching 4h data for MANTA/USDT
2025-07-28 12:41:16,784 - advanced_data_fetcher - WARNING - Low quality data for MANTA/USDT 4h: {'date_range_days': 557, 'min_required_days': 1460, 'actual_samples': 3343, 'expected_samples': 8760, 'completeness_ratio': 0.38162100456621006, 'gap_ratio': 0.0, 'avg_volume': np.float64(3040641.751779838), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:41:17,847 - advanced_data_fetcher - INFO - Cached data for MANTA/USDT 4h
2025-07-28 12:41:17,850 - advanced_data_fetcher - INFO - Fetching 1d data for MANTA/USDT
2025-07-28 12:41:18,322 - advanced_data_fetcher - WARNING - Low quality data for MANTA/USDT 1d: {'date_range_days': 557, 'min_required_days': 1460, 'actual_samples': 558, 'expected_samples': 1460, 'completeness_ratio': 0.3821917808219178, 'gap_ratio': 0.0, 'avg_volume': np.float64(18216604.61684588), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:41:18,415 - advanced_data_fetcher - INFO - Cached data for MANTA/USDT 1d
2025-07-28 12:41:18,415 - advanced_data_fetcher - INFO - Fetching 1h data for MASK/USDT
2025-07-28 12:41:41,125 - advanced_data_fetcher - INFO - Cached data for MASK/USDT 1h
2025-07-28 12:41:41,125 - advanced_data_fetcher - INFO - Fetching 4h data for MASK/USDT
2025-07-28 12:41:46,553 - advanced_data_fetcher - INFO - Cached data for MASK/USDT 4h
2025-07-28 12:41:46,553 - advanced_data_fetcher - INFO - Fetching 1d data for MASK/USDT
2025-07-28 12:41:47,765 - advanced_data_fetcher - INFO - Cached data for MASK/USDT 1d
2025-07-28 12:41:47,765 - advanced_data_fetcher - INFO - Fetching 1h data for MAV/USDT
2025-07-28 12:41:56,866 - advanced_data_fetcher - WARNING - Low quality data for MAV/USDT 1h: {'date_range_days': 761, 'min_required_days': 1460, 'actual_samples': 18266, 'expected_samples': 35040, 'completeness_ratio': 0.5212899543378996, 'gap_ratio': 0.0, 'avg_volume': np.float64(1097661.7800832146), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:41:59,297 - advanced_data_fetcher - INFO - Cached data for MAV/USDT 1h
2025-07-28 12:41:59,300 - advanced_data_fetcher - INFO - Fetching 4h data for MAV/USDT
2025-07-28 12:42:01,631 - advanced_data_fetcher - WARNING - Low quality data for MAV/USDT 4h: {'date_range_days': 761, 'min_required_days': 1460, 'actual_samples': 4567, 'expected_samples': 8760, 'completeness_ratio': 0.5213470319634703, 'gap_ratio': 0.0, 'avg_volume': np.float64(4390167.096124371), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:42:02,200 - advanced_data_fetcher - INFO - Cached data for MAV/USDT 4h
2025-07-28 12:42:02,200 - advanced_data_fetcher - INFO - Fetching 1d data for MAV/USDT
2025-07-28 12:42:02,668 - advanced_data_fetcher - WARNING - Low quality data for MAV/USDT 1d: {'date_range_days': 761, 'min_required_days': 1460, 'actual_samples': 762, 'expected_samples': 1460, 'completeness_ratio': 0.5219178082191781, 'gap_ratio': 0.0, 'avg_volume': np.float64(26312195.706036747), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:42:02,800 - advanced_data_fetcher - INFO - Cached data for MAV/USDT 1d
2025-07-28 12:42:02,800 - advanced_data_fetcher - INFO - Fetching 1h data for MBL/USDT
2025-07-28 12:42:25,016 - advanced_data_fetcher - INFO - Cached data for MBL/USDT 1h
2025-07-28 12:42:25,016 - advanced_data_fetcher - INFO - Fetching 4h data for MBL/USDT
2025-07-28 12:42:30,800 - advanced_data_fetcher - INFO - Cached data for MBL/USDT 4h
2025-07-28 12:42:30,800 - advanced_data_fetcher - INFO - Fetching 1d data for MBL/USDT
2025-07-28 12:42:32,015 - advanced_data_fetcher - INFO - Cached data for MBL/USDT 1d
2025-07-28 12:42:32,015 - advanced_data_fetcher - INFO - Fetching 1h data for MBOX/USDT
2025-07-28 12:42:53,041 - advanced_data_fetcher - INFO - Cached data for MBOX/USDT 1h
2025-07-28 12:42:53,041 - advanced_data_fetcher - INFO - Fetching 4h data for MBOX/USDT
2025-07-28 12:42:58,500 - advanced_data_fetcher - INFO - Cached data for MBOX/USDT 4h
2025-07-28 12:42:58,500 - advanced_data_fetcher - INFO - Fetching 1d data for MBOX/USDT
2025-07-28 12:42:59,700 - advanced_data_fetcher - INFO - Cached data for MBOX/USDT 1d
2025-07-28 12:42:59,700 - advanced_data_fetcher - INFO - Fetching 1h data for MDT/USDT
2025-07-28 12:43:22,168 - advanced_data_fetcher - INFO - Cached data for MDT/USDT 1h
2025-07-28 12:43:22,168 - advanced_data_fetcher - INFO - Fetching 4h data for MDT/USDT
2025-07-28 12:43:27,874 - advanced_data_fetcher - INFO - Cached data for MDT/USDT 4h
2025-07-28 12:43:27,874 - advanced_data_fetcher - INFO - Fetching 1d data for MDT/USDT
2025-07-28 12:43:29,050 - advanced_data_fetcher - INFO - Cached data for MDT/USDT 1d
2025-07-28 12:43:29,050 - advanced_data_fetcher - INFO - Fetching 1h data for ME/USDT
2025-07-28 12:43:31,950 - advanced_data_fetcher - WARNING - Low quality data for ME/USDT 1h: {'date_range_days': 229, 'min_required_days': 1460, 'actual_samples': 5515, 'expected_samples': 35040, 'completeness_ratio': 0.15739155251141554, 'gap_ratio': 0.0, 'avg_volume': np.float64(259834.32242792385), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:43:32,650 - advanced_data_fetcher - INFO - Cached data for ME/USDT 1h
2025-07-28 12:43:32,650 - advanced_data_fetcher - INFO - Fetching 4h data for ME/USDT
2025-07-28 12:43:33,583 - advanced_data_fetcher - WARNING - Low quality data for ME/USDT 4h: {'date_range_days': 229, 'min_required_days': 1460, 'actual_samples': 1380, 'expected_samples': 8760, 'completeness_ratio': 0.15753424657534246, 'gap_ratio': 0.0, 'avg_volume': np.float64(1038395.8610072464), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:43:33,825 - advanced_data_fetcher - INFO - Cached data for ME/USDT 4h
2025-07-28 12:43:33,825 - advanced_data_fetcher - INFO - Fetching 1d data for ME/USDT
2025-07-28 12:43:34,274 - advanced_data_fetcher - WARNING - Low quality data for ME/USDT 1d: {'date_range_days': 230, 'min_required_days': 1460, 'actual_samples': 231, 'expected_samples': 1460, 'completeness_ratio': 0.15821917808219177, 'gap_ratio': 0.0, 'avg_volume': np.float64(6203403.844978354), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:43:34,381 - advanced_data_fetcher - INFO - Cached data for ME/USDT 1d
2025-07-28 12:43:34,381 - advanced_data_fetcher - INFO - Fetching 1h data for MEME/USDT
2025-07-28 12:43:42,265 - advanced_data_fetcher - WARNING - Low quality data for MEME/USDT 1h: {'date_range_days': 633, 'min_required_days': 1460, 'actual_samples': 15194, 'expected_samples': 35040, 'completeness_ratio': 0.43361872146118724, 'gap_ratio': 0.0, 'avg_volume': np.float64(81986126.6648019), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:43:44,150 - advanced_data_fetcher - INFO - Cached data for MEME/USDT 1h
2025-07-28 12:43:44,150 - advanced_data_fetcher - INFO - Fetching 4h data for MEME/USDT
2025-07-28 12:43:46,115 - advanced_data_fetcher - WARNING - Low quality data for MEME/USDT 4h: {'date_range_days': 633, 'min_required_days': 1460, 'actual_samples': 3799, 'expected_samples': 8760, 'completeness_ratio': 0.433675799086758, 'gap_ratio': 0.0, 'avg_volume': np.float64(327901344.70781785), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:43:46,631 - advanced_data_fetcher - INFO - Cached data for MEME/USDT 4h
2025-07-28 12:43:46,647 - advanced_data_fetcher - INFO - Fetching 1d data for MEME/USDT
2025-07-28 12:43:47,131 - advanced_data_fetcher - WARNING - Low quality data for MEME/USDT 1d: {'date_range_days': 633, 'min_required_days': 1460, 'actual_samples': 634, 'expected_samples': 1460, 'completeness_ratio': 0.43424657534246575, 'gap_ratio': 0.0, 'avg_volume': np.float64(1964822095.4968455), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:43:47,250 - advanced_data_fetcher - INFO - Cached data for MEME/USDT 1d
2025-07-28 12:43:47,250 - advanced_data_fetcher - INFO - Fetching 1h data for METIS/USDT
2025-07-28 12:43:53,415 - advanced_data_fetcher - WARNING - Low quality data for METIS/USDT 1h: {'date_range_days': 503, 'min_required_days': 1460, 'actual_samples': 12094, 'expected_samples': 35040, 'completeness_ratio': 0.34514840182648404, 'gap_ratio': 0.0, 'avg_volume': np.float64(4738.571662063833), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:43:54,975 - advanced_data_fetcher - INFO - Cached data for METIS/USDT 1h
2025-07-28 12:43:54,975 - advanced_data_fetcher - INFO - Fetching 4h data for METIS/USDT
2025-07-28 12:43:56,832 - advanced_data_fetcher - WARNING - Low quality data for METIS/USDT 4h: {'date_range_days': 503, 'min_required_days': 1460, 'actual_samples': 3024, 'expected_samples': 8760, 'completeness_ratio': 0.3452054794520548, 'gap_ratio': 0.0, 'avg_volume': np.float64(18951.15267228836), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:43:57,265 - advanced_data_fetcher - INFO - Cached data for METIS/USDT 4h
2025-07-28 12:43:57,265 - advanced_data_fetcher - INFO - Fetching 1d data for METIS/USDT
2025-07-28 12:43:57,732 - advanced_data_fetcher - WARNING - Low quality data for METIS/USDT 1d: {'date_range_days': 504, 'min_required_days': 1460, 'actual_samples': 505, 'expected_samples': 1460, 'completeness_ratio': 0.3458904109589041, 'gap_ratio': 0.0, 'avg_volume': np.float64(113481.75382376238), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:43:57,850 - advanced_data_fetcher - INFO - Cached data for METIS/USDT 1d
2025-07-28 12:43:57,850 - advanced_data_fetcher - INFO - Fetching 1h data for MINA/USDT
2025-07-28 12:44:19,175 - advanced_data_fetcher - INFO - Cached data for MINA/USDT 1h
2025-07-28 12:44:19,175 - advanced_data_fetcher - INFO - Fetching 4h data for MINA/USDT
2025-07-28 12:44:24,630 - advanced_data_fetcher - INFO - Cached data for MINA/USDT 4h
2025-07-28 12:44:24,630 - advanced_data_fetcher - INFO - Fetching 1d data for MINA/USDT
2025-07-28 12:44:25,815 - advanced_data_fetcher - INFO - Cached data for MINA/USDT 1d
2025-07-28 12:44:25,815 - advanced_data_fetcher - INFO - Fetching 1h data for MKR/USDT
2025-07-28 12:44:47,765 - advanced_data_fetcher - INFO - Cached data for MKR/USDT 1h
2025-07-28 12:44:47,765 - advanced_data_fetcher - INFO - Fetching 4h data for MKR/USDT
2025-07-28 12:44:54,556 - advanced_data_fetcher - INFO - Cached data for MKR/USDT 4h
2025-07-28 12:44:54,557 - advanced_data_fetcher - INFO - Fetching 1d data for MKR/USDT
2025-07-28 12:44:56,150 - advanced_data_fetcher - INFO - Cached data for MKR/USDT 1d
2025-07-28 12:44:56,150 - advanced_data_fetcher - INFO - Fetching 1h data for MLN/USDT
2025-07-28 12:45:18,675 - advanced_data_fetcher - INFO - Cached data for MLN/USDT 1h
2025-07-28 12:45:18,675 - advanced_data_fetcher - INFO - Fetching 4h data for MLN/USDT
2025-07-28 12:45:24,631 - advanced_data_fetcher - INFO - Cached data for MLN/USDT 4h
2025-07-28 12:45:24,631 - advanced_data_fetcher - INFO - Fetching 1d data for MLN/USDT
2025-07-28 12:45:25,825 - advanced_data_fetcher - INFO - Cached data for MLN/USDT 1d
2025-07-28 12:45:25,825 - advanced_data_fetcher - INFO - Fetching 1h data for MOVE/USDT
2025-07-28 12:45:28,700 - advanced_data_fetcher - WARNING - Low quality data for MOVE/USDT 1h: {'date_range_days': 230, 'min_required_days': 1460, 'actual_samples': 5542, 'expected_samples': 35040, 'completeness_ratio': 0.158162100456621, 'gap_ratio': 0.0, 'avg_volume': np.float64(2792667.678978708), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:45:29,381 - advanced_data_fetcher - INFO - Cached data for MOVE/USDT 1h
2025-07-28 12:45:29,381 - advanced_data_fetcher - INFO - Fetching 4h data for MOVE/USDT
2025-07-28 12:45:30,352 - advanced_data_fetcher - WARNING - Low quality data for MOVE/USDT 4h: {'date_range_days': 230, 'min_required_days': 1460, 'actual_samples': 1386, 'expected_samples': 8760, 'completeness_ratio': 0.15821917808219177, 'gap_ratio': 0.0, 'avg_volume': np.float64(11166640.892424244), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:45:30,559 - advanced_data_fetcher - INFO - Cached data for MOVE/USDT 4h
2025-07-28 12:45:30,560 - advanced_data_fetcher - INFO - Fetching 1d data for MOVE/USDT
2025-07-28 12:45:31,015 - advanced_data_fetcher - WARNING - Low quality data for MOVE/USDT 1d: {'date_range_days': 231, 'min_required_days': 1460, 'actual_samples': 232, 'expected_samples': 1460, 'completeness_ratio': 0.1589041095890411, 'gap_ratio': 0.0, 'avg_volume': np.float64(66711052.91767242), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:45:31,091 - advanced_data_fetcher - INFO - Cached data for MOVE/USDT 1d
2025-07-28 12:45:31,091 - advanced_data_fetcher - INFO - Fetching 1h data for MOVR/USDT
2025-07-28 12:45:51,000 - advanced_data_fetcher - INFO - Cached data for MOVR/USDT 1h
2025-07-28 12:45:51,000 - advanced_data_fetcher - INFO - Fetching 4h data for MOVR/USDT
2025-07-28 12:45:56,716 - advanced_data_fetcher - INFO - Cached data for MOVR/USDT 4h
2025-07-28 12:45:56,716 - advanced_data_fetcher - INFO - Fetching 1d data for MOVR/USDT
2025-07-28 12:45:57,850 - advanced_data_fetcher - INFO - Cached data for MOVR/USDT 1d
2025-07-28 12:45:57,850 - advanced_data_fetcher - INFO - Fetching 1h data for MTL/USDT
2025-07-28 12:46:20,465 - advanced_data_fetcher - INFO - Cached data for MTL/USDT 1h
2025-07-28 12:46:20,465 - advanced_data_fetcher - INFO - Fetching 4h data for MTL/USDT
2025-07-28 12:46:25,950 - advanced_data_fetcher - INFO - Cached data for MTL/USDT 4h
2025-07-28 12:46:25,950 - advanced_data_fetcher - INFO - Fetching 1d data for MTL/USDT
2025-07-28 12:46:27,200 - advanced_data_fetcher - INFO - Cached data for MTL/USDT 1d
2025-07-28 12:46:27,200 - advanced_data_fetcher - INFO - Fetching 1h data for MUBARAK/USDT
2025-07-28 12:46:28,650 - advanced_data_fetcher - WARNING - Low quality data for MUBARAK/USDT 1h: {'date_range_days': 122, 'min_required_days': 1460, 'actual_samples': 2941, 'expected_samples': 35040, 'completeness_ratio': 0.08393264840182649, 'gap_ratio': 0.0, 'avg_volume': np.float64(18499310.521931317), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:46:29,031 - advanced_data_fetcher - INFO - Cached data for MUBARAK/USDT 1h
2025-07-28 12:46:29,031 - advanced_data_fetcher - INFO - Fetching 4h data for MUBARAK/USDT
2025-07-28 12:46:29,540 - advanced_data_fetcher - WARNING - Low quality data for MUBARAK/USDT 4h: {'date_range_days': 122, 'min_required_days': 1460, 'actual_samples': 736, 'expected_samples': 8760, 'completeness_ratio': 0.08401826484018265, 'gap_ratio': 0.0, 'avg_volume': np.float64(73921837.28940217), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:46:29,700 - advanced_data_fetcher - INFO - Cached data for MUBARAK/USDT 4h
2025-07-28 12:46:29,700 - advanced_data_fetcher - INFO - Fetching 1d data for MUBARAK/USDT
2025-07-28 12:46:30,150 - advanced_data_fetcher - WARNING - Low quality data for MUBARAK/USDT 1d: {'date_range_days': 123, 'min_required_days': 1460, 'actual_samples': 124, 'expected_samples': 1460, 'completeness_ratio': 0.08493150684931507, 'gap_ratio': 0.0, 'avg_volume': np.float64(438761887.70161283), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:46:30,250 - advanced_data_fetcher - INFO - Cached data for MUBARAK/USDT 1d
2025-07-28 12:46:30,250 - advanced_data_fetcher - INFO - Fetching 1h data for NEAR/USDT
2025-07-28 12:46:52,670 - advanced_data_fetcher - INFO - Cached data for NEAR/USDT 1h
2025-07-28 12:46:52,670 - advanced_data_fetcher - INFO - Fetching 4h data for NEAR/USDT
2025-07-28 12:46:58,150 - advanced_data_fetcher - INFO - Cached data for NEAR/USDT 4h
2025-07-28 12:46:58,150 - advanced_data_fetcher - INFO - Fetching 1d data for NEAR/USDT
2025-07-28 12:46:59,381 - advanced_data_fetcher - INFO - Cached data for NEAR/USDT 1d
2025-07-28 12:46:59,381 - advanced_data_fetcher - INFO - Fetching 1h data for NEIRO/USDT
2025-07-28 12:47:03,416 - advanced_data_fetcher - WARNING - Low quality data for NEIRO/USDT 1h: {'date_range_days': 314, 'min_required_days': 1460, 'actual_samples': 7560, 'expected_samples': 35040, 'completeness_ratio': 0.21575342465753425, 'gap_ratio': 0.0, 'avg_volume': np.float64(5167629601.741799), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:47:04,494 - advanced_data_fetcher - INFO - Cached data for NEIRO/USDT 1h
2025-07-28 12:47:04,494 - advanced_data_fetcher - INFO - Fetching 4h data for NEIRO/USDT
2025-07-28 12:47:05,515 - advanced_data_fetcher - WARNING - Low quality data for NEIRO/USDT 4h: {'date_range_days': 315, 'min_required_days': 1460, 'actual_samples': 1891, 'expected_samples': 8760, 'completeness_ratio': 0.2158675799086758, 'gap_ratio': 0.0, 'avg_volume': np.float64(20659587408.338444), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:47:05,825 - advanced_data_fetcher - INFO - Cached data for NEIRO/USDT 4h
2025-07-28 12:47:05,825 - advanced_data_fetcher - INFO - Fetching 1d data for NEIRO/USDT
2025-07-28 12:47:06,273 - advanced_data_fetcher - WARNING - Low quality data for NEIRO/USDT 1d: {'date_range_days': 315, 'min_required_days': 1460, 'actual_samples': 316, 'expected_samples': 1460, 'completeness_ratio': 0.21643835616438356, 'gap_ratio': 0.0, 'avg_volume': np.float64(123630632244.20253), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:47:06,415 - advanced_data_fetcher - INFO - Cached data for NEIRO/USDT 1d
2025-07-28 12:47:06,415 - advanced_data_fetcher - INFO - Fetching 1h data for NEO/USDT
2025-07-28 12:47:28,023 - advanced_data_fetcher - INFO - Cached data for NEO/USDT 1h
2025-07-28 12:47:28,024 - advanced_data_fetcher - INFO - Fetching 4h data for NEO/USDT
2025-07-28 12:47:33,847 - advanced_data_fetcher - INFO - Cached data for NEO/USDT 4h
2025-07-28 12:47:33,847 - advanced_data_fetcher - INFO - Fetching 1d data for NEO/USDT
2025-07-28 12:47:35,031 - advanced_data_fetcher - INFO - Cached data for NEO/USDT 1d
2025-07-28 12:47:35,031 - advanced_data_fetcher - INFO - Fetching 1h data for NEWT/USDT
2025-07-28 12:47:35,501 - advanced_data_fetcher - WARNING - Low quality data for NEWT/USDT 1h: {'date_range_days': 33, 'min_required_days': 1460, 'actual_samples': 812, 'expected_samples': 35040, 'completeness_ratio': 0.02317351598173516, 'gap_ratio': 0.0, 'avg_volume': np.float64(5060466.153571429), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:47:35,631 - advanced_data_fetcher - INFO - Cached data for NEWT/USDT 1h
2025-07-28 12:47:35,631 - advanced_data_fetcher - INFO - Fetching 4h data for NEWT/USDT
2025-07-28 12:47:36,549 - advanced_data_fetcher - WARNING - Low quality data for NEWT/USDT 4h: {'date_range_days': 33, 'min_required_days': 1460, 'actual_samples': 204, 'expected_samples': 8760, 'completeness_ratio': 0.023287671232876714, 'gap_ratio': 0.0, 'avg_volume': np.float64(20142639.787745096), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:47:36,628 - advanced_data_fetcher - INFO - Cached data for NEWT/USDT 4h
2025-07-28 12:47:36,629 - advanced_data_fetcher - INFO - Fetching 1d data for NEWT/USDT
2025-07-28 12:47:37,065 - advanced_data_fetcher - WARNING - Low quality data for NEWT/USDT 1d: {'date_range_days': 34, 'min_required_days': 1460, 'actual_samples': 35, 'expected_samples': 1460, 'completeness_ratio': 0.023972602739726026, 'gap_ratio': 0.0, 'avg_volume': np.float64(117402814.76285715), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:47:37,125 - advanced_data_fetcher - INFO - Cached data for NEWT/USDT 1d
2025-07-28 12:47:37,125 - advanced_data_fetcher - INFO - Fetching 1h data for NEXO/USDT
2025-07-28 12:47:54,557 - advanced_data_fetcher - INFO - Cached data for NEXO/USDT 1h
2025-07-28 12:47:54,558 - advanced_data_fetcher - INFO - Fetching 4h data for NEXO/USDT
2025-07-28 12:47:59,331 - advanced_data_fetcher - INFO - Cached data for NEXO/USDT 4h
2025-07-28 12:47:59,331 - advanced_data_fetcher - INFO - Fetching 1d data for NEXO/USDT
2025-07-28 12:48:00,431 - advanced_data_fetcher - INFO - Cached data for NEXO/USDT 1d
2025-07-28 12:48:00,431 - advanced_data_fetcher - INFO - Fetching 1h data for NFP/USDT
2025-07-28 12:48:07,231 - advanced_data_fetcher - WARNING - Low quality data for NFP/USDT 1h: {'date_range_days': 578, 'min_required_days': 1460, 'actual_samples': 13896, 'expected_samples': 35040, 'completeness_ratio': 0.39657534246575343, 'gap_ratio': 0.0, 'avg_volume': np.float64(905823.8050518135), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:48:08,965 - advanced_data_fetcher - INFO - Cached data for NFP/USDT 1h
2025-07-28 12:48:08,965 - advanced_data_fetcher - INFO - Fetching 4h data for NFP/USDT
2025-07-28 12:48:11,250 - advanced_data_fetcher - WARNING - Low quality data for NFP/USDT 4h: {'date_range_days': 579, 'min_required_days': 1460, 'actual_samples': 3475, 'expected_samples': 8760, 'completeness_ratio': 0.396689497716895, 'gap_ratio': 0.0, 'avg_volume': np.float64(3622253.4670503596), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:48:11,716 - advanced_data_fetcher - INFO - Cached data for NFP/USDT 4h
2025-07-28 12:48:11,716 - advanced_data_fetcher - INFO - Fetching 1d data for NFP/USDT
2025-07-28 12:48:12,200 - advanced_data_fetcher - WARNING - Low quality data for NFP/USDT 1d: {'date_range_days': 579, 'min_required_days': 1460, 'actual_samples': 580, 'expected_samples': 1460, 'completeness_ratio': 0.3972602739726027, 'gap_ratio': 0.0, 'avg_volume': np.float64(21702294.479310345), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:48:12,340 - advanced_data_fetcher - INFO - Cached data for NFP/USDT 1d
2025-07-28 12:48:12,340 - advanced_data_fetcher - INFO - Fetching 1h data for NIL/USDT
2025-07-28 12:48:14,265 - advanced_data_fetcher - WARNING - Low quality data for NIL/USDT 1h: {'date_range_days': 125, 'min_required_days': 1460, 'actual_samples': 3021, 'expected_samples': 35040, 'completeness_ratio': 0.08621575342465754, 'gap_ratio': 0.0, 'avg_volume': np.float64(931628.7061569015), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:48:14,700 - advanced_data_fetcher - INFO - Cached data for NIL/USDT 1h
2025-07-28 12:48:14,700 - advanced_data_fetcher - INFO - Fetching 4h data for NIL/USDT
2025-07-28 12:48:15,186 - advanced_data_fetcher - WARNING - Low quality data for NIL/USDT 4h: {'date_range_days': 125, 'min_required_days': 1460, 'actual_samples': 756, 'expected_samples': 8760, 'completeness_ratio': 0.0863013698630137, 'gap_ratio': 0.0, 'avg_volume': np.float64(3722817.8853174606), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:48:15,316 - advanced_data_fetcher - INFO - Cached data for NIL/USDT 4h
2025-07-28 12:48:15,316 - advanced_data_fetcher - INFO - Fetching 1d data for NIL/USDT
2025-07-28 12:48:15,750 - advanced_data_fetcher - WARNING - Low quality data for NIL/USDT 1d: {'date_range_days': 126, 'min_required_days': 1460, 'actual_samples': 127, 'expected_samples': 1460, 'completeness_ratio': 0.08698630136986302, 'gap_ratio': 0.0, 'avg_volume': np.float64(22161026.151968505), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:48:15,831 - advanced_data_fetcher - INFO - Cached data for NIL/USDT 1d
2025-07-28 12:48:15,831 - advanced_data_fetcher - INFO - Fetching 1h data for NKN/USDT
2025-07-28 12:48:37,651 - advanced_data_fetcher - INFO - Cached data for NKN/USDT 1h
2025-07-28 12:48:37,651 - advanced_data_fetcher - INFO - Fetching 4h data for NKN/USDT
2025-07-28 12:48:43,146 - advanced_data_fetcher - INFO - Cached data for NKN/USDT 4h
2025-07-28 12:48:43,146 - advanced_data_fetcher - INFO - Fetching 1d data for NKN/USDT
2025-07-28 12:48:44,321 - advanced_data_fetcher - INFO - Cached data for NKN/USDT 1d
2025-07-28 12:48:44,322 - advanced_data_fetcher - INFO - Fetching 1h data for NMR/USDT
2025-07-28 12:49:06,224 - advanced_data_fetcher - INFO - Cached data for NMR/USDT 1h
2025-07-28 12:49:06,225 - advanced_data_fetcher - INFO - Fetching 4h data for NMR/USDT
2025-07-28 12:49:13,166 - advanced_data_fetcher - INFO - Cached data for NMR/USDT 4h
2025-07-28 12:49:13,166 - advanced_data_fetcher - INFO - Fetching 1d data for NMR/USDT
2025-07-28 12:49:14,350 - advanced_data_fetcher - INFO - Cached data for NMR/USDT 1d
2025-07-28 12:49:14,350 - advanced_data_fetcher - INFO - Fetching 1h data for NOT/USDT
2025-07-28 12:49:19,715 - advanced_data_fetcher - WARNING - Low quality data for NOT/USDT 1h: {'date_range_days': 437, 'min_required_days': 1460, 'actual_samples': 10510, 'expected_samples': 35040, 'completeness_ratio': 0.2999429223744292, 'gap_ratio': 0.0, 'avg_volume': np.float64(285910447.15661275), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:49:21,050 - advanced_data_fetcher - INFO - Cached data for NOT/USDT 1h
2025-07-28 12:49:21,050 - advanced_data_fetcher - INFO - Fetching 4h data for NOT/USDT
2025-07-28 12:49:22,500 - advanced_data_fetcher - WARNING - Low quality data for NOT/USDT 4h: {'date_range_days': 437, 'min_required_days': 1460, 'actual_samples': 2628, 'expected_samples': 8760, 'completeness_ratio': 0.3, 'gap_ratio': 0.0, 'avg_volume': np.float64(1143424209.2290716), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:49:22,900 - advanced_data_fetcher - INFO - Cached data for NOT/USDT 4h
2025-07-28 12:49:22,900 - advanced_data_fetcher - INFO - Fetching 1d data for NOT/USDT
2025-07-28 12:49:23,366 - advanced_data_fetcher - WARNING - Low quality data for NOT/USDT 1d: {'date_range_days': 438, 'min_required_days': 1460, 'actual_samples': 439, 'expected_samples': 1460, 'completeness_ratio': 0.30068493150684933, 'gap_ratio': 0.0, 'avg_volume': np.float64(6844917589.644647), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:49:23,481 - advanced_data_fetcher - INFO - Cached data for NOT/USDT 1d
2025-07-28 12:49:23,481 - advanced_data_fetcher - INFO - Fetching 1h data for NTRN/USDT
2025-07-28 12:49:31,481 - advanced_data_fetcher - WARNING - Low quality data for NTRN/USDT 1h: {'date_range_days': 656, 'min_required_days': 1460, 'actual_samples': 15766, 'expected_samples': 35040, 'completeness_ratio': 0.44994292237442923, 'gap_ratio': 0.0, 'avg_volume': np.float64(472067.89432956994), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:49:33,415 - advanced_data_fetcher - INFO - Cached data for NTRN/USDT 1h
2025-07-28 12:49:33,415 - advanced_data_fetcher - INFO - Fetching 4h data for NTRN/USDT
2025-07-28 12:49:35,416 - advanced_data_fetcher - WARNING - Low quality data for NTRN/USDT 4h: {'date_range_days': 656, 'min_required_days': 1460, 'actual_samples': 3942, 'expected_samples': 8760, 'completeness_ratio': 0.45, 'gap_ratio': 0.0, 'avg_volume': np.float64(1888032.0705225773), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:49:35,965 - advanced_data_fetcher - INFO - Cached data for NTRN/USDT 4h
2025-07-28 12:49:35,965 - advanced_data_fetcher - INFO - Fetching 1d data for NTRN/USDT
2025-07-28 12:49:36,765 - advanced_data_fetcher - WARNING - Low quality data for NTRN/USDT 1d: {'date_range_days': 657, 'min_required_days': 1460, 'actual_samples': 658, 'expected_samples': 1460, 'completeness_ratio': 0.4506849315068493, 'gap_ratio': 0.0, 'avg_volume': np.float64(11310976.325227963), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:49:36,866 - advanced_data_fetcher - INFO - Cached data for NTRN/USDT 1d
2025-07-28 12:49:36,866 - advanced_data_fetcher - INFO - Fetching 1h data for NXPC/USDT
2025-07-28 12:49:37,815 - advanced_data_fetcher - WARNING - Low quality data for NXPC/USDT 1h: {'date_range_days': 74, 'min_required_days': 1460, 'actual_samples': 1779, 'expected_samples': 35040, 'completeness_ratio': 0.05077054794520548, 'gap_ratio': 0.0, 'avg_volume': np.float64(589423.4042158516), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:49:38,074 - advanced_data_fetcher - INFO - Cached data for NXPC/USDT 1h
2025-07-28 12:49:38,074 - advanced_data_fetcher - INFO - Fetching 4h data for NXPC/USDT
2025-07-28 12:49:38,543 - advanced_data_fetcher - WARNING - Low quality data for NXPC/USDT 4h: {'date_range_days': 74, 'min_required_days': 1460, 'actual_samples': 446, 'expected_samples': 8760, 'completeness_ratio': 0.05091324200913242, 'gap_ratio': 0.0, 'avg_volume': np.float64(2351085.7311659195), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:49:38,658 - advanced_data_fetcher - INFO - Cached data for NXPC/USDT 4h
2025-07-28 12:49:38,658 - advanced_data_fetcher - INFO - Fetching 1d data for NXPC/USDT
2025-07-28 12:49:39,091 - advanced_data_fetcher - WARNING - Low quality data for NXPC/USDT 1d: {'date_range_days': 74, 'min_required_days': 1460, 'actual_samples': 75, 'expected_samples': 1460, 'completeness_ratio': 0.05136986301369863, 'gap_ratio': 0.0, 'avg_volume': np.float64(13981123.148), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:49:39,188 - advanced_data_fetcher - INFO - Cached data for NXPC/USDT 1d
2025-07-28 12:49:39,188 - advanced_data_fetcher - INFO - Fetching 1h data for OG/USDT
2025-07-28 12:50:01,141 - advanced_data_fetcher - INFO - Cached data for OG/USDT 1h
2025-07-28 12:50:01,141 - advanced_data_fetcher - INFO - Fetching 4h data for OG/USDT
2025-07-28 12:50:06,615 - advanced_data_fetcher - INFO - Cached data for OG/USDT 4h
2025-07-28 12:50:06,615 - advanced_data_fetcher - INFO - Fetching 1d data for OG/USDT
2025-07-28 12:50:07,781 - advanced_data_fetcher - INFO - Cached data for OG/USDT 1d
2025-07-28 12:50:07,781 - advanced_data_fetcher - INFO - Fetching 1h data for OGN/USDT
2025-07-28 12:50:30,026 - advanced_data_fetcher - INFO - Cached data for OGN/USDT 1h
2025-07-28 12:50:30,026 - advanced_data_fetcher - INFO - Fetching 4h data for OGN/USDT
2025-07-28 12:50:35,642 - advanced_data_fetcher - INFO - Cached data for OGN/USDT 4h
2025-07-28 12:50:35,642 - advanced_data_fetcher - INFO - Fetching 1d data for OGN/USDT
2025-07-28 12:50:37,200 - advanced_data_fetcher - INFO - Cached data for OGN/USDT 1d
2025-07-28 12:50:37,200 - advanced_data_fetcher - INFO - Fetching 1h data for OM/USDT
2025-07-28 12:50:59,466 - advanced_data_fetcher - INFO - Cached data for OM/USDT 1h
2025-07-28 12:50:59,469 - advanced_data_fetcher - INFO - Fetching 4h data for OM/USDT
2025-07-28 12:51:04,881 - advanced_data_fetcher - INFO - Cached data for OM/USDT 4h
2025-07-28 12:51:04,896 - advanced_data_fetcher - INFO - Fetching 1d data for OM/USDT
2025-07-28 12:51:06,115 - advanced_data_fetcher - INFO - Cached data for OM/USDT 1d
2025-07-28 12:51:06,115 - advanced_data_fetcher - INFO - Fetching 1h data for OMNI/USDT
2025-07-28 12:51:11,766 - advanced_data_fetcher - WARNING - Low quality data for OMNI/USDT 1h: {'date_range_days': 466, 'min_required_days': 1460, 'actual_samples': 11206, 'expected_samples': 35040, 'completeness_ratio': 0.31980593607305935, 'gap_ratio': 0.0, 'avg_volume': np.float64(42073.41048991612), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:51:13,131 - advanced_data_fetcher - INFO - Cached data for OMNI/USDT 1h
2025-07-28 12:51:13,131 - advanced_data_fetcher - INFO - Fetching 4h data for OMNI/USDT
2025-07-28 12:51:14,568 - advanced_data_fetcher - WARNING - Low quality data for OMNI/USDT 4h: {'date_range_days': 466, 'min_required_days': 1460, 'actual_samples': 2802, 'expected_samples': 8760, 'completeness_ratio': 0.3198630136986301, 'gap_ratio': 0.0, 'avg_volume': np.float64(168263.67829764454), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:51:14,940 - advanced_data_fetcher - INFO - Cached data for OMNI/USDT 4h
2025-07-28 12:51:14,940 - advanced_data_fetcher - INFO - Fetching 1d data for OMNI/USDT
2025-07-28 12:51:15,400 - advanced_data_fetcher - WARNING - Low quality data for OMNI/USDT 1d: {'date_range_days': 467, 'min_required_days': 1460, 'actual_samples': 468, 'expected_samples': 1460, 'completeness_ratio': 0.32054794520547947, 'gap_ratio': 0.0, 'avg_volume': np.float64(1007424.8431410257), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:51:15,515 - advanced_data_fetcher - INFO - Cached data for OMNI/USDT 1d
2025-07-28 12:51:15,515 - advanced_data_fetcher - INFO - Fetching 1h data for ONDO/USDT
2025-07-28 12:51:16,965 - advanced_data_fetcher - WARNING - Low quality data for ONDO/USDT 1h: {'date_range_days': 107, 'min_required_days': 1460, 'actual_samples': 2588, 'expected_samples': 35040, 'completeness_ratio': 0.07385844748858447, 'gap_ratio': 0.0, 'avg_volume': np.float64(910224.6091576507), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:51:17,408 - advanced_data_fetcher - INFO - Cached data for ONDO/USDT 1h
2025-07-28 12:51:17,408 - advanced_data_fetcher - INFO - Fetching 4h data for ONDO/USDT
2025-07-28 12:51:17,881 - advanced_data_fetcher - WARNING - Low quality data for ONDO/USDT 4h: {'date_range_days': 107, 'min_required_days': 1460, 'actual_samples': 648, 'expected_samples': 8760, 'completeness_ratio': 0.07397260273972603, 'gap_ratio': 0.0, 'avg_volume': np.float64(3635279.766203704), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:51:18,016 - advanced_data_fetcher - INFO - Cached data for ONDO/USDT 4h
2025-07-28 12:51:18,016 - advanced_data_fetcher - INFO - Fetching 1d data for ONDO/USDT
2025-07-28 12:51:18,465 - advanced_data_fetcher - WARNING - Low quality data for ONDO/USDT 1d: {'date_range_days': 108, 'min_required_days': 1460, 'actual_samples': 109, 'expected_samples': 1460, 'completeness_ratio': 0.07465753424657534, 'gap_ratio': 0.0, 'avg_volume': np.float64(21611571.45412844), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:51:18,540 - advanced_data_fetcher - INFO - Cached data for ONDO/USDT 1d
2025-07-28 12:51:18,541 - advanced_data_fetcher - INFO - Fetching 1h data for ONE/USDT
2025-07-28 12:51:40,725 - advanced_data_fetcher - INFO - Cached data for ONE/USDT 1h
2025-07-28 12:51:40,725 - advanced_data_fetcher - INFO - Fetching 4h data for ONE/USDT
2025-07-28 12:51:46,559 - advanced_data_fetcher - INFO - Cached data for ONE/USDT 4h
2025-07-28 12:51:46,559 - advanced_data_fetcher - INFO - Fetching 1d data for ONE/USDT
2025-07-28 12:51:47,950 - advanced_data_fetcher - INFO - Cached data for ONE/USDT 1d
2025-07-28 12:51:47,950 - advanced_data_fetcher - INFO - Fetching 1h data for ONG/USDT
2025-07-28 12:52:10,041 - advanced_data_fetcher - INFO - Cached data for ONG/USDT 1h
2025-07-28 12:52:10,041 - advanced_data_fetcher - INFO - Fetching 4h data for ONG/USDT
2025-07-28 12:52:15,500 - advanced_data_fetcher - INFO - Cached data for ONG/USDT 4h
2025-07-28 12:52:15,500 - advanced_data_fetcher - INFO - Fetching 1d data for ONG/USDT
2025-07-28 12:52:16,700 - advanced_data_fetcher - INFO - Cached data for ONG/USDT 1d
2025-07-28 12:52:16,700 - advanced_data_fetcher - INFO - Fetching 1h data for ONT/USDT
2025-07-28 12:52:38,740 - advanced_data_fetcher - INFO - Cached data for ONT/USDT 1h
2025-07-28 12:52:38,740 - advanced_data_fetcher - INFO - Fetching 4h data for ONT/USDT
2025-07-28 12:52:44,200 - advanced_data_fetcher - INFO - Cached data for ONT/USDT 4h
2025-07-28 12:52:44,200 - advanced_data_fetcher - INFO - Fetching 1d data for ONT/USDT
2025-07-28 12:52:45,416 - advanced_data_fetcher - INFO - Cached data for ONT/USDT 1d
2025-07-28 12:52:45,416 - advanced_data_fetcher - INFO - Fetching 1h data for OP/USDT
2025-07-28 12:52:58,915 - advanced_data_fetcher - WARNING - Low quality data for OP/USDT 1h: {'date_range_days': 1153, 'min_required_days': 1460, 'actual_samples': 27673, 'expected_samples': 35040, 'completeness_ratio': 0.7897545662100457, 'gap_ratio': 3.613500036135e-05, 'avg_volume': np.float64(1126255.525400571), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:53:02,371 - advanced_data_fetcher - INFO - Cached data for OP/USDT 1h
2025-07-28 12:53:02,371 - advanced_data_fetcher - INFO - Fetching 4h data for OP/USDT
2025-07-28 12:53:05,941 - advanced_data_fetcher - WARNING - Low quality data for OP/USDT 4h: {'date_range_days': 1153, 'min_required_days': 1460, 'actual_samples': 6919, 'expected_samples': 8760, 'completeness_ratio': 0.7898401826484018, 'gap_ratio': 0.0, 'avg_volume': np.float64(4504534.303084261), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:53:06,815 - advanced_data_fetcher - INFO - Cached data for OP/USDT 4h
2025-07-28 12:53:06,815 - advanced_data_fetcher - INFO - Fetching 1d data for OP/USDT
2025-07-28 12:53:07,794 - advanced_data_fetcher - WARNING - Low quality data for OP/USDT 1d: {'date_range_days': 1153, 'min_required_days': 1460, 'actual_samples': 1154, 'expected_samples': 1460, 'completeness_ratio': 0.7904109589041096, 'gap_ratio': 0.0, 'avg_volume': np.float64(27007688.95951473), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:53:07,965 - advanced_data_fetcher - INFO - Cached data for OP/USDT 1d
2025-07-28 12:53:07,965 - advanced_data_fetcher - INFO - Fetching 1h data for ORCA/USDT
2025-07-28 12:53:10,880 - advanced_data_fetcher - WARNING - Low quality data for ORCA/USDT 1h: {'date_range_days': 233, 'min_required_days': 1460, 'actual_samples': 5613, 'expected_samples': 35040, 'completeness_ratio': 0.16018835616438357, 'gap_ratio': 0.0, 'avg_volume': np.float64(109404.87905041869), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:53:11,575 - advanced_data_fetcher - INFO - Cached data for ORCA/USDT 1h
2025-07-28 12:53:11,575 - advanced_data_fetcher - INFO - Fetching 4h data for ORCA/USDT
2025-07-28 12:53:12,527 - advanced_data_fetcher - WARNING - Low quality data for ORCA/USDT 4h: {'date_range_days': 233, 'min_required_days': 1460, 'actual_samples': 1404, 'expected_samples': 8760, 'completeness_ratio': 0.16027397260273973, 'gap_ratio': 0.0, 'avg_volume': np.float64(437385.7450925926), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:53:12,741 - advanced_data_fetcher - INFO - Cached data for ORCA/USDT 4h
2025-07-28 12:53:12,741 - advanced_data_fetcher - INFO - Fetching 1d data for ORCA/USDT
2025-07-28 12:53:13,186 - advanced_data_fetcher - WARNING - Low quality data for ORCA/USDT 1d: {'date_range_days': 234, 'min_required_days': 1460, 'actual_samples': 235, 'expected_samples': 1460, 'completeness_ratio': 0.16095890410958905, 'gap_ratio': 0.0, 'avg_volume': np.float64(2613148.503744681), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:53:13,249 - advanced_data_fetcher - INFO - Cached data for ORCA/USDT 1d
2025-07-28 12:53:13,249 - advanced_data_fetcher - INFO - Fetching 1h data for ORDI/USDT
2025-07-28 12:53:21,500 - advanced_data_fetcher - WARNING - Low quality data for ORDI/USDT 1h: {'date_range_days': 628, 'min_required_days': 1460, 'actual_samples': 15094, 'expected_samples': 35040, 'completeness_ratio': 0.4307648401826484, 'gap_ratio': 0.0, 'avg_volume': np.float64(79155.6354531602), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:53:23,350 - advanced_data_fetcher - INFO - Cached data for ORDI/USDT 1h
2025-07-28 12:53:23,350 - advanced_data_fetcher - INFO - Fetching 4h data for ORDI/USDT
2025-07-28 12:53:25,616 - advanced_data_fetcher - WARNING - Low quality data for ORDI/USDT 4h: {'date_range_days': 628, 'min_required_days': 1460, 'actual_samples': 3774, 'expected_samples': 8760, 'completeness_ratio': 0.43082191780821916, 'gap_ratio': 0.0, 'avg_volume': np.float64(316580.62380763114), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:53:26,131 - advanced_data_fetcher - INFO - Cached data for ORDI/USDT 4h
2025-07-28 12:53:26,131 - advanced_data_fetcher - INFO - Fetching 1d data for ORDI/USDT
2025-07-28 12:53:26,615 - advanced_data_fetcher - WARNING - Low quality data for ORDI/USDT 1d: {'date_range_days': 629, 'min_required_days': 1460, 'actual_samples': 630, 'expected_samples': 1460, 'completeness_ratio': 0.4315068493150685, 'gap_ratio': 0.0, 'avg_volume': np.float64(1896468.690619048), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:53:26,731 - advanced_data_fetcher - INFO - Cached data for ORDI/USDT 1d
2025-07-28 12:53:26,731 - advanced_data_fetcher - INFO - Fetching 1h data for OSMO/USDT
2025-07-28 12:53:38,966 - advanced_data_fetcher - WARNING - Low quality data for OSMO/USDT 1h: {'date_range_days': 1003, 'min_required_days': 1460, 'actual_samples': 24095, 'expected_samples': 35040, 'completeness_ratio': 0.687642694063927, 'gap_ratio': 4.150066401062417e-05, 'avg_volume': np.float64(163362.08280846645), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:53:41,981 - advanced_data_fetcher - INFO - Cached data for OSMO/USDT 1h
2025-07-28 12:53:41,981 - advanced_data_fetcher - INFO - Fetching 4h data for OSMO/USDT
2025-07-28 12:53:45,350 - advanced_data_fetcher - WARNING - Low quality data for OSMO/USDT 4h: {'date_range_days': 1004, 'min_required_days': 1460, 'actual_samples': 6025, 'expected_samples': 8760, 'completeness_ratio': 0.6877853881278538, 'gap_ratio': 0.0, 'avg_volume': np.float64(653312.7654887966), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:53:46,130 - advanced_data_fetcher - INFO - Cached data for OSMO/USDT 4h
2025-07-28 12:53:46,130 - advanced_data_fetcher - INFO - Fetching 1d data for OSMO/USDT
2025-07-28 12:53:47,042 - advanced_data_fetcher - WARNING - Low quality data for OSMO/USDT 1d: {'date_range_days': 1004, 'min_required_days': 1460, 'actual_samples': 1005, 'expected_samples': 1460, 'completeness_ratio': 0.6883561643835616, 'gap_ratio': 0.0, 'avg_volume': np.float64(3916626.2806666666), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:53:47,225 - advanced_data_fetcher - INFO - Cached data for OSMO/USDT 1d
2025-07-28 12:53:47,225 - advanced_data_fetcher - INFO - Fetching 1h data for OXT/USDT
2025-07-28 12:54:09,015 - advanced_data_fetcher - INFO - Cached data for OXT/USDT 1h
2025-07-28 12:54:09,015 - advanced_data_fetcher - INFO - Fetching 4h data for OXT/USDT
2025-07-28 12:54:15,515 - advanced_data_fetcher - INFO - Cached data for OXT/USDT 4h
2025-07-28 12:54:15,515 - advanced_data_fetcher - INFO - Fetching 1d data for OXT/USDT
2025-07-28 12:54:16,700 - advanced_data_fetcher - INFO - Cached data for OXT/USDT 1d
2025-07-28 12:54:16,715 - advanced_data_fetcher - INFO - Fetching 1h data for PARTI/USDT
2025-07-28 12:54:18,166 - advanced_data_fetcher - WARNING - Low quality data for PARTI/USDT 1h: {'date_range_days': 124, 'min_required_days': 1460, 'actual_samples': 2997, 'expected_samples': 35040, 'completeness_ratio': 0.08553082191780823, 'gap_ratio': 0.0, 'avg_volume': np.float64(1837116.291558225), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:54:18,581 - advanced_data_fetcher - INFO - Cached data for PARTI/USDT 1h
2025-07-28 12:54:18,582 - advanced_data_fetcher - INFO - Fetching 4h data for PARTI/USDT
2025-07-28 12:54:19,066 - advanced_data_fetcher - WARNING - Low quality data for PARTI/USDT 4h: {'date_range_days': 124, 'min_required_days': 1460, 'actual_samples': 750, 'expected_samples': 8760, 'completeness_ratio': 0.08561643835616438, 'gap_ratio': 0.0, 'avg_volume': np.float64(7341116.701066667), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:54:19,215 - advanced_data_fetcher - INFO - Cached data for PARTI/USDT 4h
2025-07-28 12:54:19,215 - advanced_data_fetcher - INFO - Fetching 1d data for PARTI/USDT
2025-07-28 12:54:19,650 - advanced_data_fetcher - WARNING - Low quality data for PARTI/USDT 1d: {'date_range_days': 125, 'min_required_days': 1460, 'actual_samples': 126, 'expected_samples': 1460, 'completeness_ratio': 0.0863013698630137, 'gap_ratio': 0.0, 'avg_volume': np.float64(43697123.22063492), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:54:19,719 - advanced_data_fetcher - INFO - Cached data for PARTI/USDT 1d
2025-07-28 12:54:19,719 - advanced_data_fetcher - INFO - Fetching 1h data for PAXG/USDT
2025-07-28 12:54:41,685 - advanced_data_fetcher - INFO - Cached data for PAXG/USDT 1h
2025-07-28 12:54:41,685 - advanced_data_fetcher - INFO - Fetching 4h data for PAXG/USDT
2025-07-28 12:54:47,200 - advanced_data_fetcher - INFO - Cached data for PAXG/USDT 4h
2025-07-28 12:54:47,200 - advanced_data_fetcher - INFO - Fetching 1d data for PAXG/USDT
2025-07-28 12:54:48,400 - advanced_data_fetcher - INFO - Cached data for PAXG/USDT 1d
2025-07-28 12:54:48,400 - advanced_data_fetcher - INFO - Fetching 1h data for PENDLE/USDT
2025-07-28 12:54:57,866 - advanced_data_fetcher - WARNING - Low quality data for PENDLE/USDT 1h: {'date_range_days': 755, 'min_required_days': 1460, 'actual_samples': 18144, 'expected_samples': 35040, 'completeness_ratio': 0.5178082191780822, 'gap_ratio': 0.0, 'avg_volume': np.float64(253507.22345127867), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:55:00,150 - advanced_data_fetcher - INFO - Cached data for PENDLE/USDT 1h
2025-07-28 12:55:00,150 - advanced_data_fetcher - INFO - Fetching 4h data for PENDLE/USDT
2025-07-28 12:55:02,615 - advanced_data_fetcher - WARNING - Low quality data for PENDLE/USDT 4h: {'date_range_days': 756, 'min_required_days': 1460, 'actual_samples': 4537, 'expected_samples': 8760, 'completeness_ratio': 0.5179223744292237, 'gap_ratio': 0.0, 'avg_volume': np.float64(1013805.4484240687), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:55:03,150 - advanced_data_fetcher - INFO - Cached data for PENDLE/USDT 4h
2025-07-28 12:55:03,150 - advanced_data_fetcher - INFO - Fetching 1d data for PENDLE/USDT
2025-07-28 12:55:03,641 - advanced_data_fetcher - WARNING - Low quality data for PENDLE/USDT 1d: {'date_range_days': 756, 'min_required_days': 1460, 'actual_samples': 757, 'expected_samples': 1460, 'completeness_ratio': 0.5184931506849315, 'gap_ratio': 0.0, 'avg_volume': np.float64(6076136.485468957), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:55:03,765 - advanced_data_fetcher - INFO - Cached data for PENDLE/USDT 1d
2025-07-28 12:55:03,765 - advanced_data_fetcher - INFO - Fetching 1h data for PENGU/USDT
2025-07-28 12:55:06,650 - advanced_data_fetcher - WARNING - Low quality data for PENGU/USDT 1h: {'date_range_days': 222, 'min_required_days': 1460, 'actual_samples': 5348, 'expected_samples': 35040, 'completeness_ratio': 0.15262557077625571, 'gap_ratio': 0.0, 'avg_volume': np.float64(151363449.59386685), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:55:07,365 - advanced_data_fetcher - INFO - Cached data for PENGU/USDT 1h
2025-07-28 12:55:07,381 - advanced_data_fetcher - INFO - Fetching 4h data for PENGU/USDT
2025-07-28 12:55:08,348 - advanced_data_fetcher - WARNING - Low quality data for PENGU/USDT 4h: {'date_range_days': 222, 'min_required_days': 1460, 'actual_samples': 1338, 'expected_samples': 8760, 'completeness_ratio': 0.15273972602739727, 'gap_ratio': 0.0, 'avg_volume': np.float64(605001397.6390134), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:55:08,544 - advanced_data_fetcher - INFO - Cached data for PENGU/USDT 4h
2025-07-28 12:55:08,545 - advanced_data_fetcher - INFO - Fetching 1d data for PENGU/USDT
2025-07-28 12:55:09,002 - advanced_data_fetcher - WARNING - Low quality data for PENGU/USDT 1d: {'date_range_days': 223, 'min_required_days': 1460, 'actual_samples': 224, 'expected_samples': 1460, 'completeness_ratio': 0.15342465753424658, 'gap_ratio': 0.0, 'avg_volume': np.float64(3613803551.522321), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:55:09,081 - advanced_data_fetcher - INFO - Cached data for PENGU/USDT 1d
2025-07-28 12:55:09,081 - advanced_data_fetcher - INFO - Fetching 1h data for PEOPLE/USDT
2025-07-28 12:55:28,958 - advanced_data_fetcher - INFO - Cached data for PEOPLE/USDT 1h
2025-07-28 12:55:28,958 - advanced_data_fetcher - INFO - Fetching 4h data for PEOPLE/USDT
2025-07-28 12:55:33,981 - advanced_data_fetcher - INFO - Cached data for PEOPLE/USDT 4h
2025-07-28 12:55:33,981 - advanced_data_fetcher - INFO - Fetching 1d data for PEOPLE/USDT
2025-07-28 12:55:35,131 - advanced_data_fetcher - INFO - Cached data for PEOPLE/USDT 1d
2025-07-28 12:55:35,131 - advanced_data_fetcher - INFO - Fetching 1h data for PEPE/USDT
2025-07-28 12:55:45,416 - advanced_data_fetcher - WARNING - Low quality data for PEPE/USDT 1h: {'date_range_days': 814, 'min_required_days': 1460, 'actual_samples': 19552, 'expected_samples': 35040, 'completeness_ratio': 0.5579908675799087, 'gap_ratio': 0.0, 'avg_volume': np.float64(1348933314693.0212), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:55:47,815 - advanced_data_fetcher - INFO - Cached data for PEPE/USDT 1h
2025-07-28 12:55:47,815 - advanced_data_fetcher - INFO - Fetching 4h data for PEPE/USDT
2025-07-28 12:55:50,328 - advanced_data_fetcher - WARNING - Low quality data for PEPE/USDT 4h: {'date_range_days': 814, 'min_required_days': 1460, 'actual_samples': 4889, 'expected_samples': 8760, 'completeness_ratio': 0.5581050228310502, 'gap_ratio': 0.0, 'avg_volume': np.float64(5394629638556.686), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:55:50,950 - advanced_data_fetcher - INFO - Cached data for PEPE/USDT 4h
2025-07-28 12:55:50,950 - advanced_data_fetcher - INFO - Fetching 1d data for PEPE/USDT
2025-07-28 12:55:51,423 - advanced_data_fetcher - WARNING - Low quality data for PEPE/USDT 1d: {'date_range_days': 815, 'min_required_days': 1460, 'actual_samples': 816, 'expected_samples': 1460, 'completeness_ratio': 0.5589041095890411, 'gap_ratio': 0.0, 'avg_volume': np.float64(32321500375584.945), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:55:51,561 - advanced_data_fetcher - INFO - Cached data for PEPE/USDT 1d
2025-07-28 12:55:51,565 - advanced_data_fetcher - INFO - Fetching 1h data for PERP/USDT
2025-07-28 12:56:13,383 - advanced_data_fetcher - INFO - Cached data for PERP/USDT 1h
2025-07-28 12:56:13,383 - advanced_data_fetcher - INFO - Fetching 4h data for PERP/USDT
2025-07-28 12:56:21,350 - advanced_data_fetcher - INFO - Cached data for PERP/USDT 4h
2025-07-28 12:56:21,350 - advanced_data_fetcher - INFO - Fetching 1d data for PERP/USDT
2025-07-28 12:56:23,116 - advanced_data_fetcher - INFO - Cached data for PERP/USDT 1d
2025-07-28 12:56:23,116 - advanced_data_fetcher - INFO - Fetching 1h data for PHA/USDT
2025-07-28 12:56:47,050 - advanced_data_fetcher - INFO - Cached data for PHA/USDT 1h
2025-07-28 12:56:47,050 - advanced_data_fetcher - INFO - Fetching 4h data for PHA/USDT
2025-07-28 12:56:52,515 - advanced_data_fetcher - INFO - Cached data for PHA/USDT 4h
2025-07-28 12:56:52,515 - advanced_data_fetcher - INFO - Fetching 1d data for PHA/USDT
2025-07-28 12:56:54,131 - advanced_data_fetcher - INFO - Cached data for PHA/USDT 1d
2025-07-28 12:56:54,131 - advanced_data_fetcher - INFO - Fetching 1h data for PHB/USDT
2025-07-28 12:57:05,965 - advanced_data_fetcher - WARNING - Low quality data for PHB/USDT 1h: {'date_range_days': 983, 'min_required_days': 1460, 'actual_samples': 23592, 'expected_samples': 35040, 'completeness_ratio': 0.6732876712328767, 'gap_ratio': 4.238545331242318e-05, 'avg_volume': np.float64(207840.03122668705), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:57:08,839 - advanced_data_fetcher - INFO - Cached data for PHB/USDT 1h
2025-07-28 12:57:08,839 - advanced_data_fetcher - INFO - Fetching 4h data for PHB/USDT
2025-07-28 12:57:11,857 - advanced_data_fetcher - WARNING - Low quality data for PHB/USDT 4h: {'date_range_days': 983, 'min_required_days': 1460, 'actual_samples': 5899, 'expected_samples': 8760, 'completeness_ratio': 0.6734018264840183, 'gap_ratio': 0.0, 'avg_volume': np.float64(831219.1925241566), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:57:12,572 - advanced_data_fetcher - INFO - Cached data for PHB/USDT 4h
2025-07-28 12:57:12,572 - advanced_data_fetcher - INFO - Fetching 1d data for PHB/USDT
2025-07-28 12:57:13,075 - advanced_data_fetcher - WARNING - Low quality data for PHB/USDT 1d: {'date_range_days': 983, 'min_required_days': 1460, 'actual_samples': 984, 'expected_samples': 1460, 'completeness_ratio': 0.673972602739726, 'gap_ratio': 0.0, 'avg_volume': np.float64(4983091.480386179), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:57:13,215 - advanced_data_fetcher - INFO - Cached data for PHB/USDT 1d
2025-07-28 12:57:13,215 - advanced_data_fetcher - INFO - Fetching 1h data for PIVX/USDT
2025-07-28 12:57:20,366 - advanced_data_fetcher - WARNING - Low quality data for PIVX/USDT 1h: {'date_range_days': 620, 'min_required_days': 1460, 'actual_samples': 14882, 'expected_samples': 35040, 'completeness_ratio': 0.4247146118721461, 'gap_ratio': 0.0, 'avg_volume': np.float64(200130.22093804597), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:57:22,166 - advanced_data_fetcher - INFO - Cached data for PIVX/USDT 1h
2025-07-28 12:57:22,166 - advanced_data_fetcher - INFO - Fetching 4h data for PIVX/USDT
2025-07-28 12:57:24,050 - advanced_data_fetcher - WARNING - Low quality data for PIVX/USDT 4h: {'date_range_days': 620, 'min_required_days': 1460, 'actual_samples': 3721, 'expected_samples': 8760, 'completeness_ratio': 0.4247716894977169, 'gap_ratio': 0.0, 'avg_volume': np.float64(800413.3410373556), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:57:24,581 - advanced_data_fetcher - INFO - Cached data for PIVX/USDT 4h
2025-07-28 12:57:24,581 - advanced_data_fetcher - INFO - Fetching 1d data for PIVX/USDT
2025-07-28 12:57:25,050 - advanced_data_fetcher - WARNING - Low quality data for PIVX/USDT 1d: {'date_range_days': 620, 'min_required_days': 1460, 'actual_samples': 621, 'expected_samples': 1460, 'completeness_ratio': 0.42534246575342466, 'gap_ratio': 0.0, 'avg_volume': np.float64(4796035.494363929), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:57:25,181 - advanced_data_fetcher - INFO - Cached data for PIVX/USDT 1d
2025-07-28 12:57:25,181 - advanced_data_fetcher - INFO - Fetching 1h data for PIXEL/USDT
2025-07-28 12:57:32,647 - advanced_data_fetcher - WARNING - Low quality data for PIXEL/USDT 1h: {'date_range_days': 524, 'min_required_days': 1460, 'actual_samples': 12600, 'expected_samples': 35040, 'completeness_ratio': 0.3595890410958904, 'gap_ratio': 0.0, 'avg_volume': np.float64(3754543.676492064), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:57:34,250 - advanced_data_fetcher - INFO - Cached data for PIXEL/USDT 1h
2025-07-28 12:57:34,250 - advanced_data_fetcher - INFO - Fetching 4h data for PIXEL/USDT
2025-07-28 12:57:36,465 - advanced_data_fetcher - WARNING - Low quality data for PIXEL/USDT 4h: {'date_range_days': 525, 'min_required_days': 1460, 'actual_samples': 3151, 'expected_samples': 8760, 'completeness_ratio': 0.35970319634703196, 'gap_ratio': 0.0, 'avg_volume': np.float64(15013408.74354173), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:57:36,893 - advanced_data_fetcher - INFO - Cached data for PIXEL/USDT 4h
2025-07-28 12:57:36,894 - advanced_data_fetcher - INFO - Fetching 1d data for PIXEL/USDT
2025-07-28 12:57:37,350 - advanced_data_fetcher - WARNING - Low quality data for PIXEL/USDT 1d: {'date_range_days': 525, 'min_required_days': 1460, 'actual_samples': 526, 'expected_samples': 1460, 'completeness_ratio': 0.36027397260273974, 'gap_ratio': 0.0, 'avg_volume': np.float64(89937739.45038022), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:57:37,450 - advanced_data_fetcher - INFO - Cached data for PIXEL/USDT 1d
2025-07-28 12:57:37,450 - advanced_data_fetcher - INFO - Fetching 1h data for PNUT/USDT
2025-07-28 12:57:41,131 - advanced_data_fetcher - WARNING - Low quality data for PNUT/USDT 1h: {'date_range_days': 258, 'min_required_days': 1460, 'actual_samples': 6216, 'expected_samples': 35040, 'completeness_ratio': 0.1773972602739726, 'gap_ratio': 0.0, 'avg_volume': np.float64(10558350.437226513), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:57:41,931 - advanced_data_fetcher - INFO - Cached data for PNUT/USDT 1h
2025-07-28 12:57:41,931 - advanced_data_fetcher - INFO - Fetching 4h data for PNUT/USDT
2025-07-28 12:57:42,915 - advanced_data_fetcher - WARNING - Low quality data for PNUT/USDT 4h: {'date_range_days': 259, 'min_required_days': 1460, 'actual_samples': 1555, 'expected_samples': 8760, 'completeness_ratio': 0.17751141552511415, 'gap_ratio': 0.0, 'avg_volume': np.float64(42206242.128810294), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:57:43,150 - advanced_data_fetcher - INFO - Cached data for PNUT/USDT 4h
2025-07-28 12:57:43,165 - advanced_data_fetcher - INFO - Fetching 1d data for PNUT/USDT
2025-07-28 12:57:43,603 - advanced_data_fetcher - WARNING - Low quality data for PNUT/USDT 1d: {'date_range_days': 259, 'min_required_days': 1460, 'actual_samples': 260, 'expected_samples': 1460, 'completeness_ratio': 0.1780821917808219, 'gap_ratio': 0.0, 'avg_volume': np.float64(252425794.27038464), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:57:43,691 - advanced_data_fetcher - INFO - Cached data for PNUT/USDT 1d
2025-07-28 12:57:43,691 - advanced_data_fetcher - INFO - Fetching 1h data for POL/USDT
2025-07-28 12:57:47,516 - advanced_data_fetcher - WARNING - Low quality data for POL/USDT 1h: {'date_range_days': 317, 'min_required_days': 1460, 'actual_samples': 7632, 'expected_samples': 35040, 'completeness_ratio': 0.21780821917808219, 'gap_ratio': 0.0, 'avg_volume': np.float64(2491393.5942610065), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:57:48,481 - advanced_data_fetcher - INFO - Cached data for POL/USDT 1h
2025-07-28 12:57:48,481 - advanced_data_fetcher - INFO - Fetching 4h data for POL/USDT
2025-07-28 12:57:49,450 - advanced_data_fetcher - WARNING - Low quality data for POL/USDT 4h: {'date_range_days': 318, 'min_required_days': 1460, 'actual_samples': 1909, 'expected_samples': 8760, 'completeness_ratio': 0.21792237442922374, 'gap_ratio': 0.0, 'avg_volume': np.float64(9960354.065688843), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:57:49,725 - advanced_data_fetcher - INFO - Cached data for POL/USDT 4h
2025-07-28 12:57:49,725 - advanced_data_fetcher - INFO - Fetching 1d data for POL/USDT
2025-07-28 12:57:50,194 - advanced_data_fetcher - WARNING - Low quality data for POL/USDT 1d: {'date_range_days': 318, 'min_required_days': 1460, 'actual_samples': 319, 'expected_samples': 1460, 'completeness_ratio': 0.2184931506849315, 'gap_ratio': 0.0, 'avg_volume': np.float64(59606005.991849534), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:57:50,265 - advanced_data_fetcher - INFO - Cached data for POL/USDT 1d
2025-07-28 12:57:50,265 - advanced_data_fetcher - INFO - Fetching 1h data for POLYX/USDT
2025-07-28 12:58:03,065 - advanced_data_fetcher - WARNING - Low quality data for POLYX/USDT 1h: {'date_range_days': 1015, 'min_required_days': 1460, 'actual_samples': 24361, 'expected_samples': 35040, 'completeness_ratio': 0.6952340182648402, 'gap_ratio': 4.10475330432641e-05, 'avg_volume': np.float64(1092047.976117565), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:58:06,115 - advanced_data_fetcher - INFO - Cached data for POLYX/USDT 1h
2025-07-28 12:58:06,115 - advanced_data_fetcher - INFO - Fetching 4h data for POLYX/USDT
2025-07-28 12:58:09,850 - advanced_data_fetcher - WARNING - Low quality data for POLYX/USDT 4h: {'date_range_days': 1015, 'min_required_days': 1460, 'actual_samples': 6091, 'expected_samples': 8760, 'completeness_ratio': 0.6953196347031964, 'gap_ratio': 0.0, 'avg_volume': np.float64(4367654.224577244), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:58:10,600 - advanced_data_fetcher - INFO - Cached data for POLYX/USDT 4h
2025-07-28 12:58:10,600 - advanced_data_fetcher - INFO - Fetching 1d data for POLYX/USDT
2025-07-28 12:58:11,515 - advanced_data_fetcher - WARNING - Low quality data for POLYX/USDT 1d: {'date_range_days': 1015, 'min_required_days': 1460, 'actual_samples': 1016, 'expected_samples': 1460, 'completeness_ratio': 0.6958904109589041, 'gap_ratio': 0.0, 'avg_volume': np.float64(26184430.98612205), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:58:11,700 - advanced_data_fetcher - INFO - Cached data for POLYX/USDT 1d
2025-07-28 12:58:11,700 - advanced_data_fetcher - INFO - Fetching 1h data for POND/USDT
2025-07-28 12:58:36,491 - advanced_data_fetcher - INFO - Cached data for POND/USDT 1h
2025-07-28 12:58:36,491 - advanced_data_fetcher - INFO - Fetching 4h data for POND/USDT
2025-07-28 12:58:42,570 - advanced_data_fetcher - INFO - Cached data for POND/USDT 4h
2025-07-28 12:58:42,571 - advanced_data_fetcher - INFO - Fetching 1d data for POND/USDT
2025-07-28 12:58:43,743 - advanced_data_fetcher - INFO - Cached data for POND/USDT 1d
2025-07-28 12:58:43,743 - advanced_data_fetcher - INFO - Fetching 1h data for PORTAL/USDT
2025-07-28 12:58:50,600 - advanced_data_fetcher - WARNING - Low quality data for PORTAL/USDT 1h: {'date_range_days': 514, 'min_required_days': 1460, 'actual_samples': 12360, 'expected_samples': 35040, 'completeness_ratio': 0.3527397260273973, 'gap_ratio': 0.0, 'avg_volume': np.float64(1438202.7742394821), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:58:52,068 - advanced_data_fetcher - INFO - Cached data for PORTAL/USDT 1h
2025-07-28 12:58:52,068 - advanced_data_fetcher - INFO - Fetching 4h data for PORTAL/USDT
2025-07-28 12:58:54,200 - advanced_data_fetcher - WARNING - Low quality data for PORTAL/USDT 4h: {'date_range_days': 515, 'min_required_days': 1460, 'actual_samples': 3091, 'expected_samples': 8760, 'completeness_ratio': 0.35285388127853884, 'gap_ratio': 0.0, 'avg_volume': np.float64(5750955.468327401), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:58:54,666 - advanced_data_fetcher - INFO - Cached data for PORTAL/USDT 4h
2025-07-28 12:58:54,666 - advanced_data_fetcher - INFO - Fetching 1d data for PORTAL/USDT
2025-07-28 12:58:55,131 - advanced_data_fetcher - WARNING - Low quality data for PORTAL/USDT 1d: {'date_range_days': 515, 'min_required_days': 1460, 'actual_samples': 516, 'expected_samples': 1460, 'completeness_ratio': 0.35342465753424657, 'gap_ratio': 0.0, 'avg_volume': np.float64(34450090.01996124), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 12:58:55,265 - advanced_data_fetcher - INFO - Cached data for PORTAL/USDT 1d
2025-07-28 12:58:55,265 - advanced_data_fetcher - INFO - Fetching 1h data for PORTO/USDT
2025-07-28 12:59:16,725 - advanced_data_fetcher - INFO - Cached data for PORTO/USDT 1h
2025-07-28 12:59:16,725 - advanced_data_fetcher - INFO - Fetching 4h data for PORTO/USDT
2025-07-28 12:59:22,053 - advanced_data_fetcher - INFO - Cached data for PORTO/USDT 4h
2025-07-28 12:59:22,053 - advanced_data_fetcher - INFO - Fetching 1d data for PORTO/USDT
2025-07-28 12:59:23,360 - advanced_data_fetcher - INFO - Cached data for PORTO/USDT 1d
2025-07-28 12:59:23,360 - advanced_data_fetcher - INFO - Fetching 1h data for POWR/USDT
2025-07-28 12:59:43,650 - advanced_data_fetcher - INFO - Cached data for POWR/USDT 1h
2025-07-28 12:59:43,650 - advanced_data_fetcher - INFO - Fetching 4h data for POWR/USDT
2025-07-28 12:59:49,065 - advanced_data_fetcher - INFO - Cached data for POWR/USDT 4h
2025-07-28 12:59:49,065 - advanced_data_fetcher - INFO - Fetching 1d data for POWR/USDT
2025-07-28 12:59:50,200 - advanced_data_fetcher - INFO - Cached data for POWR/USDT 1d
2025-07-28 12:59:50,200 - advanced_data_fetcher - INFO - Fetching 1h data for PROM/USDT
2025-07-28 13:00:00,966 - advanced_data_fetcher - WARNING - Low quality data for PROM/USDT 1h: {'date_range_days': 864, 'min_required_days': 1460, 'actual_samples': 20737, 'expected_samples': 35040, 'completeness_ratio': 0.5918093607305936, 'gap_ratio': 4.8220657729771434e-05, 'avg_volume': np.float64(12304.373973091577), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:00:03,500 - advanced_data_fetcher - INFO - Cached data for PROM/USDT 1h
2025-07-28 13:00:03,500 - advanced_data_fetcher - INFO - Fetching 4h data for PROM/USDT
2025-07-28 13:00:06,362 - advanced_data_fetcher - WARNING - Low quality data for PROM/USDT 4h: {'date_range_days': 864, 'min_required_days': 1460, 'actual_samples': 5185, 'expected_samples': 8760, 'completeness_ratio': 0.5918949771689498, 'gap_ratio': 0.0, 'avg_volume': np.float64(49210.41989392479), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:00:07,015 - advanced_data_fetcher - INFO - Cached data for PROM/USDT 4h
2025-07-28 13:00:07,015 - advanced_data_fetcher - INFO - Fetching 1d data for PROM/USDT
2025-07-28 13:00:07,515 - advanced_data_fetcher - WARNING - Low quality data for PROM/USDT 1d: {'date_range_days': 864, 'min_required_days': 1460, 'actual_samples': 865, 'expected_samples': 1460, 'completeness_ratio': 0.5924657534246576, 'gap_ratio': 0.0, 'avg_volume': np.float64(294978.06606936414), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:00:07,650 - advanced_data_fetcher - INFO - Cached data for PROM/USDT 1d
2025-07-28 13:00:07,650 - advanced_data_fetcher - INFO - Fetching 1h data for PSG/USDT
2025-07-28 13:00:29,725 - advanced_data_fetcher - INFO - Cached data for PSG/USDT 1h
2025-07-28 13:00:29,725 - advanced_data_fetcher - INFO - Fetching 4h data for PSG/USDT
2025-07-28 13:00:35,281 - advanced_data_fetcher - INFO - Cached data for PSG/USDT 4h
2025-07-28 13:00:35,281 - advanced_data_fetcher - INFO - Fetching 1d data for PSG/USDT
2025-07-28 13:00:36,575 - advanced_data_fetcher - INFO - Cached data for PSG/USDT 1d
2025-07-28 13:00:36,576 - advanced_data_fetcher - INFO - Fetching 1h data for PUNDIX/USDT
2025-07-28 13:00:58,686 - advanced_data_fetcher - INFO - Cached data for PUNDIX/USDT 1h
2025-07-28 13:00:58,686 - advanced_data_fetcher - INFO - Fetching 4h data for PUNDIX/USDT
2025-07-28 13:01:04,215 - advanced_data_fetcher - INFO - Cached data for PUNDIX/USDT 4h
2025-07-28 13:01:04,215 - advanced_data_fetcher - INFO - Fetching 1d data for PUNDIX/USDT
2025-07-28 13:01:05,400 - advanced_data_fetcher - INFO - Cached data for PUNDIX/USDT 1d
2025-07-28 13:01:05,415 - advanced_data_fetcher - INFO - Fetching 1h data for PYR/USDT
2025-07-28 13:01:25,844 - advanced_data_fetcher - INFO - Cached data for PYR/USDT 1h
2025-07-28 13:01:25,845 - advanced_data_fetcher - INFO - Fetching 4h data for PYR/USDT
2025-07-28 13:01:31,181 - advanced_data_fetcher - INFO - Cached data for PYR/USDT 4h
2025-07-28 13:01:31,181 - advanced_data_fetcher - INFO - Fetching 1d data for PYR/USDT
2025-07-28 13:01:32,342 - advanced_data_fetcher - INFO - Cached data for PYR/USDT 1d
2025-07-28 13:01:32,342 - advanced_data_fetcher - INFO - Fetching 1h data for PYTH/USDT
2025-07-28 13:01:39,169 - advanced_data_fetcher - WARNING - Low quality data for PYTH/USDT 1h: {'date_range_days': 541, 'min_required_days': 1460, 'actual_samples': 13007, 'expected_samples': 35040, 'completeness_ratio': 0.3712043378995434, 'gap_ratio': 0.0, 'avg_volume': np.float64(1704624.9528869072), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:01:40,931 - advanced_data_fetcher - INFO - Cached data for PYTH/USDT 1h
2025-07-28 13:01:40,931 - advanced_data_fetcher - INFO - Fetching 4h data for PYTH/USDT
2025-07-28 13:01:42,881 - advanced_data_fetcher - WARNING - Low quality data for PYTH/USDT 4h: {'date_range_days': 541, 'min_required_days': 1460, 'actual_samples': 3252, 'expected_samples': 8760, 'completeness_ratio': 0.37123287671232874, 'gap_ratio': 0.0, 'avg_volume': np.float64(6817975.634132842), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:01:43,350 - advanced_data_fetcher - INFO - Cached data for PYTH/USDT 4h
2025-07-28 13:01:43,350 - advanced_data_fetcher - INFO - Fetching 1d data for PYTH/USDT
2025-07-28 13:01:43,831 - advanced_data_fetcher - WARNING - Low quality data for PYTH/USDT 1d: {'date_range_days': 542, 'min_required_days': 1460, 'actual_samples': 543, 'expected_samples': 1460, 'completeness_ratio': 0.3719178082191781, 'gap_ratio': 0.0, 'avg_volume': np.float64(40832517.05745857), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:01:43,931 - advanced_data_fetcher - INFO - Cached data for PYTH/USDT 1d
2025-07-28 13:01:43,931 - advanced_data_fetcher - INFO - Fetching 1h data for QI/USDT
2025-07-28 13:02:04,040 - advanced_data_fetcher - INFO - Cached data for QI/USDT 1h
2025-07-28 13:02:04,040 - advanced_data_fetcher - INFO - Fetching 4h data for QI/USDT
2025-07-28 13:02:09,450 - advanced_data_fetcher - INFO - Cached data for QI/USDT 4h
2025-07-28 13:02:09,450 - advanced_data_fetcher - INFO - Fetching 1d data for QI/USDT
2025-07-28 13:02:10,613 - advanced_data_fetcher - INFO - Cached data for QI/USDT 1d
2025-07-28 13:02:10,614 - advanced_data_fetcher - INFO - Fetching 1h data for QKC/USDT
2025-07-28 13:02:20,950 - advanced_data_fetcher - WARNING - Low quality data for QKC/USDT 1h: {'date_range_days': 864, 'min_required_days': 1460, 'actual_samples': 20738, 'expected_samples': 35040, 'completeness_ratio': 0.591837899543379, 'gap_ratio': 4.821833261005834e-05, 'avg_volume': np.float64(5616244.907271675), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:02:23,581 - advanced_data_fetcher - INFO - Cached data for QKC/USDT 1h
2025-07-28 13:02:23,581 - advanced_data_fetcher - INFO - Fetching 4h data for QKC/USDT
2025-07-28 13:02:26,850 - advanced_data_fetcher - WARNING - Low quality data for QKC/USDT 4h: {'date_range_days': 864, 'min_required_days': 1460, 'actual_samples': 5185, 'expected_samples': 8760, 'completeness_ratio': 0.5918949771689498, 'gap_ratio': 0.0, 'avg_volume': np.float64(22462813.551976856), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:02:27,565 - advanced_data_fetcher - INFO - Cached data for QKC/USDT 4h
2025-07-28 13:02:27,565 - advanced_data_fetcher - INFO - Fetching 1d data for QKC/USDT
2025-07-28 13:02:28,081 - advanced_data_fetcher - WARNING - Low quality data for QKC/USDT 1d: {'date_range_days': 864, 'min_required_days': 1460, 'actual_samples': 865, 'expected_samples': 1460, 'completeness_ratio': 0.5924657534246576, 'gap_ratio': 0.0, 'avg_volume': np.float64(134647038.45895955), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:02:28,250 - advanced_data_fetcher - INFO - Cached data for QKC/USDT 1d
2025-07-28 13:02:28,250 - advanced_data_fetcher - INFO - Fetching 1h data for QNT/USDT
2025-07-28 13:02:51,126 - advanced_data_fetcher - INFO - Cached data for QNT/USDT 1h
2025-07-28 13:02:51,127 - advanced_data_fetcher - INFO - Fetching 4h data for QNT/USDT
2025-07-28 13:02:56,571 - advanced_data_fetcher - INFO - Cached data for QNT/USDT 4h
2025-07-28 13:02:56,572 - advanced_data_fetcher - INFO - Fetching 1d data for QNT/USDT
2025-07-28 13:02:57,684 - advanced_data_fetcher - INFO - Cached data for QNT/USDT 1d
2025-07-28 13:02:57,684 - advanced_data_fetcher - INFO - Fetching 1h data for QTUM/USDT
2025-07-28 13:03:21,515 - advanced_data_fetcher - INFO - Cached data for QTUM/USDT 1h
2025-07-28 13:03:21,515 - advanced_data_fetcher - INFO - Fetching 4h data for QTUM/USDT
2025-07-28 13:03:27,724 - advanced_data_fetcher - INFO - Cached data for QTUM/USDT 4h
2025-07-28 13:03:27,724 - advanced_data_fetcher - INFO - Fetching 1d data for QTUM/USDT
2025-07-28 13:03:28,903 - advanced_data_fetcher - INFO - Cached data for QTUM/USDT 1d
2025-07-28 13:03:28,903 - advanced_data_fetcher - INFO - Fetching 1h data for QUICK/USDT
2025-07-28 13:03:59,734 - advanced_data_fetcher - INFO - Cached data for QUICK/USDT 1h
2025-07-28 13:03:59,735 - advanced_data_fetcher - INFO - Fetching 4h data for QUICK/USDT
2025-07-28 13:04:05,382 - advanced_data_fetcher - INFO - Cached data for QUICK/USDT 4h
2025-07-28 13:04:05,385 - advanced_data_fetcher - INFO - Fetching 1d data for QUICK/USDT
2025-07-28 13:04:07,157 - advanced_data_fetcher - INFO - Cached data for QUICK/USDT 1d
2025-07-28 13:04:07,157 - advanced_data_fetcher - INFO - Fetching 1h data for RAD/USDT
2025-07-28 13:04:28,960 - advanced_data_fetcher - INFO - Cached data for RAD/USDT 1h
2025-07-28 13:04:28,960 - advanced_data_fetcher - INFO - Fetching 4h data for RAD/USDT
2025-07-28 13:04:34,422 - advanced_data_fetcher - INFO - Cached data for RAD/USDT 4h
2025-07-28 13:04:34,422 - advanced_data_fetcher - INFO - Fetching 1d data for RAD/USDT
2025-07-28 13:04:35,621 - advanced_data_fetcher - INFO - Cached data for RAD/USDT 1d
2025-07-28 13:04:35,622 - advanced_data_fetcher - INFO - Fetching 1h data for RARE/USDT
2025-07-28 13:04:57,895 - advanced_data_fetcher - INFO - Cached data for RARE/USDT 1h
2025-07-28 13:04:57,895 - advanced_data_fetcher - INFO - Fetching 4h data for RARE/USDT
2025-07-28 13:05:03,608 - advanced_data_fetcher - INFO - Cached data for RARE/USDT 4h
2025-07-28 13:05:03,609 - advanced_data_fetcher - INFO - Fetching 1d data for RARE/USDT
2025-07-28 13:05:05,020 - advanced_data_fetcher - INFO - Cached data for RARE/USDT 1d
2025-07-28 13:05:05,020 - advanced_data_fetcher - INFO - Fetching 1h data for RAY/USDT
2025-07-28 13:05:26,402 - advanced_data_fetcher - INFO - Cached data for RAY/USDT 1h
2025-07-28 13:05:26,402 - advanced_data_fetcher - INFO - Fetching 4h data for RAY/USDT
2025-07-28 13:05:31,935 - advanced_data_fetcher - INFO - Cached data for RAY/USDT 4h
2025-07-28 13:05:31,935 - advanced_data_fetcher - INFO - Fetching 1d data for RAY/USDT
2025-07-28 13:05:33,121 - advanced_data_fetcher - INFO - Cached data for RAY/USDT 1d
2025-07-28 13:05:33,122 - advanced_data_fetcher - INFO - Fetching 1h data for RDNT/USDT
2025-07-28 13:05:43,385 - advanced_data_fetcher - WARNING - Low quality data for RDNT/USDT 1h: {'date_range_days': 851, 'min_required_days': 1460, 'actual_samples': 20428, 'expected_samples': 35040, 'completeness_ratio': 0.5829908675799087, 'gap_ratio': 0.0, 'avg_volume': np.float64(2418071.2996867043), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:05:45,968 - advanced_data_fetcher - INFO - Cached data for RDNT/USDT 1h
2025-07-28 13:05:45,968 - advanced_data_fetcher - INFO - Fetching 4h data for RDNT/USDT
2025-07-28 13:05:48,866 - advanced_data_fetcher - WARNING - Low quality data for RDNT/USDT 4h: {'date_range_days': 851, 'min_required_days': 1460, 'actual_samples': 5108, 'expected_samples': 8760, 'completeness_ratio': 0.5831050228310503, 'gap_ratio': 0.0, 'avg_volume': np.float64(9670391.642521534), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:05:49,635 - advanced_data_fetcher - INFO - Cached data for RDNT/USDT 4h
2025-07-28 13:05:49,635 - advanced_data_fetcher - INFO - Fetching 1d data for RDNT/USDT
2025-07-28 13:05:50,153 - advanced_data_fetcher - WARNING - Low quality data for RDNT/USDT 1d: {'date_range_days': 851, 'min_required_days': 1460, 'actual_samples': 852, 'expected_samples': 1460, 'completeness_ratio': 0.5835616438356165, 'gap_ratio': 0.0, 'avg_volume': np.float64(57976948.95539906), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:05:50,323 - advanced_data_fetcher - INFO - Cached data for RDNT/USDT 1d
2025-07-28 13:05:50,324 - advanced_data_fetcher - INFO - Fetching 1h data for RED/USDT
2025-07-28 13:05:52,356 - advanced_data_fetcher - WARNING - Low quality data for RED/USDT 1h: {'date_range_days': 150, 'min_required_days': 1460, 'actual_samples': 3594, 'expected_samples': 35040, 'completeness_ratio': 0.10256849315068493, 'gap_ratio': 0.0019439044709802832, 'avg_volume': np.float64(594897.6838063439), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:05:52,909 - advanced_data_fetcher - INFO - Cached data for RED/USDT 1h
2025-07-28 13:05:52,909 - advanced_data_fetcher - INFO - Fetching 4h data for RED/USDT
2025-07-28 13:05:53,381 - advanced_data_fetcher - WARNING - Low quality data for RED/USDT 4h: {'date_range_days': 150, 'min_required_days': 1460, 'actual_samples': 900, 'expected_samples': 8760, 'completeness_ratio': 0.10273972602739725, 'gap_ratio': 0.0011098779134295228, 'avg_volume': np.float64(2375624.7506666663), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:05:53,535 - advanced_data_fetcher - INFO - Cached data for RED/USDT 4h
2025-07-28 13:05:53,535 - advanced_data_fetcher - INFO - Fetching 1d data for RED/USDT
2025-07-28 13:05:53,983 - advanced_data_fetcher - WARNING - Low quality data for RED/USDT 1d: {'date_range_days': 150, 'min_required_days': 1460, 'actual_samples': 151, 'expected_samples': 1460, 'completeness_ratio': 0.10342465753424658, 'gap_ratio': 0.0, 'avg_volume': np.float64(14159352.818543045), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:05:54,036 - advanced_data_fetcher - INFO - Cached data for RED/USDT 1d
2025-07-28 13:05:54,036 - advanced_data_fetcher - INFO - Fetching 1h data for REI/USDT
2025-07-28 13:06:11,874 - advanced_data_fetcher - INFO - Cached data for REI/USDT 1h
2025-07-28 13:06:11,875 - advanced_data_fetcher - INFO - Fetching 4h data for REI/USDT
2025-07-28 13:06:16,706 - advanced_data_fetcher - INFO - Cached data for REI/USDT 4h
2025-07-28 13:06:16,707 - advanced_data_fetcher - INFO - Fetching 1d data for REI/USDT
2025-07-28 13:06:17,832 - advanced_data_fetcher - INFO - Cached data for REI/USDT 1d
2025-07-28 13:06:17,833 - advanced_data_fetcher - INFO - Fetching 1h data for RENDER/USDT
2025-07-28 13:06:22,231 - advanced_data_fetcher - WARNING - Low quality data for RENDER/USDT 1h: {'date_range_days': 367, 'min_required_days': 1460, 'actual_samples': 8811, 'expected_samples': 35040, 'completeness_ratio': 0.2514554794520548, 'gap_ratio': 0.0, 'avg_volume': np.float64(211144.46660991941), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:06:23,331 - advanced_data_fetcher - INFO - Cached data for RENDER/USDT 1h
2025-07-28 13:06:23,331 - advanced_data_fetcher - INFO - Fetching 4h data for RENDER/USDT
2025-07-28 13:06:24,788 - advanced_data_fetcher - WARNING - Low quality data for RENDER/USDT 4h: {'date_range_days': 367, 'min_required_days': 1460, 'actual_samples': 2203, 'expected_samples': 8760, 'completeness_ratio': 0.2514840182648402, 'gap_ratio': 0.0, 'avg_volume': np.float64(844482.0223785746), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:06:25,100 - advanced_data_fetcher - INFO - Cached data for RENDER/USDT 4h
2025-07-28 13:06:25,100 - advanced_data_fetcher - INFO - Fetching 1d data for RENDER/USDT
2025-07-28 13:06:25,565 - advanced_data_fetcher - WARNING - Low quality data for RENDER/USDT 1d: {'date_range_days': 367, 'min_required_days': 1460, 'actual_samples': 368, 'expected_samples': 1460, 'completeness_ratio': 0.25205479452054796, 'gap_ratio': 0.0, 'avg_volume': np.float64(5055418.19375), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:06:25,650 - advanced_data_fetcher - INFO - Cached data for RENDER/USDT 1d
2025-07-28 13:06:25,650 - advanced_data_fetcher - INFO - Fetching 1h data for REQ/USDT
2025-07-28 13:06:47,187 - advanced_data_fetcher - INFO - Cached data for REQ/USDT 1h
2025-07-28 13:06:47,187 - advanced_data_fetcher - INFO - Fetching 4h data for REQ/USDT
2025-07-28 13:06:52,938 - advanced_data_fetcher - INFO - Cached data for REQ/USDT 4h
2025-07-28 13:06:52,938 - advanced_data_fetcher - INFO - Fetching 1d data for REQ/USDT
2025-07-28 13:06:54,645 - advanced_data_fetcher - INFO - Cached data for REQ/USDT 1d
2025-07-28 13:06:54,645 - advanced_data_fetcher - INFO - Fetching 1h data for RESOLV/USDT
2025-07-28 13:06:57,182 - advanced_data_fetcher - WARNING - Low quality data for RESOLV/USDT 1h: {'date_range_days': 46, 'min_required_days': 1460, 'actual_samples': 1125, 'expected_samples': 35040, 'completeness_ratio': 0.03210616438356165, 'gap_ratio': 0.0, 'avg_volume': np.float64(3294945.5392888887), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:06:57,386 - advanced_data_fetcher - INFO - Cached data for RESOLV/USDT 1h
2025-07-28 13:06:57,386 - advanced_data_fetcher - INFO - Fetching 4h data for RESOLV/USDT
2025-07-28 13:06:58,230 - advanced_data_fetcher - WARNING - Low quality data for RESOLV/USDT 4h: {'date_range_days': 46, 'min_required_days': 1460, 'actual_samples': 282, 'expected_samples': 8760, 'completeness_ratio': 0.03219178082191781, 'gap_ratio': 0.0, 'avg_volume': np.float64(13144729.545035461), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:06:58,354 - advanced_data_fetcher - INFO - Cached data for RESOLV/USDT 4h
2025-07-28 13:06:58,355 - advanced_data_fetcher - INFO - Fetching 1d data for RESOLV/USDT
2025-07-28 13:06:59,096 - advanced_data_fetcher - WARNING - Low quality data for RESOLV/USDT 1d: {'date_range_days': 47, 'min_required_days': 1460, 'actual_samples': 48, 'expected_samples': 1460, 'completeness_ratio': 0.03287671232876712, 'gap_ratio': 0.0, 'avg_volume': np.float64(77225286.07708333), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:06:59,144 - advanced_data_fetcher - INFO - Cached data for RESOLV/USDT 1d
2025-07-28 13:06:59,144 - advanced_data_fetcher - INFO - Fetching 1h data for REZ/USDT
2025-07-28 13:07:06,000 - advanced_data_fetcher - WARNING - Low quality data for REZ/USDT 1h: {'date_range_days': 453, 'min_required_days': 1460, 'actual_samples': 10895, 'expected_samples': 35040, 'completeness_ratio': 0.3109303652968037, 'gap_ratio': 0.0, 'avg_volume': np.float64(11306918.074557137), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:07:07,494 - advanced_data_fetcher - INFO - Cached data for REZ/USDT 1h
2025-07-28 13:07:07,494 - advanced_data_fetcher - INFO - Fetching 4h data for REZ/USDT
2025-07-28 13:07:08,931 - advanced_data_fetcher - WARNING - Low quality data for REZ/USDT 4h: {'date_range_days': 453, 'min_required_days': 1460, 'actual_samples': 2724, 'expected_samples': 8760, 'completeness_ratio': 0.31095890410958904, 'gap_ratio': 0.0, 'avg_volume': np.float64(45223521.4472467), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:07:09,300 - advanced_data_fetcher - INFO - Cached data for REZ/USDT 4h
2025-07-28 13:07:09,300 - advanced_data_fetcher - INFO - Fetching 1d data for REZ/USDT
2025-07-28 13:07:09,800 - advanced_data_fetcher - WARNING - Low quality data for REZ/USDT 1d: {'date_range_days': 454, 'min_required_days': 1460, 'actual_samples': 455, 'expected_samples': 1460, 'completeness_ratio': 0.3116438356164384, 'gap_ratio': 0.0, 'avg_volume': np.float64(270744774.5545055), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:07:09,900 - advanced_data_fetcher - INFO - Cached data for REZ/USDT 1d
2025-07-28 13:07:09,915 - advanced_data_fetcher - INFO - Fetching 1h data for RIF/USDT
2025-07-28 13:07:32,816 - advanced_data_fetcher - INFO - Cached data for RIF/USDT 1h
2025-07-28 13:07:32,816 - advanced_data_fetcher - INFO - Fetching 4h data for RIF/USDT
2025-07-28 13:07:38,591 - advanced_data_fetcher - INFO - Cached data for RIF/USDT 4h
2025-07-28 13:07:38,592 - advanced_data_fetcher - INFO - Fetching 1d data for RIF/USDT
2025-07-28 13:07:39,790 - advanced_data_fetcher - INFO - Cached data for RIF/USDT 1d
2025-07-28 13:07:39,791 - advanced_data_fetcher - INFO - Fetching 1h data for RLC/USDT
2025-07-28 13:08:03,500 - advanced_data_fetcher - INFO - Cached data for RLC/USDT 1h
2025-07-28 13:08:03,500 - advanced_data_fetcher - INFO - Fetching 4h data for RLC/USDT
2025-07-28 13:08:08,950 - advanced_data_fetcher - INFO - Cached data for RLC/USDT 4h
2025-07-28 13:08:08,950 - advanced_data_fetcher - INFO - Fetching 1d data for RLC/USDT
2025-07-28 13:08:10,100 - advanced_data_fetcher - INFO - Cached data for RLC/USDT 1d
2025-07-28 13:08:10,100 - advanced_data_fetcher - INFO - Fetching 1h data for RONIN/USDT
2025-07-28 13:08:17,617 - advanced_data_fetcher - WARNING - Low quality data for RONIN/USDT 1h: {'date_range_days': 538, 'min_required_days': 1460, 'actual_samples': 12934, 'expected_samples': 35040, 'completeness_ratio': 0.36912100456621005, 'gap_ratio': 0.0, 'avg_volume': np.float64(182052.719251585), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:08:19,281 - advanced_data_fetcher - INFO - Cached data for RONIN/USDT 1h
2025-07-28 13:08:19,281 - advanced_data_fetcher - INFO - Fetching 4h data for RONIN/USDT
2025-07-28 13:08:21,165 - advanced_data_fetcher - WARNING - Low quality data for RONIN/USDT 4h: {'date_range_days': 538, 'min_required_days': 1460, 'actual_samples': 3234, 'expected_samples': 8760, 'completeness_ratio': 0.36917808219178083, 'gap_ratio': 0.0, 'avg_volume': np.float64(728098.2902906616), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:08:21,653 - advanced_data_fetcher - INFO - Cached data for RONIN/USDT 4h
2025-07-28 13:08:21,653 - advanced_data_fetcher - INFO - Fetching 1d data for RONIN/USDT
2025-07-28 13:08:22,116 - advanced_data_fetcher - WARNING - Low quality data for RONIN/USDT 1d: {'date_range_days': 539, 'min_required_days': 1460, 'actual_samples': 540, 'expected_samples': 1460, 'completeness_ratio': 0.3698630136986301, 'gap_ratio': 0.0, 'avg_volume': np.float64(4360499.760740741), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:08:22,215 - advanced_data_fetcher - INFO - Cached data for RONIN/USDT 1d
2025-07-28 13:08:22,216 - advanced_data_fetcher - INFO - Fetching 1h data for ROSE/USDT
2025-07-28 13:08:45,825 - advanced_data_fetcher - INFO - Cached data for ROSE/USDT 1h
2025-07-28 13:08:45,825 - advanced_data_fetcher - INFO - Fetching 4h data for ROSE/USDT
2025-07-28 13:08:51,433 - advanced_data_fetcher - INFO - Cached data for ROSE/USDT 4h
2025-07-28 13:08:51,433 - advanced_data_fetcher - INFO - Fetching 1d data for ROSE/USDT
2025-07-28 13:08:52,684 - advanced_data_fetcher - INFO - Cached data for ROSE/USDT 1d
2025-07-28 13:08:52,684 - advanced_data_fetcher - INFO - Fetching 1h data for RPL/USDT
2025-07-28 13:09:03,665 - advanced_data_fetcher - WARNING - Low quality data for RPL/USDT 1h: {'date_range_days': 922, 'min_required_days': 1460, 'actual_samples': 22130, 'expected_samples': 35040, 'completeness_ratio': 0.6315639269406392, 'gap_ratio': 4.518548642176133e-05, 'avg_volume': np.float64(7158.105835065522), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:09:06,500 - advanced_data_fetcher - INFO - Cached data for RPL/USDT 1h
2025-07-28 13:09:06,500 - advanced_data_fetcher - INFO - Fetching 4h data for RPL/USDT
2025-07-28 13:09:09,377 - advanced_data_fetcher - WARNING - Low quality data for RPL/USDT 4h: {'date_range_days': 922, 'min_required_days': 1460, 'actual_samples': 5533, 'expected_samples': 8760, 'completeness_ratio': 0.6316210045662101, 'gap_ratio': 0.0, 'avg_volume': np.float64(28629.83591722393), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:09:10,126 - advanced_data_fetcher - INFO - Cached data for RPL/USDT 4h
2025-07-28 13:09:10,126 - advanced_data_fetcher - INFO - Fetching 1d data for RPL/USDT
2025-07-28 13:09:10,623 - advanced_data_fetcher - WARNING - Low quality data for RPL/USDT 1d: {'date_range_days': 922, 'min_required_days': 1460, 'actual_samples': 923, 'expected_samples': 1460, 'completeness_ratio': 0.6321917808219178, 'gap_ratio': 0.0, 'avg_volume': np.float64(171623.9243011918), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:09:10,759 - advanced_data_fetcher - INFO - Cached data for RPL/USDT 1d
2025-07-28 13:09:10,760 - advanced_data_fetcher - INFO - Fetching 1h data for RSR/USDT
2025-07-28 13:09:32,850 - advanced_data_fetcher - INFO - Cached data for RSR/USDT 1h
2025-07-28 13:09:32,850 - advanced_data_fetcher - INFO - Fetching 4h data for RSR/USDT
2025-07-28 13:09:39,150 - advanced_data_fetcher - INFO - Cached data for RSR/USDT 4h
2025-07-28 13:09:39,150 - advanced_data_fetcher - INFO - Fetching 1d data for RSR/USDT
2025-07-28 13:09:40,425 - advanced_data_fetcher - INFO - Cached data for RSR/USDT 1d
2025-07-28 13:09:40,425 - advanced_data_fetcher - INFO - Fetching 1h data for RUNE/USDT
2025-07-28 13:10:04,516 - advanced_data_fetcher - INFO - Cached data for RUNE/USDT 1h
2025-07-28 13:10:04,516 - advanced_data_fetcher - INFO - Fetching 4h data for RUNE/USDT
2025-07-28 13:10:10,331 - advanced_data_fetcher - INFO - Cached data for RUNE/USDT 4h
2025-07-28 13:10:10,331 - advanced_data_fetcher - INFO - Fetching 1d data for RUNE/USDT
2025-07-28 13:10:11,557 - advanced_data_fetcher - INFO - Cached data for RUNE/USDT 1d
2025-07-28 13:10:11,557 - advanced_data_fetcher - INFO - Fetching 1h data for RVN/USDT
2025-07-28 13:10:34,250 - advanced_data_fetcher - INFO - Cached data for RVN/USDT 1h
2025-07-28 13:10:34,250 - advanced_data_fetcher - INFO - Fetching 4h data for RVN/USDT
2025-07-28 13:10:40,465 - advanced_data_fetcher - INFO - Cached data for RVN/USDT 4h
2025-07-28 13:10:40,465 - advanced_data_fetcher - INFO - Fetching 1d data for RVN/USDT
2025-07-28 13:10:41,700 - advanced_data_fetcher - INFO - Cached data for RVN/USDT 1d
2025-07-28 13:10:41,700 - advanced_data_fetcher - INFO - Fetching 1h data for S/USDT
2025-07-28 13:10:44,150 - advanced_data_fetcher - WARNING - Low quality data for S/USDT 1h: {'date_range_days': 193, 'min_required_days': 1460, 'actual_samples': 4635, 'expected_samples': 35040, 'completeness_ratio': 0.13227739726027396, 'gap_ratio': 0.0, 'avg_volume': np.float64(3552971.5047249184), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:10:44,781 - advanced_data_fetcher - INFO - Cached data for S/USDT 1h
2025-07-28 13:10:44,781 - advanced_data_fetcher - INFO - Fetching 4h data for S/USDT
2025-07-28 13:10:45,715 - advanced_data_fetcher - WARNING - Low quality data for S/USDT 4h: {'date_range_days': 193, 'min_required_days': 1460, 'actual_samples': 1159, 'expected_samples': 8760, 'completeness_ratio': 0.13230593607305935, 'gap_ratio': 0.0, 'avg_volume': np.float64(14208823.45349439), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:10:45,900 - advanced_data_fetcher - INFO - Cached data for S/USDT 4h
2025-07-28 13:10:45,900 - advanced_data_fetcher - INFO - Fetching 1d data for S/USDT
2025-07-28 13:10:46,350 - advanced_data_fetcher - WARNING - Low quality data for S/USDT 1d: {'date_range_days': 193, 'min_required_days': 1460, 'actual_samples': 194, 'expected_samples': 1460, 'completeness_ratio': 0.13287671232876713, 'gap_ratio': 0.0, 'avg_volume': np.float64(84886733.93092784), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:10:46,431 - advanced_data_fetcher - INFO - Cached data for S/USDT 1d
2025-07-28 13:10:46,431 - advanced_data_fetcher - INFO - Fetching 1h data for SAGA/USDT
2025-07-28 13:10:52,397 - advanced_data_fetcher - WARNING - Low quality data for SAGA/USDT 1h: {'date_range_days': 475, 'min_required_days': 1460, 'actual_samples': 11403, 'expected_samples': 35040, 'completeness_ratio': 0.3254280821917808, 'gap_ratio': 0.0, 'avg_volume': np.float64(690705.0959747435), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:10:53,900 - advanced_data_fetcher - INFO - Cached data for SAGA/USDT 1h
2025-07-28 13:10:53,900 - advanced_data_fetcher - INFO - Fetching 4h data for SAGA/USDT
2025-07-28 13:10:55,465 - advanced_data_fetcher - WARNING - Low quality data for SAGA/USDT 4h: {'date_range_days': 475, 'min_required_days': 1460, 'actual_samples': 2851, 'expected_samples': 8760, 'completeness_ratio': 0.32545662100456624, 'gap_ratio': 0.0, 'avg_volume': np.float64(2762578.116239916), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:10:55,850 - advanced_data_fetcher - INFO - Cached data for SAGA/USDT 4h
2025-07-28 13:10:55,850 - advanced_data_fetcher - INFO - Fetching 1d data for SAGA/USDT
2025-07-28 13:10:56,338 - advanced_data_fetcher - WARNING - Low quality data for SAGA/USDT 1d: {'date_range_days': 475, 'min_required_days': 1460, 'actual_samples': 476, 'expected_samples': 1460, 'completeness_ratio': 0.32602739726027397, 'gap_ratio': 0.0, 'avg_volume': np.float64(16546450.019747898), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:10:56,425 - advanced_data_fetcher - INFO - Cached data for SAGA/USDT 1d
2025-07-28 13:10:56,425 - advanced_data_fetcher - INFO - Fetching 1h data for SAHARA/USDT
2025-07-28 13:10:56,915 - advanced_data_fetcher - WARNING - Low quality data for SAHARA/USDT 1h: {'date_range_days': 31, 'min_required_days': 1460, 'actual_samples': 767, 'expected_samples': 35040, 'completeness_ratio': 0.021889269406392693, 'gap_ratio': 0.0, 'avg_volume': np.float64(19692749.367666233), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:10:57,050 - advanced_data_fetcher - INFO - Cached data for SAHARA/USDT 1h
2025-07-28 13:10:57,050 - advanced_data_fetcher - INFO - Fetching 4h data for SAHARA/USDT
2025-07-28 13:10:57,470 - advanced_data_fetcher - WARNING - Low quality data for SAHARA/USDT 4h: {'date_range_days': 31, 'min_required_days': 1460, 'actual_samples': 192, 'expected_samples': 8760, 'completeness_ratio': 0.021917808219178082, 'gap_ratio': 0.0, 'avg_volume': np.float64(78668478.625), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:10:57,555 - advanced_data_fetcher - INFO - Cached data for SAHARA/USDT 4h
2025-07-28 13:10:57,555 - advanced_data_fetcher - INFO - Fetching 1d data for SAHARA/USDT
2025-07-28 13:10:57,986 - advanced_data_fetcher - WARNING - Low quality data for SAHARA/USDT 1d: {'date_range_days': 32, 'min_required_days': 1460, 'actual_samples': 33, 'expected_samples': 1460, 'completeness_ratio': 0.022602739726027398, 'gap_ratio': 0.0, 'avg_volume': np.float64(457707512.0), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:10:58,065 - advanced_data_fetcher - INFO - Cached data for SAHARA/USDT 1d
2025-07-28 13:10:58,065 - advanced_data_fetcher - INFO - Fetching 1h data for SAND/USDT
2025-07-28 13:11:20,536 - advanced_data_fetcher - INFO - Cached data for SAND/USDT 1h
2025-07-28 13:11:20,536 - advanced_data_fetcher - INFO - Fetching 4h data for SAND/USDT
2025-07-28 13:11:26,350 - advanced_data_fetcher - INFO - Cached data for SAND/USDT 4h
2025-07-28 13:11:26,350 - advanced_data_fetcher - INFO - Fetching 1d data for SAND/USDT
2025-07-28 13:11:27,597 - advanced_data_fetcher - INFO - Cached data for SAND/USDT 1d
2025-07-28 13:11:27,598 - advanced_data_fetcher - INFO - Fetching 1h data for SANTOS/USDT
2025-07-28 13:11:48,951 - advanced_data_fetcher - INFO - Cached data for SANTOS/USDT 1h
2025-07-28 13:11:48,952 - advanced_data_fetcher - INFO - Fetching 4h data for SANTOS/USDT
2025-07-28 13:11:54,660 - advanced_data_fetcher - INFO - Cached data for SANTOS/USDT 4h
2025-07-28 13:11:54,661 - advanced_data_fetcher - INFO - Fetching 1d data for SANTOS/USDT
2025-07-28 13:11:55,798 - advanced_data_fetcher - INFO - Cached data for SANTOS/USDT 1d
2025-07-28 13:11:55,798 - advanced_data_fetcher - INFO - Fetching 1h data for SC/USDT
2025-07-28 13:12:19,025 - advanced_data_fetcher - INFO - Cached data for SC/USDT 1h
2025-07-28 13:12:19,025 - advanced_data_fetcher - INFO - Fetching 4h data for SC/USDT
2025-07-28 13:12:24,715 - advanced_data_fetcher - INFO - Cached data for SC/USDT 4h
2025-07-28 13:12:24,715 - advanced_data_fetcher - INFO - Fetching 1d data for SC/USDT
2025-07-28 13:12:26,353 - advanced_data_fetcher - INFO - Cached data for SC/USDT 1d
2025-07-28 13:12:26,354 - advanced_data_fetcher - INFO - Fetching 1h data for SCR/USDT
2025-07-28 13:12:29,715 - advanced_data_fetcher - WARNING - Low quality data for SCR/USDT 1h: {'date_range_days': 290, 'min_required_days': 1460, 'actual_samples': 6937, 'expected_samples': 35040, 'completeness_ratio': 0.19797374429223744, 'gap_ratio': 0.00344778049130872, 'avg_volume': np.float64(566393.364451492), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:12:30,565 - advanced_data_fetcher - INFO - Cached data for SCR/USDT 1h
2025-07-28 13:12:30,565 - advanced_data_fetcher - INFO - Fetching 4h data for SCR/USDT
2025-07-28 13:12:31,515 - advanced_data_fetcher - WARNING - Low quality data for SCR/USDT 4h: {'date_range_days': 290, 'min_required_days': 1460, 'actual_samples': 1736, 'expected_samples': 8760, 'completeness_ratio': 0.19817351598173516, 'gap_ratio': 0.002871912693854107, 'avg_volume': np.float64(2263289.61359447), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:12:31,799 - advanced_data_fetcher - INFO - Cached data for SCR/USDT 4h
2025-07-28 13:12:31,799 - advanced_data_fetcher - INFO - Fetching 1d data for SCR/USDT
2025-07-28 13:12:32,221 - advanced_data_fetcher - WARNING - Low quality data for SCR/USDT 1d: {'date_range_days': 290, 'min_required_days': 1460, 'actual_samples': 291, 'expected_samples': 1460, 'completeness_ratio': 0.19931506849315067, 'gap_ratio': 0.0, 'avg_volume': np.float64(13501961.406185567), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:12:32,318 - advanced_data_fetcher - INFO - Cached data for SCR/USDT 1d
2025-07-28 13:12:32,319 - advanced_data_fetcher - INFO - Fetching 1h data for SCRT/USDT
2025-07-28 13:12:52,677 - advanced_data_fetcher - INFO - Cached data for SCRT/USDT 1h
2025-07-28 13:12:52,678 - advanced_data_fetcher - INFO - Fetching 4h data for SCRT/USDT
2025-07-28 13:12:57,888 - advanced_data_fetcher - INFO - Cached data for SCRT/USDT 4h
2025-07-28 13:12:57,889 - advanced_data_fetcher - INFO - Fetching 1d data for SCRT/USDT
2025-07-28 13:12:59,017 - advanced_data_fetcher - INFO - Cached data for SCRT/USDT 1d
2025-07-28 13:12:59,017 - advanced_data_fetcher - INFO - Fetching 1h data for SEI/USDT
2025-07-28 13:13:08,052 - advanced_data_fetcher - WARNING - Low quality data for SEI/USDT 1h: {'date_range_days': 712, 'min_required_days': 1460, 'actual_samples': 17111, 'expected_samples': 35040, 'completeness_ratio': 0.48832762557077625, 'gap_ratio': 0.0, 'avg_volume': np.float64(5356478.535801531), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:13:10,226 - advanced_data_fetcher - INFO - Cached data for SEI/USDT 1h
2025-07-28 13:13:10,226 - advanced_data_fetcher - INFO - Fetching 4h data for SEI/USDT
2025-07-28 13:13:12,698 - advanced_data_fetcher - WARNING - Low quality data for SEI/USDT 4h: {'date_range_days': 712, 'min_required_days': 1460, 'actual_samples': 4278, 'expected_samples': 8760, 'completeness_ratio': 0.48835616438356166, 'gap_ratio': 0.0, 'avg_volume': np.float64(21424662.997685835), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:13:13,354 - advanced_data_fetcher - INFO - Cached data for SEI/USDT 4h
2025-07-28 13:13:13,354 - advanced_data_fetcher - INFO - Fetching 1d data for SEI/USDT
2025-07-28 13:13:13,834 - advanced_data_fetcher - WARNING - Low quality data for SEI/USDT 1d: {'date_range_days': 713, 'min_required_days': 1460, 'actual_samples': 714, 'expected_samples': 1460, 'completeness_ratio': 0.48904109589041095, 'gap_ratio': 0.0, 'avg_volume': np.float64(128367938.80126052), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:13:13,956 - advanced_data_fetcher - INFO - Cached data for SEI/USDT 1d
2025-07-28 13:13:13,956 - advanced_data_fetcher - INFO - Fetching 1h data for SFP/USDT
2025-07-28 13:13:37,424 - advanced_data_fetcher - INFO - Cached data for SFP/USDT 1h
2025-07-28 13:13:37,425 - advanced_data_fetcher - INFO - Fetching 4h data for SFP/USDT
2025-07-28 13:13:44,031 - advanced_data_fetcher - INFO - Cached data for SFP/USDT 4h
2025-07-28 13:13:44,031 - advanced_data_fetcher - INFO - Fetching 1d data for SFP/USDT
2025-07-28 13:13:45,179 - advanced_data_fetcher - INFO - Cached data for SFP/USDT 1d
2025-07-28 13:13:45,179 - advanced_data_fetcher - INFO - Fetching 1h data for SHELL/USDT
2025-07-28 13:13:47,137 - advanced_data_fetcher - WARNING - Low quality data for SHELL/USDT 1h: {'date_range_days': 150, 'min_required_days': 1460, 'actual_samples': 3622, 'expected_samples': 35040, 'completeness_ratio': 0.1033675799086758, 'gap_ratio': 0.0, 'avg_volume': np.float64(1378297.9486471564), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:13:47,693 - advanced_data_fetcher - INFO - Cached data for SHELL/USDT 1h
2025-07-28 13:13:47,693 - advanced_data_fetcher - INFO - Fetching 4h data for SHELL/USDT
2025-07-28 13:13:48,185 - advanced_data_fetcher - WARNING - Low quality data for SHELL/USDT 4h: {'date_range_days': 150, 'min_required_days': 1460, 'actual_samples': 906, 'expected_samples': 8760, 'completeness_ratio': 0.10342465753424658, 'gap_ratio': 0.0, 'avg_volume': np.float64(5510149.1942604855), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:13:48,365 - advanced_data_fetcher - INFO - Cached data for SHELL/USDT 4h
2025-07-28 13:13:48,366 - advanced_data_fetcher - INFO - Fetching 1d data for SHELL/USDT
2025-07-28 13:13:48,814 - advanced_data_fetcher - WARNING - Low quality data for SHELL/USDT 1d: {'date_range_days': 151, 'min_required_days': 1460, 'actual_samples': 152, 'expected_samples': 1460, 'completeness_ratio': 0.10410958904109589, 'gap_ratio': 0.0, 'avg_volume': np.float64(32843389.27631579), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:13:48,865 - advanced_data_fetcher - INFO - Cached data for SHELL/USDT 1d
2025-07-28 13:13:48,865 - advanced_data_fetcher - INFO - Fetching 1h data for SHIB/USDT
2025-07-28 13:14:12,273 - advanced_data_fetcher - INFO - Cached data for SHIB/USDT 1h
2025-07-28 13:14:12,274 - advanced_data_fetcher - INFO - Fetching 4h data for SHIB/USDT
2025-07-28 13:14:18,053 - advanced_data_fetcher - INFO - Cached data for SHIB/USDT 4h
2025-07-28 13:14:18,054 - advanced_data_fetcher - INFO - Fetching 1d data for SHIB/USDT
2025-07-28 13:14:19,481 - advanced_data_fetcher - INFO - Cached data for SHIB/USDT 1d
2025-07-28 13:14:19,481 - advanced_data_fetcher - INFO - Fetching 1h data for SIGN/USDT
2025-07-28 13:14:21,015 - advanced_data_fetcher - WARNING - Low quality data for SIGN/USDT 1h: {'date_range_days': 90, 'min_required_days': 1460, 'actual_samples': 2184, 'expected_samples': 35040, 'completeness_ratio': 0.06232876712328767, 'gap_ratio': 0.0, 'avg_volume': np.float64(4627570.707875458), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:14:21,428 - advanced_data_fetcher - INFO - Cached data for SIGN/USDT 1h
2025-07-28 13:14:21,429 - advanced_data_fetcher - INFO - Fetching 4h data for SIGN/USDT
2025-07-28 13:14:21,965 - advanced_data_fetcher - WARNING - Low quality data for SIGN/USDT 4h: {'date_range_days': 91, 'min_required_days': 1460, 'actual_samples': 547, 'expected_samples': 8760, 'completeness_ratio': 0.06244292237442922, 'gap_ratio': 0.0, 'avg_volume': np.float64(18476443.191956125), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:14:22,128 - advanced_data_fetcher - INFO - Cached data for SIGN/USDT 4h
2025-07-28 13:14:22,130 - advanced_data_fetcher - INFO - Fetching 1d data for SIGN/USDT
2025-07-28 13:14:22,581 - advanced_data_fetcher - WARNING - Low quality data for SIGN/USDT 1d: {'date_range_days': 91, 'min_required_days': 1460, 'actual_samples': 92, 'expected_samples': 1460, 'completeness_ratio': 0.06301369863013699, 'gap_ratio': 0.0, 'avg_volume': np.float64(109854504.63043478), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:14:22,638 - advanced_data_fetcher - INFO - Cached data for SIGN/USDT 1d
2025-07-28 13:14:22,639 - advanced_data_fetcher - INFO - Fetching 1h data for SKL/USDT
2025-07-28 13:14:45,591 - advanced_data_fetcher - INFO - Cached data for SKL/USDT 1h
2025-07-28 13:14:45,591 - advanced_data_fetcher - INFO - Fetching 4h data for SKL/USDT
2025-07-28 13:14:51,228 - advanced_data_fetcher - INFO - Cached data for SKL/USDT 4h
2025-07-28 13:14:51,229 - advanced_data_fetcher - INFO - Fetching 1d data for SKL/USDT
2025-07-28 13:14:52,548 - advanced_data_fetcher - INFO - Cached data for SKL/USDT 1d
2025-07-28 13:14:52,549 - advanced_data_fetcher - INFO - Fetching 1h data for SLF/USDT
2025-07-28 13:14:56,775 - advanced_data_fetcher - WARNING - Low quality data for SLF/USDT 1h: {'date_range_days': 332, 'min_required_days': 1460, 'actual_samples': 7971, 'expected_samples': 35040, 'completeness_ratio': 0.22748287671232878, 'gap_ratio': 0.0, 'avg_volume': np.float64(846688.7614853844), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:14:57,841 - advanced_data_fetcher - INFO - Cached data for SLF/USDT 1h
2025-07-28 13:14:57,842 - advanced_data_fetcher - INFO - Fetching 4h data for SLF/USDT
2025-07-28 13:14:58,900 - advanced_data_fetcher - WARNING - Low quality data for SLF/USDT 4h: {'date_range_days': 332, 'min_required_days': 1460, 'actual_samples': 1993, 'expected_samples': 8760, 'completeness_ratio': 0.22751141552511417, 'gap_ratio': 0.0, 'avg_volume': np.float64(3386330.214651279), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:14:59,250 - advanced_data_fetcher - INFO - Cached data for SLF/USDT 4h
2025-07-28 13:14:59,250 - advanced_data_fetcher - INFO - Fetching 1d data for SLF/USDT
2025-07-28 13:14:59,700 - advanced_data_fetcher - WARNING - Low quality data for SLF/USDT 1d: {'date_range_days': 332, 'min_required_days': 1460, 'actual_samples': 333, 'expected_samples': 1460, 'completeness_ratio': 0.22808219178082192, 'gap_ratio': 0.0, 'avg_volume': np.float64(20267135.488888893), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:14:59,831 - advanced_data_fetcher - INFO - Cached data for SLF/USDT 1d
2025-07-28 13:14:59,831 - advanced_data_fetcher - INFO - Fetching 1h data for SLP/USDT
2025-07-28 13:15:22,057 - advanced_data_fetcher - INFO - Cached data for SLP/USDT 1h
2025-07-28 13:15:22,058 - advanced_data_fetcher - INFO - Fetching 4h data for SLP/USDT
2025-07-28 13:15:27,789 - advanced_data_fetcher - INFO - Cached data for SLP/USDT 4h
2025-07-28 13:15:27,789 - advanced_data_fetcher - INFO - Fetching 1d data for SLP/USDT
2025-07-28 13:15:29,083 - advanced_data_fetcher - INFO - Cached data for SLP/USDT 1d
2025-07-28 13:15:29,083 - advanced_data_fetcher - INFO - Fetching 1h data for SNX/USDT
2025-07-28 13:15:51,915 - advanced_data_fetcher - INFO - Cached data for SNX/USDT 1h
2025-07-28 13:15:51,915 - advanced_data_fetcher - INFO - Fetching 4h data for SNX/USDT
2025-07-28 13:15:57,618 - advanced_data_fetcher - INFO - Cached data for SNX/USDT 4h
2025-07-28 13:15:57,619 - advanced_data_fetcher - INFO - Fetching 1d data for SNX/USDT
2025-07-28 13:15:58,794 - advanced_data_fetcher - INFO - Cached data for SNX/USDT 1d
2025-07-28 13:15:58,794 - advanced_data_fetcher - INFO - Fetching 1h data for SOL/USDT
2025-07-28 13:16:21,660 - advanced_data_fetcher - INFO - Cached data for SOL/USDT 1h
2025-07-28 13:16:21,661 - advanced_data_fetcher - INFO - Fetching 4h data for SOL/USDT
2025-07-28 13:16:27,626 - advanced_data_fetcher - INFO - Cached data for SOL/USDT 4h
2025-07-28 13:16:27,626 - advanced_data_fetcher - INFO - Fetching 1d data for SOL/USDT
2025-07-28 13:16:28,814 - advanced_data_fetcher - INFO - Cached data for SOL/USDT 1d
2025-07-28 13:16:28,815 - advanced_data_fetcher - INFO - Fetching 1h data for SOLV/USDT
2025-07-28 13:16:31,153 - advanced_data_fetcher - WARNING - Low quality data for SOLV/USDT 1h: {'date_range_days': 192, 'min_required_days': 1460, 'actual_samples': 4609, 'expected_samples': 35040, 'completeness_ratio': 0.13153538812785387, 'gap_ratio': 0.0, 'avg_volume': np.float64(10577923.693642873), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:16:31,829 - advanced_data_fetcher - INFO - Cached data for SOLV/USDT 1h
2025-07-28 13:16:31,830 - advanced_data_fetcher - INFO - Fetching 4h data for SOLV/USDT
2025-07-28 13:16:32,776 - advanced_data_fetcher - WARNING - Low quality data for SOLV/USDT 4h: {'date_range_days': 192, 'min_required_days': 1460, 'actual_samples': 1153, 'expected_samples': 8760, 'completeness_ratio': 0.13162100456621004, 'gap_ratio': 0.0, 'avg_volume': np.float64(42284171.989592366), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:16:33,003 - advanced_data_fetcher - INFO - Cached data for SOLV/USDT 4h
2025-07-28 13:16:33,004 - advanced_data_fetcher - INFO - Fetching 1d data for SOLV/USDT
2025-07-28 13:16:33,433 - advanced_data_fetcher - WARNING - Low quality data for SOLV/USDT 1d: {'date_range_days': 192, 'min_required_days': 1460, 'actual_samples': 193, 'expected_samples': 1460, 'completeness_ratio': 0.13219178082191782, 'gap_ratio': 0.0, 'avg_volume': np.float64(252609587.0673575), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:16:33,521 - advanced_data_fetcher - INFO - Cached data for SOLV/USDT 1d
2025-07-28 13:16:33,521 - advanced_data_fetcher - INFO - Fetching 1h data for SOPH/USDT
2025-07-28 13:16:34,496 - advanced_data_fetcher - WARNING - Low quality data for SOPH/USDT 1h: {'date_range_days': 60, 'min_required_days': 1460, 'actual_samples': 1462, 'expected_samples': 35040, 'completeness_ratio': 0.04172374429223744, 'gap_ratio': 0.0, 'avg_volume': np.float64(9958843.484268125), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:16:34,754 - advanced_data_fetcher - INFO - Cached data for SOPH/USDT 1h
2025-07-28 13:16:34,755 - advanced_data_fetcher - INFO - Fetching 4h data for SOPH/USDT
2025-07-28 13:16:35,378 - advanced_data_fetcher - WARNING - Low quality data for SOPH/USDT 4h: {'date_range_days': 60, 'min_required_days': 1460, 'actual_samples': 366, 'expected_samples': 8760, 'completeness_ratio': 0.04178082191780822, 'gap_ratio': 0.0, 'avg_volume': np.float64(39780954.027322404), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:16:35,479 - advanced_data_fetcher - INFO - Cached data for SOPH/USDT 4h
2025-07-28 13:16:35,479 - advanced_data_fetcher - INFO - Fetching 1d data for SOPH/USDT
2025-07-28 13:16:36,088 - advanced_data_fetcher - WARNING - Low quality data for SOPH/USDT 1d: {'date_range_days': 61, 'min_required_days': 1460, 'actual_samples': 62, 'expected_samples': 1460, 'completeness_ratio': 0.04246575342465753, 'gap_ratio': 0.0, 'avg_volume': np.float64(234835954.41935483), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:16:36,174 - advanced_data_fetcher - INFO - Cached data for SOPH/USDT 1d
2025-07-28 13:16:36,175 - advanced_data_fetcher - INFO - Fetching 1h data for SPELL/USDT
2025-07-28 13:16:56,555 - advanced_data_fetcher - INFO - Cached data for SPELL/USDT 1h
2025-07-28 13:16:56,555 - advanced_data_fetcher - INFO - Fetching 4h data for SPELL/USDT
2025-07-28 13:17:01,574 - advanced_data_fetcher - INFO - Cached data for SPELL/USDT 4h
2025-07-28 13:17:01,575 - advanced_data_fetcher - INFO - Fetching 1d data for SPELL/USDT
2025-07-28 13:17:02,861 - advanced_data_fetcher - INFO - Cached data for SPELL/USDT 1d
2025-07-28 13:17:02,861 - advanced_data_fetcher - INFO - Fetching 1h data for SPK/USDT
2025-07-28 13:17:03,355 - advanced_data_fetcher - WARNING - Low quality data for SPK/USDT 1h: {'date_range_days': 41, 'min_required_days': 1460, 'actual_samples': 986, 'expected_samples': 35040, 'completeness_ratio': 0.028139269406392695, 'gap_ratio': 0.0, 'avg_volume': np.float64(21481104.746450305), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:17:03,520 - advanced_data_fetcher - INFO - Cached data for SPK/USDT 1h
2025-07-28 13:17:03,520 - advanced_data_fetcher - INFO - Fetching 4h data for SPK/USDT
2025-07-28 13:17:03,950 - advanced_data_fetcher - WARNING - Low quality data for SPK/USDT 4h: {'date_range_days': 41, 'min_required_days': 1460, 'actual_samples': 247, 'expected_samples': 8760, 'completeness_ratio': 0.02819634703196347, 'gap_ratio': 0.0, 'avg_volume': np.float64(85750482.91497976), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:17:04,021 - advanced_data_fetcher - INFO - Cached data for SPK/USDT 4h
2025-07-28 13:17:04,021 - advanced_data_fetcher - INFO - Fetching 1d data for SPK/USDT
2025-07-28 13:17:04,443 - advanced_data_fetcher - WARNING - Low quality data for SPK/USDT 1d: {'date_range_days': 41, 'min_required_days': 1460, 'actual_samples': 42, 'expected_samples': 1460, 'completeness_ratio': 0.028767123287671233, 'gap_ratio': 0.0, 'avg_volume': np.float64(504294506.6666667), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:17:04,512 - advanced_data_fetcher - INFO - Cached data for SPK/USDT 1d
2025-07-28 13:17:04,512 - advanced_data_fetcher - INFO - Fetching 1h data for SSV/USDT
2025-07-28 13:17:15,457 - advanced_data_fetcher - WARNING - Low quality data for SSV/USDT 1h: {'date_range_days': 885, 'min_required_days': 1460, 'actual_samples': 21242, 'expected_samples': 35040, 'completeness_ratio': 0.6062214611872146, 'gap_ratio': 4.707433036765052e-05, 'avg_volume': np.float64(17264.36537312871), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:17:18,181 - advanced_data_fetcher - INFO - Cached data for SSV/USDT 1h
2025-07-28 13:17:18,182 - advanced_data_fetcher - INFO - Fetching 4h data for SSV/USDT
2025-07-28 13:17:21,004 - advanced_data_fetcher - WARNING - Low quality data for SSV/USDT 4h: {'date_range_days': 885, 'min_required_days': 1460, 'actual_samples': 5311, 'expected_samples': 8760, 'completeness_ratio': 0.6062785388127854, 'gap_ratio': 0.0, 'avg_volume': np.float64(69050.96013104878), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:17:21,717 - advanced_data_fetcher - INFO - Cached data for SSV/USDT 4h
2025-07-28 13:17:21,717 - advanced_data_fetcher - INFO - Fetching 1d data for SSV/USDT
2025-07-28 13:17:22,194 - advanced_data_fetcher - WARNING - Low quality data for SSV/USDT 1d: {'date_range_days': 885, 'min_required_days': 1460, 'actual_samples': 886, 'expected_samples': 1460, 'completeness_ratio': 0.6068493150684932, 'gap_ratio': 0.0, 'avg_volume': np.float64(413916.08268171555), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:17:23,140 - advanced_data_fetcher - INFO - Cached data for SSV/USDT 1d
2025-07-28 13:17:23,141 - advanced_data_fetcher - INFO - Fetching 1h data for STEEM/USDT
2025-07-28 13:17:41,367 - advanced_data_fetcher - INFO - Cached data for STEEM/USDT 1h
2025-07-28 13:17:41,368 - advanced_data_fetcher - INFO - Fetching 4h data for STEEM/USDT
2025-07-28 13:17:46,280 - advanced_data_fetcher - INFO - Cached data for STEEM/USDT 4h
2025-07-28 13:17:46,281 - advanced_data_fetcher - INFO - Fetching 1d data for STEEM/USDT
2025-07-28 13:17:47,428 - advanced_data_fetcher - INFO - Cached data for STEEM/USDT 1d
2025-07-28 13:17:47,429 - advanced_data_fetcher - INFO - Fetching 1h data for STG/USDT
2025-07-28 13:18:00,290 - advanced_data_fetcher - WARNING - Low quality data for STG/USDT 1h: {'date_range_days': 1074, 'min_required_days': 1460, 'actual_samples': 25777, 'expected_samples': 35040, 'completeness_ratio': 0.7356449771689497, 'gap_ratio': 3.879276902785321e-05, 'avg_volume': np.float64(589843.063591574), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:18:03,925 - advanced_data_fetcher - INFO - Cached data for STG/USDT 1h
2025-07-28 13:18:03,925 - advanced_data_fetcher - INFO - Fetching 4h data for STG/USDT
2025-07-28 13:18:07,316 - advanced_data_fetcher - WARNING - Low quality data for STG/USDT 4h: {'date_range_days': 1074, 'min_required_days': 1460, 'actual_samples': 6445, 'expected_samples': 8760, 'completeness_ratio': 0.735730593607306, 'gap_ratio': 0.0, 'avg_volume': np.float64(2359098.196881303), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:18:08,176 - advanced_data_fetcher - INFO - Cached data for STG/USDT 4h
2025-07-28 13:18:08,176 - advanced_data_fetcher - INFO - Fetching 1d data for STG/USDT
2025-07-28 13:18:09,058 - advanced_data_fetcher - WARNING - Low quality data for STG/USDT 1d: {'date_range_days': 1074, 'min_required_days': 1460, 'actual_samples': 1075, 'expected_samples': 1460, 'completeness_ratio': 0.7363013698630136, 'gap_ratio': 0.0, 'avg_volume': np.float64(14143616.631534884), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:18:09,231 - advanced_data_fetcher - INFO - Cached data for STG/USDT 1d
2025-07-28 13:18:09,232 - advanced_data_fetcher - INFO - Fetching 1h data for STO/USDT
2025-07-28 13:18:10,619 - advanced_data_fetcher - WARNING - Low quality data for STO/USDT 1h: {'date_range_days': 86, 'min_required_days': 1460, 'actual_samples': 2083, 'expected_samples': 35040, 'completeness_ratio': 0.05944634703196347, 'gap_ratio': 0.0, 'avg_volume': np.float64(2504305.529764762), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:18:10,909 - advanced_data_fetcher - INFO - Cached data for STO/USDT 1h
2025-07-28 13:18:10,910 - advanced_data_fetcher - INFO - Fetching 4h data for STO/USDT
2025-07-28 13:18:11,350 - advanced_data_fetcher - WARNING - Low quality data for STO/USDT 4h: {'date_range_days': 86, 'min_required_days': 1460, 'actual_samples': 521, 'expected_samples': 8760, 'completeness_ratio': 0.05947488584474886, 'gap_ratio': 0.0, 'avg_volume': np.float64(10012415.39059501), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:18:11,460 - advanced_data_fetcher - INFO - Cached data for STO/USDT 4h
2025-07-28 13:18:11,460 - advanced_data_fetcher - INFO - Fetching 1d data for STO/USDT
2025-07-28 13:18:11,872 - advanced_data_fetcher - WARNING - Low quality data for STO/USDT 1d: {'date_range_days': 87, 'min_required_days': 1460, 'actual_samples': 88, 'expected_samples': 1460, 'completeness_ratio': 0.06027397260273973, 'gap_ratio': 0.0, 'avg_volume': np.float64(59278050.21022727), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:18:11,920 - advanced_data_fetcher - INFO - Cached data for STO/USDT 1d
2025-07-28 13:18:11,921 - advanced_data_fetcher - INFO - Fetching 1h data for STORJ/USDT
2025-07-28 13:18:34,267 - advanced_data_fetcher - INFO - Cached data for STORJ/USDT 1h
2025-07-28 13:18:34,268 - advanced_data_fetcher - INFO - Fetching 4h data for STORJ/USDT
2025-07-28 13:18:39,889 - advanced_data_fetcher - INFO - Cached data for STORJ/USDT 4h
2025-07-28 13:18:39,889 - advanced_data_fetcher - INFO - Fetching 1d data for STORJ/USDT
2025-07-28 13:18:41,067 - advanced_data_fetcher - INFO - Cached data for STORJ/USDT 1d
2025-07-28 13:18:41,068 - advanced_data_fetcher - INFO - Fetching 1h data for STRAX/USDT
2025-07-28 13:19:03,700 - advanced_data_fetcher - INFO - Cached data for STRAX/USDT 1h
2025-07-28 13:19:03,700 - advanced_data_fetcher - INFO - Fetching 4h data for STRAX/USDT
2025-07-28 13:19:09,218 - advanced_data_fetcher - INFO - Cached data for STRAX/USDT 4h
2025-07-28 13:19:09,219 - advanced_data_fetcher - INFO - Fetching 1d data for STRAX/USDT
2025-07-28 13:19:10,424 - advanced_data_fetcher - INFO - Cached data for STRAX/USDT 1d
2025-07-28 13:19:10,424 - advanced_data_fetcher - INFO - Fetching 1h data for STRK/USDT
2025-07-28 13:19:17,137 - advanced_data_fetcher - WARNING - Low quality data for STRK/USDT 1h: {'date_range_days': 523, 'min_required_days': 1460, 'actual_samples': 12574, 'expected_samples': 35040, 'completeness_ratio': 0.35884703196347034, 'gap_ratio': 0.0, 'avg_volume': np.float64(1710738.583707651), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:19:18,731 - advanced_data_fetcher - INFO - Cached data for STRK/USDT 1h
2025-07-28 13:19:18,731 - advanced_data_fetcher - INFO - Fetching 4h data for STRK/USDT
2025-07-28 13:19:20,596 - advanced_data_fetcher - WARNING - Low quality data for STRK/USDT 4h: {'date_range_days': 523, 'min_required_days': 1460, 'actual_samples': 3144, 'expected_samples': 8760, 'completeness_ratio': 0.3589041095890411, 'gap_ratio': 0.0, 'avg_volume': np.float64(6841866.275645674), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:19:20,998 - advanced_data_fetcher - INFO - Cached data for STRK/USDT 4h
2025-07-28 13:19:20,999 - advanced_data_fetcher - INFO - Fetching 1d data for STRK/USDT
2025-07-28 13:19:21,469 - advanced_data_fetcher - WARNING - Low quality data for STRK/USDT 1d: {'date_range_days': 524, 'min_required_days': 1460, 'actual_samples': 525, 'expected_samples': 1460, 'completeness_ratio': 0.3595890410958904, 'gap_ratio': 0.0, 'avg_volume': np.float64(40973004.8964381), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:19:21,568 - advanced_data_fetcher - INFO - Cached data for STRK/USDT 1d
2025-07-28 13:19:21,569 - advanced_data_fetcher - INFO - Fetching 1h data for STX/USDT
2025-07-28 13:19:44,684 - advanced_data_fetcher - INFO - Cached data for STX/USDT 1h
2025-07-28 13:19:44,684 - advanced_data_fetcher - INFO - Fetching 4h data for STX/USDT
2025-07-28 13:19:50,480 - advanced_data_fetcher - INFO - Cached data for STX/USDT 4h
2025-07-28 13:19:50,480 - advanced_data_fetcher - INFO - Fetching 1d data for STX/USDT
2025-07-28 13:19:51,753 - advanced_data_fetcher - INFO - Cached data for STX/USDT 1d
2025-07-28 13:19:51,753 - advanced_data_fetcher - INFO - Fetching 1h data for SUI/USDT
2025-07-28 13:20:02,465 - advanced_data_fetcher - WARNING - Low quality data for SUI/USDT 1h: {'date_range_days': 816, 'min_required_days': 1460, 'actual_samples': 19607, 'expected_samples': 35040, 'completeness_ratio': 0.559560502283105, 'gap_ratio': 0.0, 'avg_volume': np.float64(2786728.0097516193), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:20:05,000 - advanced_data_fetcher - INFO - Cached data for SUI/USDT 1h
2025-07-28 13:20:05,000 - advanced_data_fetcher - INFO - Fetching 4h data for SUI/USDT
2025-07-28 13:20:07,500 - advanced_data_fetcher - WARNING - Low quality data for SUI/USDT 4h: {'date_range_days': 816, 'min_required_days': 1460, 'actual_samples': 4902, 'expected_samples': 8760, 'completeness_ratio': 0.5595890410958904, 'gap_ratio': 0.0, 'avg_volume': np.float64(11146344.130456956), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:20:08,081 - advanced_data_fetcher - INFO - Cached data for SUI/USDT 4h
2025-07-28 13:20:08,081 - advanced_data_fetcher - INFO - Fetching 1d data for SUI/USDT
2025-07-28 13:20:08,600 - advanced_data_fetcher - WARNING - Low quality data for SUI/USDT 1d: {'date_range_days': 817, 'min_required_days': 1460, 'actual_samples': 818, 'expected_samples': 1460, 'completeness_ratio': 0.5602739726027397, 'gap_ratio': 0.0, 'avg_volume': np.float64(66796307.104278736), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:20:08,750 - advanced_data_fetcher - INFO - Cached data for SUI/USDT 1d
2025-07-28 13:20:08,750 - advanced_data_fetcher - INFO - Fetching 1h data for SUN/USDT
2025-07-28 13:20:31,180 - advanced_data_fetcher - INFO - Cached data for SUN/USDT 1h
2025-07-28 13:20:31,180 - advanced_data_fetcher - INFO - Fetching 4h data for SUN/USDT
2025-07-28 13:20:36,597 - advanced_data_fetcher - INFO - Cached data for SUN/USDT 4h
2025-07-28 13:20:36,600 - advanced_data_fetcher - INFO - Fetching 1d data for SUN/USDT
2025-07-28 13:20:37,765 - advanced_data_fetcher - INFO - Cached data for SUN/USDT 1d
2025-07-28 13:20:37,765 - advanced_data_fetcher - INFO - Fetching 1h data for SUSHI/USDT
2025-07-28 13:21:01,678 - advanced_data_fetcher - INFO - Cached data for SUSHI/USDT 1h
2025-07-28 13:21:01,678 - advanced_data_fetcher - INFO - Fetching 4h data for SUSHI/USDT
2025-07-28 13:21:07,131 - advanced_data_fetcher - INFO - Cached data for SUSHI/USDT 4h
2025-07-28 13:21:07,131 - advanced_data_fetcher - INFO - Fetching 1d data for SUSHI/USDT
2025-07-28 13:21:08,328 - advanced_data_fetcher - INFO - Cached data for SUSHI/USDT 1d
2025-07-28 13:21:08,345 - advanced_data_fetcher - INFO - Fetching 1h data for SXP/USDT
2025-07-28 13:21:36,500 - advanced_data_fetcher - INFO - Cached data for SXP/USDT 1h
2025-07-28 13:21:36,501 - advanced_data_fetcher - INFO - Fetching 4h data for SXP/USDT
2025-07-28 13:21:42,815 - advanced_data_fetcher - INFO - Cached data for SXP/USDT 4h
2025-07-28 13:21:42,815 - advanced_data_fetcher - INFO - Fetching 1d data for SXP/USDT
2025-07-28 13:21:44,003 - advanced_data_fetcher - INFO - Cached data for SXP/USDT 1d
2025-07-28 13:21:44,004 - advanced_data_fetcher - INFO - Fetching 1h data for SXT/USDT
2025-07-28 13:21:45,077 - advanced_data_fetcher - WARNING - Low quality data for SXT/USDT 1h: {'date_range_days': 80, 'min_required_days': 1460, 'actual_samples': 1942, 'expected_samples': 35040, 'completeness_ratio': 0.05542237442922374, 'gap_ratio': 0.0, 'avg_volume': np.float64(4195411.837590113), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:21:45,364 - advanced_data_fetcher - INFO - Cached data for SXT/USDT 1h
2025-07-28 13:21:45,364 - advanced_data_fetcher - INFO - Fetching 4h data for SXT/USDT
2025-07-28 13:21:46,077 - advanced_data_fetcher - WARNING - Low quality data for SXT/USDT 4h: {'date_range_days': 80, 'min_required_days': 1460, 'actual_samples': 486, 'expected_samples': 8760, 'completeness_ratio': 0.05547945205479452, 'gap_ratio': 0.0, 'avg_volume': np.float64(16764382.281069959), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:21:46,246 - advanced_data_fetcher - INFO - Cached data for SXT/USDT 4h
2025-07-28 13:21:46,247 - advanced_data_fetcher - INFO - Fetching 1d data for SXT/USDT
2025-07-28 13:21:46,676 - advanced_data_fetcher - WARNING - Low quality data for SXT/USDT 1d: {'date_range_days': 81, 'min_required_days': 1460, 'actual_samples': 82, 'expected_samples': 1460, 'completeness_ratio': 0.056164383561643834, 'gap_ratio': 0.0, 'avg_volume': np.float64(99359631.56829268), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:21:46,726 - advanced_data_fetcher - INFO - Cached data for SXT/USDT 1d
2025-07-28 13:21:46,742 - advanced_data_fetcher - INFO - Fetching 1h data for SYN/USDT
2025-07-28 13:21:57,334 - advanced_data_fetcher - WARNING - Low quality data for SYN/USDT 1h: {'date_range_days': 887, 'min_required_days': 1460, 'actual_samples': 21288, 'expected_samples': 35040, 'completeness_ratio': 0.6075342465753425, 'gap_ratio': 4.6972614965475126e-05, 'avg_volume': np.float64(320965.2112692597), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:21:59,907 - advanced_data_fetcher - INFO - Cached data for SYN/USDT 1h
2025-07-28 13:21:59,908 - advanced_data_fetcher - INFO - Fetching 4h data for SYN/USDT
2025-07-28 13:22:02,825 - advanced_data_fetcher - WARNING - Low quality data for SYN/USDT 4h: {'date_range_days': 887, 'min_required_days': 1460, 'actual_samples': 5323, 'expected_samples': 8760, 'completeness_ratio': 0.607648401826484, 'gap_ratio': 0.0, 'avg_volume': np.float64(1283619.7405598348), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:22:03,500 - advanced_data_fetcher - INFO - Cached data for SYN/USDT 4h
2025-07-28 13:22:03,500 - advanced_data_fetcher - INFO - Fetching 1d data for SYN/USDT
2025-07-28 13:22:04,015 - advanced_data_fetcher - WARNING - Low quality data for SYN/USDT 1d: {'date_range_days': 887, 'min_required_days': 1460, 'actual_samples': 888, 'expected_samples': 1460, 'completeness_ratio': 0.6082191780821918, 'gap_ratio': 0.0, 'avg_volume': np.float64(7694490.85472973), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:22:04,181 - advanced_data_fetcher - INFO - Cached data for SYN/USDT 1d
2025-07-28 13:22:04,181 - advanced_data_fetcher - INFO - Fetching 1h data for SYS/USDT
2025-07-28 13:22:24,885 - advanced_data_fetcher - INFO - Cached data for SYS/USDT 1h
2025-07-28 13:22:24,885 - advanced_data_fetcher - INFO - Fetching 4h data for SYS/USDT
2025-07-28 13:22:30,315 - advanced_data_fetcher - INFO - Cached data for SYS/USDT 4h
2025-07-28 13:22:30,315 - advanced_data_fetcher - INFO - Fetching 1d data for SYS/USDT
2025-07-28 13:22:31,535 - advanced_data_fetcher - INFO - Cached data for SYS/USDT 1d
2025-07-28 13:22:31,535 - advanced_data_fetcher - INFO - Fetching 1h data for T/USDT
2025-07-28 13:22:50,815 - advanced_data_fetcher - INFO - Cached data for T/USDT 1h
2025-07-28 13:22:50,815 - advanced_data_fetcher - INFO - Fetching 4h data for T/USDT
2025-07-28 13:22:55,560 - advanced_data_fetcher - INFO - Cached data for T/USDT 4h
2025-07-28 13:22:55,560 - advanced_data_fetcher - INFO - Fetching 1d data for T/USDT
2025-07-28 13:22:56,665 - advanced_data_fetcher - INFO - Cached data for T/USDT 1d
2025-07-28 13:22:56,665 - advanced_data_fetcher - INFO - Fetching 1h data for TAO/USDT
2025-07-28 13:23:02,482 - advanced_data_fetcher - WARNING - Low quality data for TAO/USDT 1h: {'date_range_days': 472, 'min_required_days': 1460, 'actual_samples': 11351, 'expected_samples': 35040, 'completeness_ratio': 0.32394406392694064, 'gap_ratio': 0.0, 'avg_volume': np.float64(4520.64502729275), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:23:03,900 - advanced_data_fetcher - INFO - Cached data for TAO/USDT 1h
2025-07-28 13:23:03,900 - advanced_data_fetcher - INFO - Fetching 4h data for TAO/USDT
2025-07-28 13:23:05,365 - advanced_data_fetcher - WARNING - Low quality data for TAO/USDT 4h: {'date_range_days': 472, 'min_required_days': 1460, 'actual_samples': 2838, 'expected_samples': 8760, 'completeness_ratio': 0.323972602739726, 'gap_ratio': 0.0, 'avg_volume': np.float64(18081.00794693446), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:23:05,784 - advanced_data_fetcher - INFO - Cached data for TAO/USDT 4h
2025-07-28 13:23:05,785 - advanced_data_fetcher - INFO - Fetching 1d data for TAO/USDT
2025-07-28 13:23:06,265 - advanced_data_fetcher - WARNING - Low quality data for TAO/USDT 1d: {'date_range_days': 473, 'min_required_days': 1460, 'actual_samples': 474, 'expected_samples': 1460, 'completeness_ratio': 0.32465753424657534, 'gap_ratio': 0.0, 'avg_volume': np.float64(108257.17416329114), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:23:06,381 - advanced_data_fetcher - INFO - Cached data for TAO/USDT 1d
2025-07-28 13:23:06,381 - advanced_data_fetcher - INFO - Fetching 1h data for TFUEL/USDT
2025-07-28 13:23:30,150 - advanced_data_fetcher - INFO - Cached data for TFUEL/USDT 1h
2025-07-28 13:23:30,150 - advanced_data_fetcher - INFO - Fetching 4h data for TFUEL/USDT
2025-07-28 13:23:35,700 - advanced_data_fetcher - INFO - Cached data for TFUEL/USDT 4h
2025-07-28 13:23:35,700 - advanced_data_fetcher - INFO - Fetching 1d data for TFUEL/USDT
2025-07-28 13:23:37,292 - advanced_data_fetcher - INFO - Cached data for TFUEL/USDT 1d
2025-07-28 13:23:37,292 - advanced_data_fetcher - INFO - Fetching 1h data for THE/USDT
2025-07-28 13:23:40,330 - advanced_data_fetcher - WARNING - Low quality data for THE/USDT 1h: {'date_range_days': 243, 'min_required_days': 1460, 'actual_samples': 5833, 'expected_samples': 35040, 'completeness_ratio': 0.16646689497716896, 'gap_ratio': 0.0, 'avg_volume': np.float64(996018.8734099091), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:23:41,100 - advanced_data_fetcher - INFO - Cached data for THE/USDT 1h
2025-07-28 13:23:41,100 - advanced_data_fetcher - INFO - Fetching 4h data for THE/USDT
2025-07-28 13:23:42,050 - advanced_data_fetcher - WARNING - Low quality data for THE/USDT 4h: {'date_range_days': 243, 'min_required_days': 1460, 'actual_samples': 1459, 'expected_samples': 8760, 'completeness_ratio': 0.1665525114155251, 'gap_ratio': 0.0, 'avg_volume': np.float64(3982027.4767649076), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:23:42,300 - advanced_data_fetcher - INFO - Cached data for THE/USDT 4h
2025-07-28 13:23:42,300 - advanced_data_fetcher - INFO - Fetching 1d data for THE/USDT
2025-07-28 13:23:42,751 - advanced_data_fetcher - WARNING - Low quality data for THE/USDT 1d: {'date_range_days': 243, 'min_required_days': 1460, 'actual_samples': 244, 'expected_samples': 1460, 'completeness_ratio': 0.16712328767123288, 'gap_ratio': 0.0, 'avg_volume': np.float64(23810565.936885245), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:23:42,925 - advanced_data_fetcher - INFO - Cached data for THE/USDT 1d
2025-07-28 13:23:42,925 - advanced_data_fetcher - INFO - Fetching 1h data for THETA/USDT
2025-07-28 13:24:05,615 - advanced_data_fetcher - INFO - Cached data for THETA/USDT 1h
2025-07-28 13:24:05,625 - advanced_data_fetcher - INFO - Fetching 4h data for THETA/USDT
2025-07-28 13:24:11,131 - advanced_data_fetcher - INFO - Cached data for THETA/USDT 4h
2025-07-28 13:24:11,131 - advanced_data_fetcher - INFO - Fetching 1d data for THETA/USDT
2025-07-28 13:24:12,315 - advanced_data_fetcher - INFO - Cached data for THETA/USDT 1d
2025-07-28 13:24:12,315 - advanced_data_fetcher - INFO - Fetching 1h data for TIA/USDT
2025-07-28 13:24:20,147 - advanced_data_fetcher - WARNING - Low quality data for TIA/USDT 1h: {'date_range_days': 635, 'min_required_days': 1460, 'actual_samples': 15259, 'expected_samples': 35040, 'completeness_ratio': 0.43547374429223745, 'gap_ratio': 0.0, 'avg_volume': np.float64(326150.37393145025), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:24:22,031 - advanced_data_fetcher - INFO - Cached data for TIA/USDT 1h
2025-07-28 13:24:22,031 - advanced_data_fetcher - INFO - Fetching 4h data for TIA/USDT
2025-07-28 13:24:24,000 - advanced_data_fetcher - WARNING - Low quality data for TIA/USDT 4h: {'date_range_days': 635, 'min_required_days': 1460, 'actual_samples': 3815, 'expected_samples': 8760, 'completeness_ratio': 0.4355022831050228, 'gap_ratio': 0.0, 'avg_volume': np.float64(1304516.0342804717), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:24:24,550 - advanced_data_fetcher - INFO - Cached data for TIA/USDT 4h
2025-07-28 13:24:24,550 - advanced_data_fetcher - INFO - Fetching 1d data for TIA/USDT
2025-07-28 13:24:25,736 - advanced_data_fetcher - WARNING - Low quality data for TIA/USDT 1d: {'date_range_days': 636, 'min_required_days': 1460, 'actual_samples': 637, 'expected_samples': 1460, 'completeness_ratio': 0.4363013698630137, 'gap_ratio': 0.0, 'avg_volume': np.float64(7812760.864646781), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:24:25,850 - advanced_data_fetcher - INFO - Cached data for TIA/USDT 1d
2025-07-28 13:24:25,850 - advanced_data_fetcher - INFO - Fetching 1h data for TKO/USDT
2025-07-28 13:24:48,050 - advanced_data_fetcher - INFO - Cached data for TKO/USDT 1h
2025-07-28 13:24:48,050 - advanced_data_fetcher - INFO - Fetching 4h data for TKO/USDT
2025-07-28 13:24:53,880 - advanced_data_fetcher - INFO - Cached data for TKO/USDT 4h
2025-07-28 13:24:53,881 - advanced_data_fetcher - INFO - Fetching 1d data for TKO/USDT
2025-07-28 13:24:55,111 - advanced_data_fetcher - INFO - Cached data for TKO/USDT 1d
2025-07-28 13:24:55,112 - advanced_data_fetcher - INFO - Fetching 1h data for TLM/USDT
2025-07-28 13:25:21,969 - advanced_data_fetcher - INFO - Cached data for TLM/USDT 1h
2025-07-28 13:25:21,969 - advanced_data_fetcher - INFO - Fetching 4h data for TLM/USDT
2025-07-28 13:25:28,434 - advanced_data_fetcher - INFO - Cached data for TLM/USDT 4h
2025-07-28 13:25:28,434 - advanced_data_fetcher - INFO - Fetching 1d data for TLM/USDT
2025-07-28 13:25:29,651 - advanced_data_fetcher - INFO - Cached data for TLM/USDT 1d
2025-07-28 13:25:29,651 - advanced_data_fetcher - INFO - Fetching 1h data for TNSR/USDT
2025-07-28 13:25:36,218 - advanced_data_fetcher - WARNING - Low quality data for TNSR/USDT 1h: {'date_range_days': 475, 'min_required_days': 1460, 'actual_samples': 11420, 'expected_samples': 35040, 'completeness_ratio': 0.3259132420091324, 'gap_ratio': 0.0, 'avg_volume': np.float64(691704.3374255692), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:25:38,017 - advanced_data_fetcher - INFO - Cached data for TNSR/USDT 1h
2025-07-28 13:25:38,033 - advanced_data_fetcher - INFO - Fetching 4h data for TNSR/USDT
2025-07-28 13:25:39,500 - advanced_data_fetcher - WARNING - Low quality data for TNSR/USDT 4h: {'date_range_days': 475, 'min_required_days': 1460, 'actual_samples': 2856, 'expected_samples': 8760, 'completeness_ratio': 0.32602739726027397, 'gap_ratio': 0.0, 'avg_volume': np.float64(2765848.5761204483), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:25:39,900 - advanced_data_fetcher - INFO - Cached data for TNSR/USDT 4h
2025-07-28 13:25:39,900 - advanced_data_fetcher - INFO - Fetching 1d data for TNSR/USDT
2025-07-28 13:25:40,376 - advanced_data_fetcher - WARNING - Low quality data for TNSR/USDT 1d: {'date_range_days': 476, 'min_required_days': 1460, 'actual_samples': 477, 'expected_samples': 1460, 'completeness_ratio': 0.3267123287671233, 'gap_ratio': 0.0, 'avg_volume': np.float64(16560300.908595389), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:25:40,482 - advanced_data_fetcher - INFO - Cached data for TNSR/USDT 1d
2025-07-28 13:25:40,482 - advanced_data_fetcher - INFO - Fetching 1h data for TON/USDT
2025-07-28 13:25:44,869 - advanced_data_fetcher - WARNING - Low quality data for TON/USDT 1h: {'date_range_days': 354, 'min_required_days': 1460, 'actual_samples': 8497, 'expected_samples': 35040, 'completeness_ratio': 0.24249429223744293, 'gap_ratio': 0.0, 'avg_volume': np.float64(335683.6616570554), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:25:46,000 - advanced_data_fetcher - INFO - Cached data for TON/USDT 1h
2025-07-28 13:25:46,000 - advanced_data_fetcher - INFO - Fetching 4h data for TON/USDT
2025-07-28 13:25:47,497 - advanced_data_fetcher - WARNING - Low quality data for TON/USDT 4h: {'date_range_days': 354, 'min_required_days': 1460, 'actual_samples': 2125, 'expected_samples': 8760, 'completeness_ratio': 0.2425799086757991, 'gap_ratio': 0.0, 'avg_volume': np.float64(1342260.740282353), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:25:47,831 - advanced_data_fetcher - INFO - Cached data for TON/USDT 4h
2025-07-28 13:25:47,831 - advanced_data_fetcher - INFO - Fetching 1d data for TON/USDT
2025-07-28 13:25:48,300 - advanced_data_fetcher - WARNING - Low quality data for TON/USDT 1d: {'date_range_days': 354, 'min_required_days': 1460, 'actual_samples': 355, 'expected_samples': 1460, 'completeness_ratio': 0.24315068493150685, 'gap_ratio': 0.0, 'avg_volume': np.float64(8034659.360845071), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:25:48,400 - advanced_data_fetcher - INFO - Cached data for TON/USDT 1d
2025-07-28 13:25:48,400 - advanced_data_fetcher - INFO - Fetching 1h data for TRB/USDT
2025-07-28 13:26:38,705 - advanced_data_fetcher - INFO - Cached data for TRB/USDT 1h
2025-07-28 13:26:38,705 - advanced_data_fetcher - INFO - Fetching 4h data for TRB/USDT
2025-07-28 13:26:44,500 - advanced_data_fetcher - INFO - Cached data for TRB/USDT 4h
2025-07-28 13:26:44,501 - advanced_data_fetcher - INFO - Fetching 1d data for TRB/USDT
2025-07-28 13:26:45,704 - advanced_data_fetcher - INFO - Cached data for TRB/USDT 1d
2025-07-28 13:26:45,705 - advanced_data_fetcher - INFO - Fetching 1h data for TRU/USDT
2025-07-28 13:27:10,489 - advanced_data_fetcher - INFO - Cached data for TRU/USDT 1h
2025-07-28 13:27:10,489 - advanced_data_fetcher - INFO - Fetching 4h data for TRU/USDT
2025-07-28 13:27:16,327 - advanced_data_fetcher - INFO - Cached data for TRU/USDT 4h
2025-07-28 13:27:16,327 - advanced_data_fetcher - INFO - Fetching 1d data for TRU/USDT
2025-07-28 13:27:17,559 - advanced_data_fetcher - INFO - Cached data for TRU/USDT 1d
2025-07-28 13:27:17,560 - advanced_data_fetcher - INFO - Fetching 1h data for TRUMP/USDT
2025-07-28 13:27:20,317 - advanced_data_fetcher - WARNING - Low quality data for TRUMP/USDT 1h: {'date_range_days': 190, 'min_required_days': 1460, 'actual_samples': 4563, 'expected_samples': 35040, 'completeness_ratio': 0.13022260273972602, 'gap_ratio': 0.0, 'avg_volume': np.float64(661992.7224856454), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:27:20,952 - advanced_data_fetcher - INFO - Cached data for TRUMP/USDT 1h
2025-07-28 13:27:20,952 - advanced_data_fetcher - INFO - Fetching 4h data for TRUMP/USDT
2025-07-28 13:27:21,966 - advanced_data_fetcher - WARNING - Low quality data for TRUMP/USDT 4h: {'date_range_days': 190, 'min_required_days': 1460, 'actual_samples': 1141, 'expected_samples': 8760, 'completeness_ratio': 0.1302511415525114, 'gap_ratio': 0.0, 'avg_volume': np.float64(2647390.7035074495), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:27:22,145 - advanced_data_fetcher - INFO - Cached data for TRUMP/USDT 4h
2025-07-28 13:27:22,145 - advanced_data_fetcher - INFO - Fetching 1d data for TRUMP/USDT
2025-07-28 13:27:22,576 - advanced_data_fetcher - WARNING - Low quality data for TRUMP/USDT 1d: {'date_range_days': 190, 'min_required_days': 1460, 'actual_samples': 191, 'expected_samples': 1460, 'completeness_ratio': 0.13082191780821917, 'gap_ratio': 0.0, 'avg_volume': np.float64(15815040.799486909), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:27:22,659 - advanced_data_fetcher - INFO - Cached data for TRUMP/USDT 1d
2025-07-28 13:27:22,660 - advanced_data_fetcher - INFO - Fetching 1h data for TRX/USDT
2025-07-28 13:27:47,182 - advanced_data_fetcher - INFO - Cached data for TRX/USDT 1h
2025-07-28 13:27:47,182 - advanced_data_fetcher - INFO - Fetching 4h data for TRX/USDT
2025-07-28 13:27:52,894 - advanced_data_fetcher - INFO - Cached data for TRX/USDT 4h
2025-07-28 13:27:52,895 - advanced_data_fetcher - INFO - Fetching 1d data for TRX/USDT
2025-07-28 13:27:54,112 - advanced_data_fetcher - INFO - Cached data for TRX/USDT 1d
2025-07-28 13:27:54,112 - advanced_data_fetcher - INFO - Fetching 1h data for TST/USDT
2025-07-28 13:27:56,482 - advanced_data_fetcher - WARNING - Low quality data for TST/USDT 1h: {'date_range_days': 168, 'min_required_days': 1460, 'actual_samples': 4056, 'expected_samples': 35040, 'completeness_ratio': 0.11575342465753424, 'gap_ratio': 0.0, 'avg_volume': np.float64(12242507.641543392), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:27:57,083 - advanced_data_fetcher - INFO - Cached data for TST/USDT 1h
2025-07-28 13:27:57,083 - advanced_data_fetcher - INFO - Fetching 4h data for TST/USDT
2025-07-28 13:27:58,012 - advanced_data_fetcher - WARNING - Low quality data for TST/USDT 4h: {'date_range_days': 169, 'min_required_days': 1460, 'actual_samples': 1015, 'expected_samples': 8760, 'completeness_ratio': 0.1158675799086758, 'gap_ratio': 0.0, 'avg_volume': np.float64(48921784.2306404), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:27:58,190 - advanced_data_fetcher - INFO - Cached data for TST/USDT 4h
2025-07-28 13:27:58,190 - advanced_data_fetcher - INFO - Fetching 1d data for TST/USDT
2025-07-28 13:27:58,637 - advanced_data_fetcher - WARNING - Low quality data for TST/USDT 1d: {'date_range_days': 169, 'min_required_days': 1460, 'actual_samples': 170, 'expected_samples': 1460, 'completeness_ratio': 0.11643835616438356, 'gap_ratio': 0.0, 'avg_volume': np.float64(292091829.37705886), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:27:58,714 - advanced_data_fetcher - INFO - Cached data for TST/USDT 1d
2025-07-28 13:27:58,715 - advanced_data_fetcher - INFO - Fetching 1h data for TURBO/USDT
2025-07-28 13:28:02,832 - advanced_data_fetcher - WARNING - Low quality data for TURBO/USDT 1h: {'date_range_days': 315, 'min_required_days': 1460, 'actual_samples': 7561, 'expected_samples': 35040, 'completeness_ratio': 0.21578196347031964, 'gap_ratio': 0.0, 'avg_volume': np.float64(146014345.2593572), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:28:04,015 - advanced_data_fetcher - INFO - Cached data for TURBO/USDT 1h
2025-07-28 13:28:04,015 - advanced_data_fetcher - INFO - Fetching 4h data for TURBO/USDT
2025-07-28 13:28:05,011 - advanced_data_fetcher - WARNING - Low quality data for TURBO/USDT 4h: {'date_range_days': 315, 'min_required_days': 1460, 'actual_samples': 1891, 'expected_samples': 8760, 'completeness_ratio': 0.2158675799086758, 'gap_ratio': 0.0, 'avg_volume': np.float64(583825734.799577), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:28:05,337 - advanced_data_fetcher - INFO - Cached data for TURBO/USDT 4h
2025-07-28 13:28:05,337 - advanced_data_fetcher - INFO - Fetching 1d data for TURBO/USDT
2025-07-28 13:28:05,787 - advanced_data_fetcher - WARNING - Low quality data for TURBO/USDT 1d: {'date_range_days': 315, 'min_required_days': 1460, 'actual_samples': 316, 'expected_samples': 1460, 'completeness_ratio': 0.21643835616438356, 'gap_ratio': 0.0, 'avg_volume': np.float64(3493716659.829114), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:28:05,876 - advanced_data_fetcher - INFO - Cached data for TURBO/USDT 1d
2025-07-28 13:28:05,877 - advanced_data_fetcher - INFO - Fetching 1h data for TUT/USDT
2025-07-28 13:28:07,329 - advanced_data_fetcher - WARNING - Low quality data for TUT/USDT 1h: {'date_range_days': 122, 'min_required_days': 1460, 'actual_samples': 2942, 'expected_samples': 35040, 'completeness_ratio': 0.08396118721461188, 'gap_ratio': 0.0, 'avg_volume': np.float64(9829718.577158395), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:28:07,725 - advanced_data_fetcher - INFO - Cached data for TUT/USDT 1h
2025-07-28 13:28:07,726 - advanced_data_fetcher - INFO - Fetching 4h data for TUT/USDT
2025-07-28 13:28:08,202 - advanced_data_fetcher - WARNING - Low quality data for TUT/USDT 4h: {'date_range_days': 122, 'min_required_days': 1460, 'actual_samples': 736, 'expected_samples': 8760, 'completeness_ratio': 0.08401826484018265, 'gap_ratio': 0.0, 'avg_volume': np.float64(39292163.11684783), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:28:08,350 - advanced_data_fetcher - INFO - Cached data for TUT/USDT 4h
2025-07-28 13:28:08,350 - advanced_data_fetcher - INFO - Fetching 1d data for TUT/USDT
2025-07-28 13:28:08,768 - advanced_data_fetcher - WARNING - Low quality data for TUT/USDT 1d: {'date_range_days': 123, 'min_required_days': 1460, 'actual_samples': 124, 'expected_samples': 1460, 'completeness_ratio': 0.08493150684931507, 'gap_ratio': 0.0, 'avg_volume': np.float64(233218000.43548387), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:28:08,815 - advanced_data_fetcher - INFO - Cached data for TUT/USDT 1d
2025-07-28 13:28:08,815 - advanced_data_fetcher - INFO - Fetching 1h data for TWT/USDT
2025-07-28 13:28:31,069 - advanced_data_fetcher - INFO - Cached data for TWT/USDT 1h
2025-07-28 13:28:31,070 - advanced_data_fetcher - INFO - Fetching 4h data for TWT/USDT
2025-07-28 13:28:37,748 - advanced_data_fetcher - INFO - Cached data for TWT/USDT 4h
2025-07-28 13:28:37,748 - advanced_data_fetcher - INFO - Fetching 1d data for TWT/USDT
2025-07-28 13:28:38,909 - advanced_data_fetcher - INFO - Cached data for TWT/USDT 1d
2025-07-28 13:28:38,910 - advanced_data_fetcher - INFO - Fetching 1h data for UMA/USDT
2025-07-28 13:29:01,973 - advanced_data_fetcher - INFO - Cached data for UMA/USDT 1h
2025-07-28 13:29:01,974 - advanced_data_fetcher - INFO - Fetching 4h data for UMA/USDT
2025-07-28 13:29:07,712 - advanced_data_fetcher - INFO - Cached data for UMA/USDT 4h
2025-07-28 13:29:07,712 - advanced_data_fetcher - INFO - Fetching 1d data for UMA/USDT
2025-07-28 13:29:09,810 - advanced_data_fetcher - INFO - Cached data for UMA/USDT 1d
2025-07-28 13:29:09,811 - advanced_data_fetcher - INFO - Fetching 1h data for UNI/USDT
2025-07-28 13:29:34,317 - advanced_data_fetcher - INFO - Cached data for UNI/USDT 1h
2025-07-28 13:29:34,317 - advanced_data_fetcher - INFO - Fetching 4h data for UNI/USDT
2025-07-28 13:29:43,497 - advanced_data_fetcher - INFO - Cached data for UNI/USDT 4h
2025-07-28 13:29:43,498 - advanced_data_fetcher - INFO - Fetching 1d data for UNI/USDT
2025-07-28 13:29:44,701 - advanced_data_fetcher - INFO - Cached data for UNI/USDT 1d
2025-07-28 13:29:44,701 - advanced_data_fetcher - INFO - Fetching 1h data for USD1/USDT
2025-07-28 13:29:45,651 - advanced_data_fetcher - WARNING - Low quality data for USD1/USDT 1h: {'date_range_days': 66, 'min_required_days': 1460, 'actual_samples': 1607, 'expected_samples': 35040, 'completeness_ratio': 0.04586187214611872, 'gap_ratio': 0.0, 'avg_volume': np.float64(414629.852520224), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:29:45,874 - advanced_data_fetcher - INFO - Cached data for USD1/USDT 1h
2025-07-28 13:29:45,874 - advanced_data_fetcher - INFO - Fetching 4h data for USD1/USDT
2025-07-28 13:29:46,312 - advanced_data_fetcher - WARNING - Low quality data for USD1/USDT 4h: {'date_range_days': 66, 'min_required_days': 1460, 'actual_samples': 402, 'expected_samples': 8760, 'completeness_ratio': 0.04589041095890411, 'gap_ratio': 0.0, 'avg_volume': np.float64(1657487.9925373134), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:29:46,404 - advanced_data_fetcher - INFO - Cached data for USD1/USDT 4h
2025-07-28 13:29:46,405 - advanced_data_fetcher - INFO - Fetching 1d data for USD1/USDT
2025-07-28 13:29:46,950 - advanced_data_fetcher - WARNING - Low quality data for USD1/USDT 1d: {'date_range_days': 67, 'min_required_days': 1460, 'actual_samples': 68, 'expected_samples': 1460, 'completeness_ratio': 0.04657534246575343, 'gap_ratio': 0.0, 'avg_volume': np.float64(9798679.014705881), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:29:47,022 - advanced_data_fetcher - INFO - Cached data for USD1/USDT 1d
2025-07-28 13:29:47,023 - advanced_data_fetcher - INFO - Fetching 1h data for USDP/USDT
2025-07-28 13:30:04,276 - advanced_data_fetcher - WARNING - Low quality data for USDP/USDT 1h: {'date_range_days': 1417, 'min_required_days': 1460, 'actual_samples': 30017, 'expected_samples': 35040, 'completeness_ratio': 0.8566495433789955, 'gap_ratio': 0.11753638100837865, 'avg_volume': np.float64(136596.32496851785), 'quality_score': np.float64(0.5), 'is_high_quality': np.False_}
2025-07-28 13:30:08,231 - advanced_data_fetcher - INFO - Cached data for USDP/USDT 1h
2025-07-28 13:30:08,232 - advanced_data_fetcher - INFO - Fetching 4h data for USDP/USDT
2025-07-28 13:30:12,187 - advanced_data_fetcher - WARNING - Low quality data for USDP/USDT 4h: {'date_range_days': 1417, 'min_required_days': 1460, 'actual_samples': 7506, 'expected_samples': 8760, 'completeness_ratio': 0.8568493150684932, 'gap_ratio': 0.11735653809971779, 'avg_volume': np.float64(546257.9118811617), 'quality_score': np.float64(0.5), 'is_high_quality': np.False_}
2025-07-28 13:30:13,224 - advanced_data_fetcher - INFO - Cached data for USDP/USDT 4h
2025-07-28 13:30:13,225 - advanced_data_fetcher - INFO - Fetching 1d data for USDP/USDT
2025-07-28 13:30:14,149 - advanced_data_fetcher - WARNING - Low quality data for USDP/USDT 1d: {'date_range_days': 1417, 'min_required_days': 1460, 'actual_samples': 1253, 'expected_samples': 1460, 'completeness_ratio': 0.8582191780821918, 'gap_ratio': 0.11636107193229901, 'avg_volume': np.float64(3272315.950981644), 'quality_score': np.float64(0.5), 'is_high_quality': np.False_}
2025-07-28 13:30:14,357 - advanced_data_fetcher - INFO - Cached data for USDP/USDT 1d
2025-07-28 13:30:14,358 - advanced_data_fetcher - INFO - Fetching 1h data for USTC/USDT
2025-07-28 13:30:24,809 - advanced_data_fetcher - WARNING - Low quality data for USTC/USDT 1h: {'date_range_days': 871, 'min_required_days': 1460, 'actual_samples': 20906, 'expected_samples': 35040, 'completeness_ratio': 0.5966324200913242, 'gap_ratio': 4.783087004352609e-05, 'avg_volume': np.float64(16340163.888118243), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:30:27,603 - advanced_data_fetcher - INFO - Cached data for USTC/USDT 1h
2025-07-28 13:30:27,603 - advanced_data_fetcher - INFO - Fetching 4h data for USTC/USDT
2025-07-28 13:30:30,578 - advanced_data_fetcher - WARNING - Low quality data for USTC/USDT 4h: {'date_range_days': 871, 'min_required_days': 1460, 'actual_samples': 5227, 'expected_samples': 8760, 'completeness_ratio': 0.596689497716895, 'gap_ratio': 0.0, 'avg_volume': np.float64(65354403.337478474), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:30:31,398 - advanced_data_fetcher - INFO - Cached data for USTC/USDT 4h
2025-07-28 13:30:31,399 - advanced_data_fetcher - INFO - Fetching 1d data for USTC/USDT
2025-07-28 13:30:31,880 - advanced_data_fetcher - WARNING - Low quality data for USTC/USDT 1d: {'date_range_days': 871, 'min_required_days': 1460, 'actual_samples': 872, 'expected_samples': 1460, 'completeness_ratio': 0.5972602739726027, 'gap_ratio': 0.0, 'avg_volume': np.float64(391751681.4736239), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:30:32,047 - advanced_data_fetcher - INFO - Cached data for USTC/USDT 1d
2025-07-28 13:30:32,048 - advanced_data_fetcher - INFO - Fetching 1h data for USUAL/USDT
2025-07-28 13:30:35,054 - advanced_data_fetcher - WARNING - Low quality data for USUAL/USDT 1h: {'date_range_days': 251, 'min_required_days': 1460, 'actual_samples': 5999, 'expected_samples': 35040, 'completeness_ratio': 0.17120433789954337, 'gap_ratio': 0.004315352697095436, 'avg_volume': np.float64(4542530.228538089), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:30:35,957 - advanced_data_fetcher - INFO - Cached data for USUAL/USDT 1h
2025-07-28 13:30:35,957 - advanced_data_fetcher - INFO - Fetching 4h data for USUAL/USDT
2025-07-28 13:30:37,112 - advanced_data_fetcher - WARNING - Low quality data for USUAL/USDT 4h: {'date_range_days': 251, 'min_required_days': 1460, 'actual_samples': 1502, 'expected_samples': 8760, 'completeness_ratio': 0.17146118721461187, 'gap_ratio': 0.0033178500331785005, 'avg_volume': np.float64(18142902.02463382), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:30:37,390 - advanced_data_fetcher - INFO - Cached data for USUAL/USDT 4h
2025-07-28 13:30:37,390 - advanced_data_fetcher - INFO - Fetching 1d data for USUAL/USDT
2025-07-28 13:30:37,889 - advanced_data_fetcher - WARNING - Low quality data for USUAL/USDT 1d: {'date_range_days': 251, 'min_required_days': 1460, 'actual_samples': 252, 'expected_samples': 1460, 'completeness_ratio': 0.1726027397260274, 'gap_ratio': 0.0, 'avg_volume': np.float64(108137455.71825397), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:30:37,989 - advanced_data_fetcher - INFO - Cached data for USUAL/USDT 1d
2025-07-28 13:30:37,989 - advanced_data_fetcher - INFO - Fetching 1h data for UTK/USDT
2025-07-28 13:31:00,935 - advanced_data_fetcher - INFO - Cached data for UTK/USDT 1h
2025-07-28 13:31:00,936 - advanced_data_fetcher - INFO - Fetching 4h data for UTK/USDT
2025-07-28 13:31:06,581 - advanced_data_fetcher - INFO - Cached data for UTK/USDT 4h
2025-07-28 13:31:06,581 - advanced_data_fetcher - INFO - Fetching 1d data for UTK/USDT
2025-07-28 13:31:07,768 - advanced_data_fetcher - INFO - Cached data for UTK/USDT 1d
2025-07-28 13:31:07,768 - advanced_data_fetcher - INFO - Fetching 1h data for VANA/USDT
2025-07-28 13:31:10,693 - advanced_data_fetcher - WARNING - Low quality data for VANA/USDT 1h: {'date_range_days': 223, 'min_required_days': 1460, 'actual_samples': 5376, 'expected_samples': 35040, 'completeness_ratio': 0.15342465753424658, 'gap_ratio': 0.0, 'avg_volume': np.float64(76390.07899925596), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:31:11,347 - advanced_data_fetcher - INFO - Cached data for VANA/USDT 1h
2025-07-28 13:31:11,347 - advanced_data_fetcher - INFO - Fetching 4h data for VANA/USDT
2025-07-28 13:31:12,364 - advanced_data_fetcher - WARNING - Low quality data for VANA/USDT 4h: {'date_range_days': 224, 'min_required_days': 1460, 'actual_samples': 1345, 'expected_samples': 8760, 'completeness_ratio': 0.15353881278538814, 'gap_ratio': 0.0, 'avg_volume': np.float64(305333.13360594795), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:31:12,609 - advanced_data_fetcher - INFO - Cached data for VANA/USDT 4h
2025-07-28 13:31:12,609 - advanced_data_fetcher - INFO - Fetching 1d data for VANA/USDT
2025-07-28 13:31:13,055 - advanced_data_fetcher - WARNING - Low quality data for VANA/USDT 1d: {'date_range_days': 224, 'min_required_days': 1460, 'actual_samples': 225, 'expected_samples': 1460, 'completeness_ratio': 0.1541095890410959, 'gap_ratio': 0.0, 'avg_volume': np.float64(1825213.620888889), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:31:13,132 - advanced_data_fetcher - INFO - Cached data for VANA/USDT 1d
2025-07-28 13:31:13,132 - advanced_data_fetcher - INFO - Fetching 1h data for VANRY/USDT
2025-07-28 13:31:20,702 - advanced_data_fetcher - WARNING - Low quality data for VANRY/USDT 1h: {'date_range_days': 605, 'min_required_days': 1460, 'actual_samples': 14523, 'expected_samples': 35040, 'completeness_ratio': 0.4144691780821918, 'gap_ratio': 0.0, 'avg_volume': np.float64(4061714.4136886317), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:31:22,550 - advanced_data_fetcher - INFO - Cached data for VANRY/USDT 1h
2025-07-28 13:31:22,551 - advanced_data_fetcher - INFO - Fetching 4h data for VANRY/USDT
2025-07-28 13:31:24,526 - advanced_data_fetcher - WARNING - Low quality data for VANRY/USDT 4h: {'date_range_days': 605, 'min_required_days': 1460, 'actual_samples': 3631, 'expected_samples': 8760, 'completeness_ratio': 0.41449771689497716, 'gap_ratio': 0.0, 'avg_volume': np.float64(16245742.057009088), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:31:25,015 - advanced_data_fetcher - INFO - Cached data for VANRY/USDT 4h
2025-07-28 13:31:25,016 - advanced_data_fetcher - INFO - Fetching 1d data for VANRY/USDT
2025-07-28 13:31:25,507 - advanced_data_fetcher - WARNING - Low quality data for VANRY/USDT 1d: {'date_range_days': 605, 'min_required_days': 1460, 'actual_samples': 606, 'expected_samples': 1460, 'completeness_ratio': 0.41506849315068495, 'gap_ratio': 0.0, 'avg_volume': np.float64(97340411.5660066), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:31:25,631 - advanced_data_fetcher - INFO - Cached data for VANRY/USDT 1d
2025-07-28 13:31:25,632 - advanced_data_fetcher - INFO - Fetching 1h data for VELODROME/USDT
2025-07-28 13:31:28,530 - advanced_data_fetcher - WARNING - Low quality data for VELODROME/USDT 1h: {'date_range_days': 226, 'min_required_days': 1460, 'actual_samples': 5445, 'expected_samples': 35040, 'completeness_ratio': 0.15539383561643835, 'gap_ratio': 0.0, 'avg_volume': np.float64(2274073.045050505), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:31:29,237 - advanced_data_fetcher - INFO - Cached data for VELODROME/USDT 1h
2025-07-28 13:31:29,237 - advanced_data_fetcher - INFO - Fetching 4h data for VELODROME/USDT
2025-07-28 13:31:30,233 - advanced_data_fetcher - WARNING - Low quality data for VELODROME/USDT 4h: {'date_range_days': 226, 'min_required_days': 1460, 'actual_samples': 1362, 'expected_samples': 8760, 'completeness_ratio': 0.15547945205479452, 'gap_ratio': 0.0, 'avg_volume': np.float64(9091283.208737152), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:31:30,479 - advanced_data_fetcher - INFO - Cached data for VELODROME/USDT 4h
2025-07-28 13:31:30,480 - advanced_data_fetcher - INFO - Fetching 1d data for VELODROME/USDT
2025-07-28 13:31:30,949 - advanced_data_fetcher - WARNING - Low quality data for VELODROME/USDT 1d: {'date_range_days': 227, 'min_required_days': 1460, 'actual_samples': 228, 'expected_samples': 1460, 'completeness_ratio': 0.15616438356164383, 'gap_ratio': 0.0, 'avg_volume': np.float64(54308454.957456134), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:31:31,021 - advanced_data_fetcher - INFO - Cached data for VELODROME/USDT 1d
2025-07-28 13:31:31,022 - advanced_data_fetcher - INFO - Fetching 1h data for VET/USDT
2025-07-28 13:31:53,881 - advanced_data_fetcher - INFO - Cached data for VET/USDT 1h
2025-07-28 13:31:53,882 - advanced_data_fetcher - INFO - Fetching 4h data for VET/USDT
2025-07-28 13:31:59,533 - advanced_data_fetcher - INFO - Cached data for VET/USDT 4h
2025-07-28 13:31:59,533 - advanced_data_fetcher - INFO - Fetching 1d data for VET/USDT
2025-07-28 13:32:00,776 - advanced_data_fetcher - INFO - Cached data for VET/USDT 1d
2025-07-28 13:32:00,777 - advanced_data_fetcher - INFO - Fetching 1h data for VIC/USDT
2025-07-28 13:32:08,186 - advanced_data_fetcher - WARNING - Low quality data for VIC/USDT 1h: {'date_range_days': 612, 'min_required_days': 1460, 'actual_samples': 14691, 'expected_samples': 35040, 'completeness_ratio': 0.41926369863013696, 'gap_ratio': 0.0, 'avg_volume': np.float64(299761.5652195221), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:32:10,249 - advanced_data_fetcher - INFO - Cached data for VIC/USDT 1h
2025-07-28 13:32:10,250 - advanced_data_fetcher - INFO - Fetching 4h data for VIC/USDT
2025-07-28 13:32:12,237 - advanced_data_fetcher - WARNING - Low quality data for VIC/USDT 4h: {'date_range_days': 612, 'min_required_days': 1460, 'actual_samples': 3673, 'expected_samples': 8760, 'completeness_ratio': 0.4192922374429224, 'gap_ratio': 0.0, 'avg_volume': np.float64(1198964.648690444), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:32:12,789 - advanced_data_fetcher - INFO - Cached data for VIC/USDT 4h
2025-07-28 13:32:12,790 - advanced_data_fetcher - INFO - Fetching 1d data for VIC/USDT
2025-07-28 13:32:13,282 - advanced_data_fetcher - WARNING - Low quality data for VIC/USDT 1d: {'date_range_days': 612, 'min_required_days': 1460, 'actual_samples': 613, 'expected_samples': 1460, 'completeness_ratio': 0.41986301369863016, 'gap_ratio': 0.0, 'avg_volume': np.float64(7184008.408874389), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:32:13,446 - advanced_data_fetcher - INFO - Cached data for VIC/USDT 1d
2025-07-28 13:32:13,446 - advanced_data_fetcher - INFO - Fetching 1h data for VIRTUAL/USDT
2025-07-28 13:32:14,907 - advanced_data_fetcher - WARNING - Low quality data for VIRTUAL/USDT 1h: {'date_range_days': 107, 'min_required_days': 1460, 'actual_samples': 2589, 'expected_samples': 35040, 'completeness_ratio': 0.07388698630136986, 'gap_ratio': 0.0, 'avg_volume': np.float64(1102376.8558903052), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:32:15,279 - advanced_data_fetcher - INFO - Cached data for VIRTUAL/USDT 1h
2025-07-28 13:32:15,279 - advanced_data_fetcher - INFO - Fetching 4h data for VIRTUAL/USDT
2025-07-28 13:32:15,771 - advanced_data_fetcher - WARNING - Low quality data for VIRTUAL/USDT 4h: {'date_range_days': 107, 'min_required_days': 1460, 'actual_samples': 648, 'expected_samples': 8760, 'completeness_ratio': 0.07397260273972603, 'gap_ratio': 0.0, 'avg_volume': np.float64(4404403.8342592595), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:32:15,915 - advanced_data_fetcher - INFO - Cached data for VIRTUAL/USDT 4h
2025-07-28 13:32:15,916 - advanced_data_fetcher - INFO - Fetching 1d data for VIRTUAL/USDT
2025-07-28 13:32:16,365 - advanced_data_fetcher - WARNING - Low quality data for VIRTUAL/USDT 1d: {'date_range_days': 108, 'min_required_days': 1460, 'actual_samples': 109, 'expected_samples': 1460, 'completeness_ratio': 0.07465753424657534, 'gap_ratio': 0.0, 'avg_volume': np.float64(26183978.75779816), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:32:16,446 - advanced_data_fetcher - INFO - Cached data for VIRTUAL/USDT 1d
2025-07-28 13:32:16,446 - advanced_data_fetcher - INFO - Fetching 1h data for VOXEL/USDT
2025-07-28 13:32:38,046 - advanced_data_fetcher - INFO - Cached data for VOXEL/USDT 1h
2025-07-28 13:32:38,047 - advanced_data_fetcher - INFO - Fetching 4h data for VOXEL/USDT
2025-07-28 13:32:44,300 - advanced_data_fetcher - INFO - Cached data for VOXEL/USDT 4h
2025-07-28 13:32:44,301 - advanced_data_fetcher - INFO - Fetching 1d data for VOXEL/USDT
2025-07-28 13:32:45,438 - advanced_data_fetcher - INFO - Cached data for VOXEL/USDT 1d
2025-07-28 13:32:45,439 - advanced_data_fetcher - INFO - Fetching 1h data for VTHO/USDT
2025-07-28 13:33:08,900 - advanced_data_fetcher - INFO - Cached data for VTHO/USDT 1h
2025-07-28 13:33:08,901 - advanced_data_fetcher - INFO - Fetching 4h data for VTHO/USDT
2025-07-28 13:33:14,482 - advanced_data_fetcher - INFO - Cached data for VTHO/USDT 4h
2025-07-28 13:33:14,482 - advanced_data_fetcher - INFO - Fetching 1d data for VTHO/USDT
2025-07-28 13:33:15,644 - advanced_data_fetcher - INFO - Cached data for VTHO/USDT 1d
2025-07-28 13:33:15,644 - advanced_data_fetcher - INFO - Fetching 1h data for W/USDT
2025-07-28 13:33:21,403 - advanced_data_fetcher - WARNING - Low quality data for W/USDT 1h: {'date_range_days': 480, 'min_required_days': 1460, 'actual_samples': 11543, 'expected_samples': 35040, 'completeness_ratio': 0.32942351598173514, 'gap_ratio': 0.0, 'avg_volume': np.float64(2621047.1369747897), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:33:22,897 - advanced_data_fetcher - INFO - Cached data for W/USDT 1h
2025-07-28 13:33:22,898 - advanced_data_fetcher - INFO - Fetching 4h data for W/USDT
2025-07-28 13:33:24,379 - advanced_data_fetcher - WARNING - Low quality data for W/USDT 4h: {'date_range_days': 480, 'min_required_days': 1460, 'actual_samples': 2886, 'expected_samples': 8760, 'completeness_ratio': 0.32945205479452055, 'gap_ratio': 0.0, 'avg_volume': np.float64(10483280.354158003), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:33:24,743 - advanced_data_fetcher - INFO - Cached data for W/USDT 4h
2025-07-28 13:33:24,744 - advanced_data_fetcher - INFO - Fetching 1d data for W/USDT
2025-07-28 13:33:25,215 - advanced_data_fetcher - WARNING - Low quality data for W/USDT 1d: {'date_range_days': 481, 'min_required_days': 1460, 'actual_samples': 482, 'expected_samples': 1460, 'completeness_ratio': 0.33013698630136984, 'gap_ratio': 0.0, 'avg_volume': np.float64(62769184.85912864), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:33:25,313 - advanced_data_fetcher - INFO - Cached data for W/USDT 1d
2025-07-28 13:33:25,313 - advanced_data_fetcher - INFO - Fetching 1h data for WAN/USDT
2025-07-28 13:33:47,059 - advanced_data_fetcher - INFO - Cached data for WAN/USDT 1h
2025-07-28 13:33:47,059 - advanced_data_fetcher - INFO - Fetching 4h data for WAN/USDT
2025-07-28 13:33:52,730 - advanced_data_fetcher - INFO - Cached data for WAN/USDT 4h
2025-07-28 13:33:52,730 - advanced_data_fetcher - INFO - Fetching 1d data for WAN/USDT
2025-07-28 13:33:53,886 - advanced_data_fetcher - INFO - Cached data for WAN/USDT 1d
2025-07-28 13:33:53,887 - advanced_data_fetcher - INFO - Fetching 1h data for WAXP/USDT
2025-07-28 13:34:15,741 - advanced_data_fetcher - INFO - Cached data for WAXP/USDT 1h
2025-07-28 13:34:15,742 - advanced_data_fetcher - INFO - Fetching 4h data for WAXP/USDT
2025-07-28 13:34:21,507 - advanced_data_fetcher - INFO - Cached data for WAXP/USDT 4h
2025-07-28 13:34:21,507 - advanced_data_fetcher - INFO - Fetching 1d data for WAXP/USDT
2025-07-28 13:34:22,656 - advanced_data_fetcher - INFO - Cached data for WAXP/USDT 1d
2025-07-28 13:34:22,656 - advanced_data_fetcher - INFO - Fetching 1h data for WBETH/USDT
2025-07-28 13:34:31,542 - advanced_data_fetcher - WARNING - Low quality data for WBETH/USDT 1h: {'date_range_days': 740, 'min_required_days': 1460, 'actual_samples': 17765, 'expected_samples': 35040, 'completeness_ratio': 0.50699200913242, 'gap_ratio': 0.0, 'avg_volume': np.float64(30.551862527441596), 'quality_score': np.float64(0.2), 'is_high_quality': np.False_}
2025-07-28 13:34:33,732 - advanced_data_fetcher - INFO - Cached data for WBETH/USDT 1h
2025-07-28 13:34:33,732 - advanced_data_fetcher - INFO - Fetching 4h data for WBETH/USDT
2025-07-28 13:34:36,245 - advanced_data_fetcher - WARNING - Low quality data for WBETH/USDT 4h: {'date_range_days': 740, 'min_required_days': 1460, 'actual_samples': 4442, 'expected_samples': 8760, 'completeness_ratio': 0.5070776255707763, 'gap_ratio': 0.0, 'avg_volume': np.float64(122.18681625393967), 'quality_score': np.float64(0.2), 'is_high_quality': np.False_}
2025-07-28 13:34:36,805 - advanced_data_fetcher - INFO - Cached data for WBETH/USDT 4h
2025-07-28 13:34:36,805 - advanced_data_fetcher - INFO - Fetching 1d data for WBETH/USDT
2025-07-28 13:34:37,311 - advanced_data_fetcher - WARNING - Low quality data for WBETH/USDT 1d: {'date_range_days': 740, 'min_required_days': 1460, 'actual_samples': 741, 'expected_samples': 1460, 'completeness_ratio': 0.5075342465753425, 'gap_ratio': 0.0, 'avg_volume': np.float64(732.4613195681511), 'quality_score': np.float64(0.2), 'is_high_quality': np.False_}
2025-07-28 13:34:37,482 - advanced_data_fetcher - INFO - Cached data for WBETH/USDT 1d
2025-07-28 13:34:37,483 - advanced_data_fetcher - INFO - Fetching 1h data for WBTC/USDT
2025-07-28 13:34:47,463 - advanced_data_fetcher - WARNING - Low quality data for WBTC/USDT 1h: {'date_range_days': 822, 'min_required_days': 1460, 'actual_samples': 19729, 'expected_samples': 35040, 'completeness_ratio': 0.5630422374429224, 'gap_ratio': 0.0, 'avg_volume': np.float64(3.0367021901768974), 'quality_score': np.float64(0.2), 'is_high_quality': np.False_}
2025-07-28 13:34:49,940 - advanced_data_fetcher - INFO - Cached data for WBTC/USDT 1h
2025-07-28 13:34:49,940 - advanced_data_fetcher - INFO - Fetching 4h data for WBTC/USDT
2025-07-28 13:34:52,674 - advanced_data_fetcher - WARNING - Low quality data for WBTC/USDT 4h: {'date_range_days': 822, 'min_required_days': 1460, 'actual_samples': 4933, 'expected_samples': 8760, 'completeness_ratio': 0.5631278538812785, 'gap_ratio': 0.0, 'avg_volume': np.float64(12.144961992702209), 'quality_score': np.float64(0.2), 'is_high_quality': np.False_}
2025-07-28 13:34:53,372 - advanced_data_fetcher - INFO - Cached data for WBTC/USDT 4h
2025-07-28 13:34:53,372 - advanced_data_fetcher - INFO - Fetching 1d data for WBTC/USDT
2025-07-28 13:34:53,896 - advanced_data_fetcher - WARNING - Low quality data for WBTC/USDT 1d: {'date_range_days': 822, 'min_required_days': 1460, 'actual_samples': 823, 'expected_samples': 1460, 'completeness_ratio': 0.5636986301369863, 'gap_ratio': 0.0, 'avg_volume': np.float64(72.79598725394898), 'quality_score': np.float64(0.2), 'is_high_quality': np.False_}
2025-07-28 13:34:54,064 - advanced_data_fetcher - INFO - Cached data for WBTC/USDT 1d
2025-07-28 13:34:54,065 - advanced_data_fetcher - INFO - Fetching 1h data for WCT/USDT
2025-07-28 13:34:55,527 - advanced_data_fetcher - WARNING - Low quality data for WCT/USDT 1h: {'date_range_days': 103, 'min_required_days': 1460, 'actual_samples': 2496, 'expected_samples': 35040, 'completeness_ratio': 0.07123287671232877, 'gap_ratio': 0.0, 'avg_volume': np.float64(1885519.3197115385), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:34:55,864 - advanced_data_fetcher - INFO - Cached data for WCT/USDT 1h
2025-07-28 13:34:55,865 - advanced_data_fetcher - INFO - Fetching 4h data for WCT/USDT
2025-07-28 13:34:56,350 - advanced_data_fetcher - WARNING - Low quality data for WCT/USDT 4h: {'date_range_days': 104, 'min_required_days': 1460, 'actual_samples': 625, 'expected_samples': 8760, 'completeness_ratio': 0.07134703196347032, 'gap_ratio': 0.0, 'avg_volume': np.float64(7530009.9552), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:34:56,486 - advanced_data_fetcher - INFO - Cached data for WCT/USDT 4h
2025-07-28 13:34:56,486 - advanced_data_fetcher - INFO - Fetching 1d data for WCT/USDT
2025-07-28 13:34:56,916 - advanced_data_fetcher - WARNING - Low quality data for WCT/USDT 1d: {'date_range_days': 104, 'min_required_days': 1460, 'actual_samples': 105, 'expected_samples': 1460, 'completeness_ratio': 0.07191780821917808, 'gap_ratio': 0.0, 'avg_volume': np.float64(44821487.82857142), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:34:56,992 - advanced_data_fetcher - INFO - Cached data for WCT/USDT 1d
2025-07-28 13:34:56,992 - advanced_data_fetcher - INFO - Fetching 1h data for WIF/USDT
2025-07-28 13:35:03,420 - advanced_data_fetcher - WARNING - Low quality data for WIF/USDT 1h: {'date_range_days': 509, 'min_required_days': 1460, 'actual_samples': 12237, 'expected_samples': 35040, 'completeness_ratio': 0.3492294520547945, 'gap_ratio': 0.0, 'avg_volume': np.float64(3608756.1709013646), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:35:04,929 - advanced_data_fetcher - INFO - Cached data for WIF/USDT 1h
2025-07-28 13:35:04,929 - advanced_data_fetcher - INFO - Fetching 4h data for WIF/USDT
2025-07-28 13:35:06,874 - advanced_data_fetcher - WARNING - Low quality data for WIF/USDT 4h: {'date_range_days': 509, 'min_required_days': 1460, 'actual_samples': 3060, 'expected_samples': 8760, 'completeness_ratio': 0.3493150684931507, 'gap_ratio': 0.0, 'avg_volume': np.float64(14431486.780209152), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:35:07,274 - advanced_data_fetcher - INFO - Cached data for WIF/USDT 4h
2025-07-28 13:35:07,274 - advanced_data_fetcher - INFO - Fetching 1d data for WIF/USDT
2025-07-28 13:35:07,737 - advanced_data_fetcher - WARNING - Low quality data for WIF/USDT 1d: {'date_range_days': 510, 'min_required_days': 1460, 'actual_samples': 511, 'expected_samples': 1460, 'completeness_ratio': 0.35, 'gap_ratio': 0.0, 'avg_volume': np.float64(86419470.73863015), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:35:07,832 - advanced_data_fetcher - INFO - Cached data for WIF/USDT 1d
2025-07-28 13:35:07,833 - advanced_data_fetcher - INFO - Fetching 1h data for WIN/USDT
2025-07-28 13:35:30,367 - advanced_data_fetcher - INFO - Cached data for WIN/USDT 1h
2025-07-28 13:35:30,368 - advanced_data_fetcher - INFO - Fetching 4h data for WIN/USDT
2025-07-28 13:35:36,111 - advanced_data_fetcher - INFO - Cached data for WIN/USDT 4h
2025-07-28 13:35:36,112 - advanced_data_fetcher - INFO - Fetching 1d data for WIN/USDT
2025-07-28 13:35:37,280 - advanced_data_fetcher - INFO - Cached data for WIN/USDT 1d
2025-07-28 13:35:37,280 - advanced_data_fetcher - INFO - Fetching 1h data for WLD/USDT
2025-07-28 13:35:46,823 - advanced_data_fetcher - WARNING - Low quality data for WLD/USDT 1h: {'date_range_days': 735, 'min_required_days': 1460, 'actual_samples': 17642, 'expected_samples': 35040, 'completeness_ratio': 0.5034817351598173, 'gap_ratio': 0.0, 'avg_volume': np.float64(1258922.2503060878), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:35:49,312 - advanced_data_fetcher - INFO - Cached data for WLD/USDT 1h
2025-07-28 13:35:49,313 - advanced_data_fetcher - INFO - Fetching 4h data for WLD/USDT
2025-07-28 13:35:51,780 - advanced_data_fetcher - WARNING - Low quality data for WLD/USDT 4h: {'date_range_days': 735, 'min_required_days': 1460, 'actual_samples': 4411, 'expected_samples': 8760, 'completeness_ratio': 0.5035388127853881, 'gap_ratio': 0.0, 'avg_volume': np.float64(5035118.221809113), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:35:52,326 - advanced_data_fetcher - INFO - Cached data for WLD/USDT 4h
2025-07-28 13:35:52,327 - advanced_data_fetcher - INFO - Fetching 1d data for WLD/USDT
2025-07-28 13:35:52,821 - advanced_data_fetcher - WARNING - Low quality data for WLD/USDT 1d: {'date_range_days': 735, 'min_required_days': 1460, 'actual_samples': 736, 'expected_samples': 1460, 'completeness_ratio': 0.5041095890410959, 'gap_ratio': 0.0, 'avg_volume': np.float64(30176503.40543478), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:35:52,939 - advanced_data_fetcher - INFO - Cached data for WLD/USDT 1d
2025-07-28 13:35:52,939 - advanced_data_fetcher - INFO - Fetching 1h data for WOO/USDT
2025-07-28 13:36:12,103 - advanced_data_fetcher - INFO - Cached data for WOO/USDT 1h
2025-07-28 13:36:12,103 - advanced_data_fetcher - INFO - Fetching 4h data for WOO/USDT
2025-07-28 13:36:16,974 - advanced_data_fetcher - INFO - Cached data for WOO/USDT 4h
2025-07-28 13:36:16,975 - advanced_data_fetcher - INFO - Fetching 1d data for WOO/USDT
2025-07-28 13:36:18,081 - advanced_data_fetcher - INFO - Cached data for WOO/USDT 1d
2025-07-28 13:36:18,081 - advanced_data_fetcher - INFO - Fetching 1h data for XAI/USDT
2025-07-28 13:36:25,042 - advanced_data_fetcher - WARNING - Low quality data for XAI/USDT 1h: {'date_range_days': 566, 'min_required_days': 1460, 'actual_samples': 13585, 'expected_samples': 35040, 'completeness_ratio': 0.3876997716894977, 'gap_ratio': 0.0, 'avg_volume': np.float64(2207657.3964961357), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:36:26,647 - advanced_data_fetcher - INFO - Cached data for XAI/USDT 1h
2025-07-28 13:36:26,647 - advanced_data_fetcher - INFO - Fetching 4h data for XAI/USDT
2025-07-28 13:36:28,829 - advanced_data_fetcher - WARNING - Low quality data for XAI/USDT 4h: {'date_range_days': 566, 'min_required_days': 1460, 'actual_samples': 3397, 'expected_samples': 8760, 'completeness_ratio': 0.3877853881278539, 'gap_ratio': 0.0, 'avg_volume': np.float64(8828680.651987046), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:36:29,267 - advanced_data_fetcher - INFO - Cached data for XAI/USDT 4h
2025-07-28 13:36:29,267 - advanced_data_fetcher - INFO - Fetching 1d data for XAI/USDT
2025-07-28 13:36:29,755 - advanced_data_fetcher - WARNING - Low quality data for XAI/USDT 1d: {'date_range_days': 566, 'min_required_days': 1460, 'actual_samples': 567, 'expected_samples': 1460, 'completeness_ratio': 0.38835616438356163, 'gap_ratio': 0.0, 'avg_volume': np.float64(52894229.585185185), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:36:29,883 - advanced_data_fetcher - INFO - Cached data for XAI/USDT 1d
2025-07-28 13:36:29,884 - advanced_data_fetcher - INFO - Fetching 1h data for XEC/USDT
2025-07-28 13:36:52,136 - advanced_data_fetcher - INFO - Cached data for XEC/USDT 1h
2025-07-28 13:36:52,137 - advanced_data_fetcher - INFO - Fetching 4h data for XEC/USDT
2025-07-28 13:36:57,644 - advanced_data_fetcher - INFO - Cached data for XEC/USDT 4h
2025-07-28 13:36:57,645 - advanced_data_fetcher - INFO - Fetching 1d data for XEC/USDT
2025-07-28 13:36:58,835 - advanced_data_fetcher - INFO - Cached data for XEC/USDT 1d
2025-07-28 13:36:58,835 - advanced_data_fetcher - INFO - Fetching 1h data for XLM/USDT
2025-07-28 13:37:20,764 - advanced_data_fetcher - INFO - Cached data for XLM/USDT 1h
2025-07-28 13:37:20,764 - advanced_data_fetcher - INFO - Fetching 4h data for XLM/USDT
2025-07-28 13:37:26,232 - advanced_data_fetcher - INFO - Cached data for XLM/USDT 4h
2025-07-28 13:37:26,233 - advanced_data_fetcher - INFO - Fetching 1d data for XLM/USDT
2025-07-28 13:37:27,470 - advanced_data_fetcher - INFO - Cached data for XLM/USDT 1d
2025-07-28 13:37:27,470 - advanced_data_fetcher - INFO - Fetching 1h data for XNO/USDT
2025-07-28 13:37:46,814 - advanced_data_fetcher - INFO - Cached data for XNO/USDT 1h
2025-07-28 13:37:46,815 - advanced_data_fetcher - INFO - Fetching 4h data for XNO/USDT
2025-07-28 13:37:51,664 - advanced_data_fetcher - INFO - Cached data for XNO/USDT 4h
2025-07-28 13:37:51,664 - advanced_data_fetcher - INFO - Fetching 1d data for XNO/USDT
2025-07-28 13:37:52,779 - advanced_data_fetcher - INFO - Cached data for XNO/USDT 1d
2025-07-28 13:37:52,779 - advanced_data_fetcher - INFO - Fetching 1h data for XRP/USDT
2025-07-28 13:38:14,923 - advanced_data_fetcher - INFO - Cached data for XRP/USDT 1h
2025-07-28 13:38:14,925 - advanced_data_fetcher - INFO - Fetching 4h data for XRP/USDT
2025-07-28 13:38:20,441 - advanced_data_fetcher - INFO - Cached data for XRP/USDT 4h
2025-07-28 13:38:20,442 - advanced_data_fetcher - INFO - Fetching 1d data for XRP/USDT
2025-07-28 13:38:21,695 - advanced_data_fetcher - INFO - Cached data for XRP/USDT 1d
2025-07-28 13:38:21,696 - advanced_data_fetcher - INFO - Fetching 1h data for XTZ/USDT
2025-07-28 13:38:43,584 - advanced_data_fetcher - INFO - Cached data for XTZ/USDT 1h
2025-07-28 13:38:43,585 - advanced_data_fetcher - INFO - Fetching 4h data for XTZ/USDT
2025-07-28 13:38:49,073 - advanced_data_fetcher - INFO - Cached data for XTZ/USDT 4h
2025-07-28 13:38:49,074 - advanced_data_fetcher - INFO - Fetching 1d data for XTZ/USDT
2025-07-28 13:38:50,226 - advanced_data_fetcher - INFO - Cached data for XTZ/USDT 1d
2025-07-28 13:38:50,226 - advanced_data_fetcher - INFO - Fetching 1h data for XUSD/USDT
2025-07-28 13:38:52,052 - advanced_data_fetcher - WARNING - Low quality data for XUSD/USDT 1h: {'date_range_days': 131, 'min_required_days': 1460, 'actual_samples': 3147, 'expected_samples': 35040, 'completeness_ratio': 0.08981164383561643, 'gap_ratio': 0.0, 'avg_volume': np.float64(258023.01652367334), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:38:52,533 - advanced_data_fetcher - INFO - Cached data for XUSD/USDT 1h
2025-07-28 13:38:52,533 - advanced_data_fetcher - INFO - Fetching 4h data for XUSD/USDT
2025-07-28 13:38:53,031 - advanced_data_fetcher - WARNING - Low quality data for XUSD/USDT 4h: {'date_range_days': 131, 'min_required_days': 1460, 'actual_samples': 787, 'expected_samples': 8760, 'completeness_ratio': 0.08984018264840182, 'gap_ratio': 0.0, 'avg_volume': np.float64(1031764.209656925), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:38:53,173 - advanced_data_fetcher - INFO - Cached data for XUSD/USDT 4h
2025-07-28 13:38:53,175 - advanced_data_fetcher - INFO - Fetching 1d data for XUSD/USDT
2025-07-28 13:38:53,604 - advanced_data_fetcher - WARNING - Low quality data for XUSD/USDT 1d: {'date_range_days': 131, 'min_required_days': 1460, 'actual_samples': 132, 'expected_samples': 1460, 'completeness_ratio': 0.09041095890410959, 'gap_ratio': 0.0, 'avg_volume': np.float64(6151503.28030303), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:38:53,691 - advanced_data_fetcher - INFO - Cached data for XUSD/USDT 1d
2025-07-28 13:38:53,692 - advanced_data_fetcher - INFO - Fetching 1h data for XVG/USDT
2025-07-28 13:39:15,741 - advanced_data_fetcher - INFO - Cached data for XVG/USDT 1h
2025-07-28 13:39:15,742 - advanced_data_fetcher - INFO - Fetching 4h data for XVG/USDT
2025-07-28 13:39:21,266 - advanced_data_fetcher - INFO - Cached data for XVG/USDT 4h
2025-07-28 13:39:21,267 - advanced_data_fetcher - INFO - Fetching 1d data for XVG/USDT
2025-07-28 13:39:22,468 - advanced_data_fetcher - INFO - Cached data for XVG/USDT 1d
2025-07-28 13:39:22,469 - advanced_data_fetcher - INFO - Fetching 1h data for XVS/USDT
2025-07-28 13:39:45,819 - advanced_data_fetcher - INFO - Cached data for XVS/USDT 1h
2025-07-28 13:39:45,820 - advanced_data_fetcher - INFO - Fetching 4h data for XVS/USDT
2025-07-28 13:39:51,769 - advanced_data_fetcher - INFO - Cached data for XVS/USDT 4h
2025-07-28 13:39:51,770 - advanced_data_fetcher - INFO - Fetching 1d data for XVS/USDT
2025-07-28 13:39:52,953 - advanced_data_fetcher - INFO - Cached data for XVS/USDT 1d
2025-07-28 13:39:52,955 - advanced_data_fetcher - INFO - Fetching 1h data for YFI/USDT
2025-07-28 13:40:14,962 - advanced_data_fetcher - INFO - Cached data for YFI/USDT 1h
2025-07-28 13:40:14,963 - advanced_data_fetcher - INFO - Fetching 4h data for YFI/USDT
2025-07-28 13:40:20,466 - advanced_data_fetcher - INFO - Cached data for YFI/USDT 4h
2025-07-28 13:40:20,466 - advanced_data_fetcher - INFO - Fetching 1d data for YFI/USDT
2025-07-28 13:40:21,660 - advanced_data_fetcher - INFO - Cached data for YFI/USDT 1d
2025-07-28 13:40:21,660 - advanced_data_fetcher - INFO - Fetching 1h data for YGG/USDT
2025-07-28 13:40:42,783 - advanced_data_fetcher - INFO - Cached data for YGG/USDT 1h
2025-07-28 13:40:42,784 - advanced_data_fetcher - INFO - Fetching 4h data for YGG/USDT
2025-07-28 13:40:48,249 - advanced_data_fetcher - INFO - Cached data for YGG/USDT 4h
2025-07-28 13:40:48,250 - advanced_data_fetcher - INFO - Fetching 1d data for YGG/USDT
2025-07-28 13:40:49,441 - advanced_data_fetcher - INFO - Cached data for YGG/USDT 1d
2025-07-28 13:40:49,442 - advanced_data_fetcher - INFO - Fetching 1h data for ZEC/USDT
2025-07-28 13:41:11,485 - advanced_data_fetcher - INFO - Cached data for ZEC/USDT 1h
2025-07-28 13:41:11,486 - advanced_data_fetcher - INFO - Fetching 4h data for ZEC/USDT
2025-07-28 13:41:16,951 - advanced_data_fetcher - INFO - Cached data for ZEC/USDT 4h
2025-07-28 13:41:16,951 - advanced_data_fetcher - INFO - Fetching 1d data for ZEC/USDT
2025-07-28 13:41:18,169 - advanced_data_fetcher - INFO - Cached data for ZEC/USDT 1d
2025-07-28 13:41:18,170 - advanced_data_fetcher - INFO - Fetching 1h data for ZEN/USDT
2025-07-28 13:41:40,000 - advanced_data_fetcher - INFO - Cached data for ZEN/USDT 1h
2025-07-28 13:41:40,000 - advanced_data_fetcher - INFO - Fetching 4h data for ZEN/USDT
2025-07-28 13:41:45,494 - advanced_data_fetcher - INFO - Cached data for ZEN/USDT 4h
2025-07-28 13:41:45,494 - advanced_data_fetcher - INFO - Fetching 1d data for ZEN/USDT
2025-07-28 13:41:46,678 - advanced_data_fetcher - INFO - Cached data for ZEN/USDT 1d
2025-07-28 13:41:46,678 - advanced_data_fetcher - INFO - Fetching 1h data for ZIL/USDT
2025-07-28 13:42:08,910 - advanced_data_fetcher - INFO - Cached data for ZIL/USDT 1h
2025-07-28 13:42:08,911 - advanced_data_fetcher - INFO - Fetching 4h data for ZIL/USDT
2025-07-28 13:42:14,586 - advanced_data_fetcher - INFO - Cached data for ZIL/USDT 4h
2025-07-28 13:42:14,587 - advanced_data_fetcher - INFO - Fetching 1d data for ZIL/USDT
2025-07-28 13:42:15,740 - advanced_data_fetcher - INFO - Cached data for ZIL/USDT 1d
2025-07-28 13:42:15,740 - advanced_data_fetcher - INFO - Fetching 1h data for ZK/USDT
2025-07-28 13:42:20,595 - advanced_data_fetcher - WARNING - Low quality data for ZK/USDT 1h: {'date_range_days': 406, 'min_required_days': 1460, 'actual_samples': 9745, 'expected_samples': 35040, 'completeness_ratio': 0.2781107305936073, 'gap_ratio': 0.0, 'avg_volume': np.float64(5429710.148794253), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:42:21,830 - advanced_data_fetcher - INFO - Cached data for ZK/USDT 1h
2025-07-28 13:42:21,830 - advanced_data_fetcher - INFO - Fetching 4h data for ZK/USDT
2025-07-28 13:42:23,263 - advanced_data_fetcher - WARNING - Low quality data for ZK/USDT 4h: {'date_range_days': 406, 'min_required_days': 1460, 'actual_samples': 2437, 'expected_samples': 8760, 'completeness_ratio': 0.2781963470319635, 'gap_ratio': 0.0, 'avg_volume': np.float64(21712157.152482558), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:42:23,616 - advanced_data_fetcher - INFO - Cached data for ZK/USDT 4h
2025-07-28 13:42:23,616 - advanced_data_fetcher - INFO - Fetching 1d data for ZK/USDT
2025-07-28 13:42:24,067 - advanced_data_fetcher - WARNING - Low quality data for ZK/USDT 1d: {'date_range_days': 406, 'min_required_days': 1460, 'actual_samples': 407, 'expected_samples': 1460, 'completeness_ratio': 0.2787671232876712, 'gap_ratio': 0.0, 'avg_volume': np.float64(130006208.79754302), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:42:24,151 - advanced_data_fetcher - INFO - Cached data for ZK/USDT 1d
2025-07-28 13:42:24,152 - advanced_data_fetcher - INFO - Fetching 1h data for ZRO/USDT
2025-07-28 13:42:29,080 - advanced_data_fetcher - WARNING - Low quality data for ZRO/USDT 1h: {'date_range_days': 402, 'min_required_days': 1460, 'actual_samples': 9670, 'expected_samples': 35040, 'completeness_ratio': 0.2759703196347032, 'gap_ratio': 0.0, 'avg_volume': np.float64(272733.13479110657), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:42:30,193 - advanced_data_fetcher - INFO - Cached data for ZRO/USDT 1h
2025-07-28 13:42:30,194 - advanced_data_fetcher - INFO - Fetching 4h data for ZRO/USDT
2025-07-28 13:42:31,625 - advanced_data_fetcher - WARNING - Low quality data for ZRO/USDT 4h: {'date_range_days': 402, 'min_required_days': 1460, 'actual_samples': 2418, 'expected_samples': 8760, 'completeness_ratio': 0.276027397260274, 'gap_ratio': 0.0, 'avg_volume': np.float64(1090706.953444996), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:42:31,937 - advanced_data_fetcher - INFO - Cached data for ZRO/USDT 4h
2025-07-28 13:42:31,937 - advanced_data_fetcher - INFO - Fetching 1d data for ZRO/USDT
2025-07-28 13:42:32,391 - advanced_data_fetcher - WARNING - Low quality data for ZRO/USDT 1d: {'date_range_days': 403, 'min_required_days': 1460, 'actual_samples': 404, 'expected_samples': 1460, 'completeness_ratio': 0.27671232876712326, 'gap_ratio': 0.0, 'avg_volume': np.float64(6528043.102549505), 'quality_score': np.float64(0.4), 'is_high_quality': np.False_}
2025-07-28 13:42:32,475 - advanced_data_fetcher - INFO - Cached data for ZRO/USDT 1d
2025-07-28 13:42:32,475 - advanced_data_fetcher - INFO - Fetching 1h data for ZRX/USDT
2025-07-28 13:42:55,805 - advanced_data_fetcher - INFO - Cached data for ZRX/USDT 1h
2025-07-28 13:42:55,806 - advanced_data_fetcher - INFO - Fetching 4h data for ZRX/USDT
2025-07-28 13:43:01,297 - advanced_data_fetcher - INFO - Cached data for ZRX/USDT 4h
2025-07-28 13:43:01,298 - advanced_data_fetcher - INFO - Fetching 1d data for ZRX/USDT
2025-07-28 13:43:02,505 - advanced_data_fetcher - INFO - Cached data for ZRX/USDT 1d
2025-07-28 14:39:10,239 - advanced_data_fetcher - WARNING - Failed to fetch Binance data for LOKA/USDT: HTTPSConnectionPool(host='api.binance.com', port=443): Max retries exceeded with url: /api/v3/klines?symbol=LOKAUSDT&interval=1h&startTime=1753671600000&endTime=1753695451025&limit=1000 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x00000270EE4F91D0>, 'Connection to api.binance.com timed out. (connect timeout=30)'))
2025-07-28 14:39:10,241 - advanced_data_fetcher - INFO - Fetching 4h data for LOKA/USDT
